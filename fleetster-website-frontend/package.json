{"name": "fleetster-website-frontend", "description": "fleetster Website Frontend", "version": "2.0.118", "private": true, "author": "<PERSON><PERSON> <<EMAIL>>", "repository": "https://gitlab.com/fleetster/fleetster-website-frontend.git", "license": "UNLICENSED", "scripts": {"fetchResources": "buildScripts/fetchResources", "dev": "WEBSITE_URL=http://localhost:3000 yarn fetchResources && next dev", "build": "yarn fetchResources && next build", "lint": "next lint && eslint buildScripts", "test": "NODE_ENV=test yarn fetchResources && jest --coverage", "serve": "http-server out/ -p 80", "serve:dev": "WEBSITE_URL=http://localhost:3000 http-server out/ -p 3000", "start": "./node_modules/.bin/next start -p 80", "start:dev": "WEBSITE_URL=http://localhost:3000 ./node_modules/.bin/next start -p 3000"}, "dependencies": {"@blueprintjs/core": "^5.10.3", "@blueprintjs/datetime2": "^2.3.5", "@blueprintjs/select": "^5.1.5", "@sanity/block-content-to-react": "^3.0.0", "@sanity/client": "^6.20.1", "@sanity/image-url": "1.0.2", "dotenv": "^17.2.1", "i18n-iso-countries": "^7.11.2", "libphonenumber-js": "^1.11.4", "lodash": "^4.17.21", "moment": "^2.29.4", "next": "^14.2.4", "next-seo": "^6.5.0", "normalize.css": "^8.0.1", "qs": "^6.12.1", "react": "18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@babel/plugin-proposal-private-methods": "^7.18.6", "@svgr/webpack": "^8.1.0", "@swc/core": "^1.6.5", "@swc/jest": "^0.2.36", "@types/qs": "^6.9.15", "aws-sdk": "^2.1650.0", "babel-jest": "^29.7.0", "eslint": "^8.57.0", "eslint-config-next": "^14.2.4", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-react": "^7.34.3", "fs-extra": "^11.2.0", "groq": "^3.48.1", "http-server": "^14.1.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-fetch-mock": "^3.0.3", "jsdom": "^24.1.0", "new-url-loader": "^0.1.1", "rc": "^1.2.8", "react-test-renderer": "^18.3.1", "sitemap": "^7.1.1"}, "resolutions": {"react": "18.3.1", "react-dom": "^18.3.1", "lodash": "^4.17.21", "cliui": "^8.0.1"}}