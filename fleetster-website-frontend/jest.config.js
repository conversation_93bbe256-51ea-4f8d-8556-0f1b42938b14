// Todo replace babel-jest with @swc/jest and next/jest

module.exports = {
    testEnvironmentOptions: {
        testURL: 'http://localhost'
    },
    testMatch:                ['<rootDir>/src/**/*.test.{js,jsx}', '<rootDir>/buildScripts/**/*.test.{js,jsx}', '<rootDir>/scripts/**/*.test.{js,jsx}'],
    modulePathIgnorePatterns: ['<rootDir>/fleetster_modules/', 'npm-cache', '.npm', '.cache/yarn'],
    testPathIgnorePatterns:   ['<rootDir>/node_modules'],
    collectCoverageFrom:      [
        '<rootDir>/src/**/*.{js,jsx}',
        '<rootDir>/buildScripts/**/*.{js,jsx}',
        '<rootDir>/scripts/**/*.{js,jsx}',
        '!<rootDir>/scripts/**/(globalSetup|setupJest|mediaFileMock).js',
        '!<rootDir>/src/content/sections/index.js'
    ],
    coverageThreshold: {
        global: {
            statements: 97,
            branches:   91,
            functions:  98
        }
    },
    transform: {
        '.*\\.jsx?$': 'babel-jest',

        '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|flf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga|scss|css)$': '<rootDir>/scripts/mediaFileMock.js'
    },
    transformIgnorePatterns: ['node_modules/(?!react-native)/'],
    haste:                   {
        retainAllFiles: true
    },
    resetModules:       true,
    testEnvironment:    'jsdom',
    setupFilesAfterEnv: ['<rootDir>/scripts/setupJest.js'],
    globalSetup:        '<rootDir>/scripts/globalSetupJest.js'
};
