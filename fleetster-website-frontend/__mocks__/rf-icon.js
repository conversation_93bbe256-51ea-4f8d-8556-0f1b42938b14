import React from 'react';
import PropTypes from 'prop-types';

export function FleetsterIcon({icon, iconSize = 20, classNames}) {
    return (
        <span className={`bp3-icon${classNames ? ` ${ classNames}` : ''}`}>
            <div src={`/v4/svgs/${icon}.svg`} width={`${iconSize}px`} height={iconSize}/>
        </span>
    );
}

FleetsterIcon.propTypes = {
    icon:       PropTypes.string,
    iconSize:   PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    classNames: PropTypes.string
};
