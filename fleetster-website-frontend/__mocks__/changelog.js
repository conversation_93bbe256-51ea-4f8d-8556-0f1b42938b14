/* eslint-disable */
const {changelogUrlMap} = require('../buildScripts/downloadSanityStudioData/constants');

const defaultChangelogs = {
    routes: {
        'CHANGELOG_INDEX_URL/q4-2021': {
            content: [{
                content: [{
                    _key:     '42539dd8a013',
                    _type:    'block',
                    children: [{
                        _key:  'becac38ba3dd',
                        _type: 'span',
                        marks: [],
                        text:  'Let us see if this works Alex -> This is the german page'
                    }],
                    markDefs: [],
                    style:    'normal'
                }],
                language:       'de',
                releaseDate:    '2021-12-22',
                releaseVersion: '3.99',
                tags:           [],
                title:          'Welcome to the fleetster changelog',
                quarter:        4,
                year:           2021,
                pageSlug:       'CHANGELOG_INDEX_URL/q4-2021'
            }, {
                content: [{
                    _key:     '609b5ef651e6',
                    _type:    'block',
                    children: [{_key: '150a6a147c2b', _type: 'span', marks: [], text: 'asdasdaddaddada'}],
                    markDefs: [],
                    style:    'normal'
                }],
                language:       'de',
                releaseDate:    '2021-12-06',
                releaseVersion: '999',
                title:          'Infinity',
                quarter:        4,
                year:           2021,
                pageSlug:       'CHANGELOG_INDEX_URL/q4-2021'
            }, {
                content: [{
                    _key:     '16bfb4db4e2e',
                    _type:    'block',
                    children: [{_key: 'c8909871a0fd', _type: 'span', marks: [], text: 'adsadsadsa'}],
                    markDefs: [],
                    style:    'normal'
                }],
                language:       'de',
                releaseDate:    '2021-12-05',
                releaseVersion: '666',
                title:          'Even more',
                quarter:        4,
                year:           2021,
                pageSlug:       'CHANGELOG_INDEX_URL/q4-2021'
            }, {
                content: [{
                    _key:     '0412cfbc36d9',
                    _type:    'block',
                    children: [{_key: '21847eb29944', _type: 'span', marks: [], text: 'asdasdasd'}],
                    markDefs: [],
                    style:    'normal'
                }],
                language:       'de',
                releaseDate:    '2021-12-03',
                releaseVersion: '1213',
                title:          'Something more',
                quarter:        4,
                year:           2021,
                pageSlug:       'CHANGELOG_INDEX_URL/q4-2021'
            }, {
                content: [{
                    _key:     'b546b1dd476c',
                    _type:    'block',
                    children: [{_key: '3a98bf2cb9e8', _type: 'span', marks: [], text: 'asdasdasdda'}],
                    markDefs: [],
                    style:    'normal'
                }],
                language:       'de',
                releaseDate:    '2021-12-02',
                releaseVersion: '123',
                title:          'Something else',
                quarter:        4,
                year:           2021,
                pageSlug:       'CHANGELOG_INDEX_URL/q4-2021'
            }, {
                content: [{
                    _key:     'afa9e4661959',
                    _type:    'block',
                    children: [{_key: '0f2f131e7fc7', _type: 'span', marks: [], text: 'asdasdasd'}],
                    markDefs: [],
                    style:    'normal'
                }],
                language:       'de',
                releaseDate:    '2021-12-01',
                releaseVersion: '3.845845',
                title:          'Something',
                quarter:        4,
                year:           2021,
                pageSlug:       'CHANGELOG_INDEX_URL/q4-2021'
            }, {
                content: [{
                    _key:     'f9acd3003b49',
                    _type:    'block',
                    children: [{_key: '063c5689d9af', _type: 'span', marks: [], text: 'asdasd'}],
                    markDefs: [],
                    style:    'normal'
                }],
                language:       'de',
                releaseDate:    '2021-11-03',
                releaseVersion: '777',
                title:          'Last one',
                quarter:        4,
                year:           2021,
                pageSlug:       'CHANGELOG_INDEX_URL/q4-2021'
            }, {
                content: [{
                    _key:     '86b299795f1b',
                    _type:    'block',
                    children: [{
                        _key:  'b2ee087ec302',
                        _type: 'span',
                        marks: [],
                        text:  'Yeahnasfksjfadkfdsadad'
                    }],
                    markDefs: [],
                    style:    'normal'
                }],
                language:       'de',
                releaseDate:    '2021-10-01',
                releaseVersion: '123123',
                title:          'Really the last one',
                quarter:        4,
                year:           2021,
                pageSlug:       'CHANGELOG_INDEX_URL/q4-2021'
            }], _updatedAt: '2021-03-03T14:48:14Z'
        },
        'CHANGELOG_INDEX_URL/q1-2021': {
            content: [{
                content: [{
                    _key:     '77486f50a85f',
                    _type:    'block',
                    children: [{
                        _key:  '34fba533e310',
                        _type: 'span',
                        marks: [],
                        text:  'Maecenas quis urna ornare, sollicitudin erat ut, dignissim nulla. Mauris aliquet tincidunt luctus. Vestibulum pretium tincidunt nunc, at bibendum urna auctor id. Nullam eget sapien nec lacus semper tincidunt ut ac diam. Aenean placerat urna sed massa sollicitudin lacinia. Praesent vel dui sit amet dui consequat sagittis nec id neque. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse scelerisque urna id leo aliquet semper. Vivamus condimentum elit vel dui commodo, a rhoncus diam blandit. Aenean ac quam fringilla, ultrices nunc at, luctus tellus. Etiam eu vehicula ex.'
                    }],
                    markDefs: [],
                    style:    'normal'
                }],
                image: {
                    _type: 'image',
                    asset: {
                        _ref:  'image-fcb623afa1a4f63bdf759291028089458ae9484d-385x685-jpg',
                        _type: 'reference'
                    }
                },
                imageAlt:       'Mockups',
                language:       'de',
                releaseDate:    '2021-02-17',
                releaseVersion: '3.68',
                tags:           [{
                    _key:  '6031889b897f',
                    _type: 'tag',
                    color: {
                        _type: 'color',
                        alpha: 1,
                        hex:   '#24a3e3',
                        hsl:   {_type: 'hslaColor', a: 1, h: 200, l: 0.5156, s: 0.7732},
                        hsv:   {_type: 'hsvaColor', a: 1, h: 200, s: 0.8414, v: 0.8901},
                        rgb:   {_type: 'rgbaColor', a: 1, b: 227, g: 163, r: 46}
                    },
                    label: 'These'
                }, {
                    _key:  '8092b699ebea',
                    _type: 'tag',
                    color: {
                        _type: 'color',
                        alpha: 1,
                        hex:   '#e32491',
                        hsl:   {
                            _type: 'hslaColor',
                            a:     1,
                            h:     325.747572815534,
                            l:     0.5155,
                            s:     0.7731000000000001
                        },
                        hsv: {
                            _type: 'hsvaColor',
                            a:     1,
                            h:     325.747572815534,
                            s:     0.8416601694962386,
                            v:     0.89006695
                        },
                        rgb: {_type: 'rgbaColor', a: 1, b: 145, g: 36, r: 227}
                    },
                    label: 'colors'
                }, {
                    _key:  '42ecb28f8548',
                    _type: 'tag',
                    color: {
                        _type: 'color',
                        alpha: 1,
                        hex:   '#e37d24',
                        hsl:   {
                            _type: 'hslaColor',
                            a:     1,
                            h:     27.96116504854369,
                            l:     0.5155,
                            s:     0.7731000000000001
                        },
                        hsv: {
                            _type: 'hsvaColor',
                            a:     1,
                            h:     27.96116504854369,
                            s:     0.8416601694962386,
                            v:     0.89006695
                        },
                        rgb: {_type: 'rgbaColor', a: 1, b: 36, g: 125, r: 227}
                    },
                    label: 'are'
                }, {
                    _key:  'a066ef632848',
                    _type: 'tag',
                    color: {
                        _type: 'color',
                        alpha: 1,
                        hex:   '#7324e3',
                        hsl:   {
                            _type: 'hslaColor',
                            a:     1,
                            h:     264.9320388349514,
                            l:     0.5155,
                            s:     0.7731000000000001
                        },
                        hsv: {
                            _type: 'hsvaColor',
                            a:     1,
                            h:     264.9320388349514,
                            s:     0.8416601694962386,
                            v:     0.89006695
                        },
                        rgb: {_type: 'rgbaColor', a: 1, b: 227, g: 36, r: 115}
                    },
                    label: 'so'
                }, {
                    _key:  'ceae9f6822bd',
                    _type: 'tag',
                    color: {
                        _type: 'color',
                        alpha: 1,
                        hex:   '#e32464',
                        hsl:   {
                            _type: 'hslaColor',
                            a:     1,
                            h:     339.7281553398058,
                            l:     0.5155,
                            s:     0.7731000000000001
                        },
                        hsv: {
                            _type: 'hsvaColor',
                            a:     1,
                            h:     339.7281553398058,
                            s:     0.8416601694962386,
                            v:     0.89006695
                        },
                        rgb: {_type: 'rgbaColor', a: 1, b: 100, g: 36, r: 227}
                    },
                    label: 'pretty! '
                }],
                title:    'Ingrid is testing this MR at long last',
                quarter:  1,
                year:     2021,
                pageSlug: 'CHANGELOG_INDEX_URL/q1-2021'
            }, {
                content: [{
                    _key:     'e7517a5317a9',
                    _type:    'block',
                    children: [{
                        _key:  'c3ef133cb459',
                        _type: 'span',
                        marks: [],
                        text:  'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed vel justo porttitor, pharetra ipsum at, vehicula diam. In leo velit, ullamcorper non varius id, auctor id ligula. Phasellus eget finibus magna, id viverra magna. Cras gravida sapien sed efficitur pellentesque. Suspendisse vestibulum quis ligula a ornare. Vestibulum nunc ligula, ultrices sed consectetur pharetra, convallis lacinia quam. Donec sodales id massa vitae lacinia. Nullam eros ante, mollis nec malesuada at, venenatis cursus magna. Suspendisse imperdiet dui id felis rhoncus vestibulum id quis tortor. Pellentesque eu sapien nisi. Sed felis nisi, vehicula at nisi nec, tincidunt vulputate enim.'
                    }],
                    markDefs: [],
                    style:    'normal'
                }],
                image: {
                    _type: 'image',
                    asset: {
                        _ref:  'image-fcb623afa1a4f63bdf759291028089458ae9484d-385x685-jpg',
                        _type: 'reference'
                    }
                },
                language:       'de',
                releaseDate:    '2021-02-12',
                releaseVersion: '3.68',
                tags:           [{
                    _key:  '889d8351f0f1',
                    _type: 'tag',
                    color: {
                        _type: 'color',
                        alpha: 1,
                        hex:   '#549fc4',
                        hsl:   {
                            _type: 'hslaColor',
                            a:     1,
                            h:     200.00000000000006,
                            l:     0.5485443200000001,
                            s:     0.4876573487789543
                        },
                        hsv: {_type: 'hsvaColor', a: 1, h: 200.00000000000006, s: 0.5728, v: 0.7687},
                        rgb: {_type: 'rgbaColor', a: 1, b: 196, g: 159, r: 84}
                    },
                    label: 'Hello'
                }, {
                    _key:  '082643c40f0a',
                    _type: 'tag',
                    color: {
                        _type: 'color',
                        alpha: 1,
                        hex:   '#b12a45',
                        hsl:   {
                            _type: 'hslaColor',
                            a:     1,
                            h:     348.116504854369,
                            l:     0.42971246500000004,
                            s:     0.6143352974412786
                        },
                        hsv: {
                            _type: 'hsvaColor',
                            a:     1,
                            h:     348.116504854369,
                            s:     0.7611,
                            v:     0.6937000000000001
                        },
                        rgb: {_type: 'rgbaColor', a: 1, b: 69, g: 42, r: 177}
                    },
                    label: 'Alex'
                }, {
                    _key:  '1e2d382703dc',
                    _type: 'tag',
                    color: {
                        _type: 'color',
                        alpha: 1,
                        hex:   '#392eff',
                        hsl:   {_type: 'hslaColor', a: 1, h: 243.26213592233012, l: 0.5903, s: 1},
                        hsv:   {_type: 'hsvaColor', a: 1, h: 243.26213592233012, s: 0.8194, v: 1},
                        rgb:   {_type: 'rgbaColor', a: 1, b: 255, g: 46, r: 57}
                    },
                    label: 'please'
                }, {
                    _key:  '7f366b3915e3',
                    _type: 'tag',
                    color: {
                        _type: 'color',
                        alpha: 1,
                        hex:   '#e37d24',
                        hsl:   {
                            _type: 'hslaColor',
                            a:     1,
                            h:     27.96116504854369,
                            l:     0.5155,
                            s:     0.7731000000000001
                        },
                        hsv: {
                            _type: 'hsvaColor',
                            a:     1,
                            h:     27.96116504854369,
                            s:     0.8416601694962386,
                            v:     0.89006695
                        },
                        rgb: {_type: 'rgbaColor', a: 1, b: 36, g: 125, r: 227}
                    },
                    label: 'forgive'
                }, {
                    _key:  '5c786d644f4c',
                    _type: 'tag',
                    color: {
                        _type: 'color',
                        alpha: 1,
                        hex:   '#2d945c',
                        hsl:   {
                            _type: 'hslaColor',
                            a:     1,
                            h:     147.49514563106794,
                            l:     0.3780705999999999,
                            s:     0.5372790161414298
                        },
                        hsv: {
                            _type: 'hsvaColor',
                            a:     1,
                            h:     147.49514563106794,
                            s:     0.6990000000000002,
                            v:     0.5811999999999999
                        },
                        rgb: {_type: 'rgbaColor', a: 1, b: 92, g: 148, r: 45}
                    },
                    label: 'me'
                }],
                title:    'Testing something with a Mobile image DE',
                quarter:  1,
                year:     2021,
                pageSlug: 'CHANGELOG_INDEX_URL/q1-2021'
            }, {
                content: [{
                    _key:     '26326d79c03e',
                    _type:    'block',
                    children: [{
                        _key:  '84d897ca4905',
                        _type: 'span',
                        marks: [],
                        text:  'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.'
                    }],
                    markDefs: [],
                    style:    'normal'
                }],
                image: {
                    _type: 'image',
                    asset: {
                        _ref:  'image-0e5907bf3fe72297e3e469016c63d8df19c790df-1000x526-png',
                        _type: 'reference'
                    }
                },
                language:       'de',
                releaseDate: '2021-01-05',
                tags:        [{
                    _key:  'ff89a4e95059',
                    _type: 'tag',
                    color: {
                        _type: 'color',
                        alpha: 1,
                        hex:   '#24a3e3',
                        hsl:   {_type: 'hslaColor', a: 1, h: 200, l: 0.5156, s: 0.7732},
                        hsv:   {_type: 'hsvaColor', a: 1, h: 200, s: 0.8414, v: 0.8901},
                        rgb:   {_type: 'rgbaColor', a: 1, b: 227, g: 163, r: 46}
                    },
                    label: 'Carsharing'
                }],
                title:    'This is henry\'s title -GERMAN',
                quarter:  1,
                year:     2021,
                pageSlug: 'CHANGELOG_INDEX_URL/q1-2021'
            }], _updatedAt: '2021-02-18T14:56:03Z'
        },
        'CHANGELOG_INDEX_URL/q1-2020': {
            content: [{
                callToAction: {_type: 'callToAction', title: 'what\'s this'},
                content:      [{
                    _key:     '8347cbbd2657',
                    _type:    'block',
                    children: [{
                        _key:  '2c681fa5994e',
                        _type: 'span',
                        marks: [],
                        text:  'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.'
                    }],
                    markDefs: [],
                    style:    'normal'
                }],
                image: {
                    _type: 'image',
                    asset: {
                        _ref:  'image-435e6806cba82d4843ed32384b7d09f87823419b-546x340-jpg',
                        _type: 'reference'
                    }
                },
                language:       'de',
                releaseDate:    '2020-01-02',
                releaseVersion: '666',
                tags:           [{
                    _key:  '5679bf63115f',
                    _type: 'tag',
                    color: {
                        _type: 'color',
                        alpha: 1,
                        hex:   '#e3242d',
                        hsl:   {
                            _type: 'hslaColor',
                            a:     1,
                            h:     357.2038834951457,
                            l:     0.5155,
                            s:     0.7731000000000001
                        },
                        hsv: {
                            _type: 'hsvaColor',
                            a:     1,
                            h:     357.2038834951457,
                            s:     0.8416601694962386,
                            v:     0.89006695
                        },
                        rgb: {_type: 'rgbaColor', a: 1, b: 45, g: 36, r: 227}
                    },
                    label: 'Chulo'
                }],
                title:    'Changelog Q1 2020',
                quarter:  1,
                year:     2020,
                pageSlug: 'CHANGELOG_INDEX_URL/q1-2020'
            }], _updatedAt: '2021-03-29T09:54:29Z'
        },
        'CHANGELOG_INDEX_URL/q4-2020': {
            content: [{
                callToAction: {
                    _type: 'callToAction',
                    link:  '/v4/request/price-request',
                    title: 'Read more'
                },
                language:       'de',
                releaseDate:    '2020-12-24',
                releaseVersion: '3.90',
                tags:           [{
                    _key:  'd341983e74f7',
                    _type: 'tag',
                    color: {
                        _type: 'color',
                        alpha: 1,
                        hex:   '#24a3e3',
                        hsl:   {_type: 'hslaColor', a: 1, h: 200, l: 0.5156, s: 0.7732},
                        hsv:   {_type: 'hsvaColor', a: 1, h: 200, s: 0.8414, v: 0.8901},
                        rgb:   {_type: 'rgbaColor', a: 1, b: 227, g: 163, r: 46}
                    },
                    label: 'Carsharing'
                }, {
                    _key:  '82cc9fed2af0',
                    _type: 'tag',
                    color: {
                        _type: 'color',
                        alpha: 1,
                        hex:   '#e34e24',
                        hsl:   {
                            _type: 'hslaColor',
                            a:     1,
                            h:     13.281553398058252,
                            l:     0.5155,
                            s:     0.7731000000000001
                        },
                        hsv: {
                            _type: 'hsvaColor',
                            a:     1,
                            h:     13.281553398058252,
                            s:     0.8416601694962386,
                            v:     0.89006695
                        },
                        rgb: {_type: 'rgbaColor', a: 1, b: 36, g: 78, r: 227}
                    },
                    label: 'Fleet Management'
                }],
                title:    'Testing Changelog Q 4',
                quarter:  4,
                year:     2020,
                pageSlug: 'CHANGELOG_INDEX_URL/q4-2020'
            }, {
                content: [{
                    _key:     '1a103460e3bf',
                    _type:    'block',
                    children: [{_key: '754eaeb63dfd', _type: 'span', marks: [], text: 'Some content'}],
                    markDefs: [],
                    style:    'normal'
                }],
                language:       'de',
                releaseDate:    '2020-10-01',
                releaseVersion: '2323',
                title:          'Testing another Q 4 ',
                quarter:        4,
                year:           2020,
                pageSlug:       'CHANGELOG_INDEX_URL/q4-2020'
            }], _updatedAt: '2021-03-05T17:30:01Z'
        },
        'CHANGELOG_INDEX_URL/q3-2020': {
            content: [{
                content: [{
                    _key:     'b2f2e81cb77a',
                    _type:    'block',
                    children: [{
                        _key:  'fefad3ea50f8',
                        _type: 'span',
                        marks: [],
                        text:  'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.'
                    }],
                    markDefs: [],
                    style:    'normal'
                }],
                language:       'de',
                releaseDate:    '2020-09-08',
                releaseVersion: '3.70',
                title:          'Testing changelog Q3',
                quarter:        3,
                year:           2020,
                pageSlug:       'CHANGELOG_INDEX_URL/q3-2020'
            }], _updatedAt: '2021-02-18T09:04:28Z'
        },
        'CHANGELOG_INDEX_URL/q2-2020': {
            content: [{
                content: [{
                    _key:     '9cfe6aa3c7af',
                    _type:    'block',
                    children: [{
                        _key:  '95abc513b5d2',
                        _type: 'span',
                        marks: [],
                        text:  'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.'
                    }],
                    markDefs: [],
                    style:    'normal'
                }],
                language:       'de',
                releaseDate:    '2020-05-12',
                releaseVersion: '5.68',
                tags:           [{
                    _key:  '60cdc5aa30da',
                    _type: 'tag',
                    color: {
                        _type: 'color',
                        alpha: 1,
                        hex:   '#d5e324',
                        hsl:   {
                            _type: 'hslaColor',
                            a:     1,
                            h:     64.31067961165051,
                            l:     0.5155,
                            s:     0.7731000000000001
                        },
                        hsv: {
                            _type: 'hsvaColor',
                            a:     1,
                            h:     64.31067961165051,
                            s:     0.8416601694962386,
                            v:     0.89006695
                        },
                        rgb: {_type: 'rgbaColor', a: 1, b: 36, g: 227, r: 213}
                    },
                    label: 'Fleet Management'
                }],
                title:    'Testing Changelog Q2',
                quarter:  2,
                year:     2020,
                pageSlug: 'CHANGELOG_INDEX_URL/q2-2020'
            }], _updatedAt: '2021-02-15T09:39:17Z'
        },
        'CHANGELOG_INDEX_URL': {
            content: [{
                content: [{
                    _key:     '42539dd8a013',
                    _type:    'block',
                    children: [{
                        _key:  'becac38ba3dd',
                        _type: 'span',
                        marks: [],
                        text:  'Let us see if this works Alex -> This is the german page'
                    }],
                    markDefs: [],
                    style:    'normal'
                }],
                language:       'de',
                releaseDate:    '2021-12-22',
                releaseVersion: '3.99',
                tags:           [],
                title:          'Welcome to the fleetster changelog',
                quarter:        4,
                year:           2021,
                pageSlug:       'CHANGELOG_INDEX_URL/q4-2021'
            }, {
                content: [{
                    _key:     '609b5ef651e6',
                    _type:    'block',
                    children: [{_key: '150a6a147c2b', _type: 'span', marks: [], text: 'asdasdaddaddada'}],
                    markDefs: [],
                    style:    'normal'
                }],
                language:       'de',
                releaseDate:    '2021-12-06',
                releaseVersion: '999',
                title:          'Infinity',
                quarter:        4,
                year:           2021,
                pageSlug:       'CHANGELOG_INDEX_URL/q4-2021'
            }, {
                content: [{
                    _key:     '16bfb4db4e2e',
                    _type:    'block',
                    children: [{_key: 'c8909871a0fd', _type: 'span', marks: [], text: 'adsadsadsa'}],
                    markDefs: [],
                    style:    'normal'
                }],
                language:       'de',
                releaseDate:    '2021-12-05',
                releaseVersion: '666',
                title:          'Even more',
                quarter:        4,
                year:           2021,
                pageSlug:       'CHANGELOG_INDEX_URL/q4-2021'
            }, {
                content: [{
                    _key:     '0412cfbc36d9',
                    _type:    'block',
                    children: [{_key: '21847eb29944', _type: 'span', marks: [], text: 'asdasdasd'}],
                    markDefs: [],
                    style:    'normal'
                }],
                language:       'de',
                releaseDate:    '2021-12-03',
                releaseVersion: '1213',
                title:          'Something more',
                quarter:        4,
                year:           2021,
                pageSlug:       'CHANGELOG_INDEX_URL/q4-2021'
            }, {
                content: [{
                    _key:     'b546b1dd476c',
                    _type:    'block',
                    children: [{_key: '3a98bf2cb9e8', _type: 'span', marks: [], text: 'asdasdasdda'}],
                    markDefs: [],
                    style:    'normal'
                }],
                language:       'de',
                releaseDate:    '2021-12-02',
                releaseVersion: '123',
                title:          'Something else',
                quarter:        4,
                year:           2021,
                pageSlug:       'CHANGELOG_INDEX_URL/q4-2021'
            }, {
                content: [{
                    _key:     'afa9e4661959',
                    _type:    'block',
                    children: [{_key: '0f2f131e7fc7', _type: 'span', marks: [], text: 'asdasdasd'}],
                    markDefs: [],
                    style:    'normal'
                }],
                language:       'de',
                releaseDate:    '2021-12-01',
                releaseVersion: '3.845845',
                title:          'Something',
                quarter:        4,
                year:           2021,
                pageSlug:       'CHANGELOG_INDEX_URL/q4-2021'
            }, {
                content: [{
                    _key:     'f9acd3003b49',
                    _type:    'block',
                    children: [{_key: '063c5689d9af', _type: 'span', marks: [], text: 'asdasd'}],
                    markDefs: [],
                    style:    'normal'
                }],
                language:       'de',
                releaseDate:    '2021-11-03',
                releaseVersion: '777',
                title:          'Last one',
                quarter:        4,
                year:           2021,
                pageSlug:       'CHANGELOG_INDEX_URL/q4-2021'
            }, {
                content: [{
                    _key:     '86b299795f1b',
                    _type:    'block',
                    children: [{
                        _key:  'b2ee087ec302',
                        _type: 'span',
                        marks: [],
                        text:  'Yeahnasfksjfadkfdsadad'
                    }],
                    markDefs: [],
                    style:    'normal'
                }],
                language:       'de',
                releaseDate:    '2021-10-01',
                releaseVersion: '123123',
                title:          'Really the last one',
                quarter:        4,
                year:           2021,
                pageSlug:       'CHANGELOG_INDEX_URL/q4-2021'
            }],
            _updatedAt: '2021-03-30T09:38:51.627Z',
            isIndex: true
        }
    }
}

let changelogs = {i18n: {}};

const pagination = [{year: 2021, quarter: 4, pageSlug: 'CHANGELOG_INDEX_URL/q4-2021'}, {
    year:     2021,
    quarter:  1,
    pageSlug: 'CHANGELOG_INDEX_URL/q1-2021'
}, {year: 2020, quarter: 4, pageSlug: 'CHANGELOG_INDEX_URL/q4-2020'}, {
    year:     2020,
    quarter:  3,
    pageSlug: 'CHANGELOG_INDEX_URL/q3-2020'
}, {year: 2020, quarter: 2, pageSlug: 'CHANGELOG_INDEX_URL/q2-2020'}, {
    year:     2020,
    quarter:  1,
    pageSlug: 'CHANGELOG_INDEX_URL/q1-2020'
}];

for (const lang in changelogUrlMap) {
    const indexUrl = changelogUrlMap[lang];
    const defaultChangelogsString = JSON.stringify(defaultChangelogs);
    const languageChangelogs = defaultChangelogsString.replace(/CHANGELOG_INDEX_URL/g, indexUrl);
    const languagePagination = JSON.stringify(pagination).replace(/CHANGELOG_INDEX_URL/g, indexUrl);
    changelogs.i18n[lang] = JSON.parse(languageChangelogs);
    for (const slug in changelogs.i18n[lang].routes) {
        const route = changelogs.i18n[lang].routes[slug];
        route.pagination = languagePagination;
    }
}

export default changelogs;
