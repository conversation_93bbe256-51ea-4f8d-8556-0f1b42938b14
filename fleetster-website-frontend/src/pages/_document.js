import React from 'react';
import {Head, Html, Main, NextScript} from 'next/document';
import PropTypes from 'prop-types';
const language = process.env.WEBSITE_LANGUAGE || 'de';

function Document() {
    return (
        <Html lang={language}>
            <Head/>
            <body>
                <Main/>
                <NextScript/>
            </body>
        </Html>
    );
}

Document.propTypes = {
    language: PropTypes.string
};

export default Document;
