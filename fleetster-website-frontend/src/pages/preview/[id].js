import React from 'react';
import _ from 'lodash';

import {getOpenGraphImages} from 'rf-static';
import {host, logo} from 'rf-data/siteConfig.json';
import {fetchDocuments, getDocument} from 'sanity-client-adapter';

import {RegularPage} from '../../pageComponents';
import Custom404Page from '../404';

async function fetchImageDataByIds(imageIds) {
    const images = await fetchDocuments('*[_id in $imageIds] { _id, metadata { dimensions { height, width } } }', {imageIds});

    return _.reduce(images, (result, image) => _.set(result, image?._id, _.get(image, 'metadata.dimensions')), {});
}

function extractImageIds(object) {
    return _(object).map(value => {
        const ref = _.get(value, 'asset._ref');
        if (ref) { return ref; }

        if (typeof value === 'object') {
            return extractImageIds(value);
        }

        return null;
    }).compact().flatten().value();
}

function formatRoute(page) {
    const route = {};

    route.slug = page._id;
    route.disallowRobots = true;
    route.canonical = `${host.url}/page/?pageId=${page._id}`;
    route.openGraph = {images: getOpenGraphImages(page?.title || 'Missing title', page?.openGraphImage)};
    route.logo = logo;
    route.page = page;

    return route;
}

function setImageDimensions(object, images) {
    _.forEach(object, value => {
        const ref = _.get(value, 'asset._ref');
        if (ref) {
            const dimensions = _.get(images, ref);

            if (dimensions) {
                value.width = dimensions.width;
                value.height = dimensions.height;
            }
        } else if (typeof value === 'object') {
            setImageDimensions(value, images);
        }
    });
}

async function hydrateImages(route) {
    const imageIds = extractImageIds(route.page.content);
    const images = await fetchImageDataByIds(imageIds);

    setImageDimensions(route.page.content, images);
}

export function getStaticPaths() {
    return {paths: [], fallback: 'blocking'};
}

export async function getStaticProps(context) {
    const pageId = _.get(context, 'params.id');

    if (!pageId) {
        return {props: {route: null}};
    }

    const page = await getDocument(pageId);
    if (!page) {
        return {props: {route: null}, revalidate: 1};
    }

    const route = formatRoute(page, pageId);
    await hydrateImages(route);

    return {props: {route}, revalidate: 1};
}

export default function PreviewPage({route}) {
    if (!route) {
        return <Custom404Page/>;
    }

    return <RegularPage route={route}/>;
}
