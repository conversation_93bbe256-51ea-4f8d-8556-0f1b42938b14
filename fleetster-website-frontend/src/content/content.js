import React from 'react';
import PropTypes from 'prop-types';

import * as SectionComponents from './sections';
import _ from 'lodash';

export function resolveSectionComponent(sectionType) {
    if (!sectionType) {
        return null;
    }

    // eslint-disable-next-line import/namespace
    const Section = SectionComponents[_.upperFirst(sectionType)];

    if (Section) {
        return Section;
    }

    console.error('Cant find section ', sectionType);

    return null;
}

export function Content({content}) {
    if (!Array.isArray(content)) { return null; }

    const isArticle = !!_.find(content, {_type: 'articleHeadingSection'});

    return (
        <div className={isArticle ? 'article' : ''}>
            {content.map((section, index) => {
                const SectionComponent = resolveSectionComponent(section._type);

                if (!SectionComponent) {
                    return null;
                }

                return <SectionComponent {...section} key={section._key} index={index}/>;
            })}
        </div>
    );
}

Content.propTypes = {
    content: PropTypes.arrayOf(PropTypes.object)
};
