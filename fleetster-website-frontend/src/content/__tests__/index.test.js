import React from 'react';

describe('Content', () => {
    const mockSections = {
        ArticleHeadingSection: () => 'articleHeadingSection',
        DummySection:          () => 'sectionOne',
        DummySectionTwo:       () => 'sectionTwo'
    };

    beforeAll(() => {
        jest.mock('../sections', () => mockSections);
    });

    describe('resolveSectionComponent', () => {
        test('returns null if sectionType is falsy', () => {
            const {resolveSectionComponent} = require('../content');

            expect(resolveSectionComponent(null)).toBe(null);
            expect(resolveSectionComponent()).toBe(null);
            expect(resolveSectionComponent('')).toBe(null);
        });

        test('returns null and output an error if section does not exist', () => {
            const {resolveSectionComponent} = require('../content');

            const consoleError = jest.spyOn(console, 'error').mockImplementation(() => 'error message');

            expect(resolveSectionComponent('nonExistentSection')).toBe(null);
            expect(consoleError).toBeCalled();

            consoleError.mockRestore();
        });

        test('returns section correctly', () => {
            const {resolveSectionComponent} = require('../content');

            expect(resolveSectionComponent('dummySection')).toBe(mockSections.DummySection);
        });
    });

    test('outputs an error if section content is not an array', () => {
        const {Content} = require('../content');

        const consoleError = jest.spyOn(console, 'error').mockImplementation(() => 'error message');

        const content = render(<Content content={{}}/>);

        expect(content.toJSON()).toBeNull();
        expect(consoleError).toBeCalled();

        consoleError.mockRestore();
    });

    test('renders all the sections', () => {
        const {Content} = require('../content');

        const sections = [{_type: 'DummySection', _key: 1}, {_type: 'DummySectionTwo', _key: 2}];

        const content = render(<Content content={sections}/>);
        const componentString = JSON.stringify(content.toJSON());

        expect(componentString).toContain('sectionOne');
        expect(componentString).toContain('sectionTwo');
    });

    test('acts the correct class if one of the sections is an article', () => {
        const {Content} = require('../content');

        const sections = [{_type: 'articleHeadingSection', _key: 1}];

        const content = render(<Content content={sections}/>);
        const componentString = JSON.stringify(content.toJSON());

        expect(componentString).toContain('article');
        expect(componentString).toContain('articleHeadingSection');
    });
});
