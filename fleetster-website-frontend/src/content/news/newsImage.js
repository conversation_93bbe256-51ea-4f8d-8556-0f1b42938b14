import React from 'react';
import PropTypes from 'prop-types';
import css, {global} from 'styled-jsx/css';

import {Image} from 'rf-components/image';
import {hSpacing, screenSizes, vSpacing} from 'rf-styles';

const newsImgStyle = global`
.news-image > .center-content > img {
    object-position: left top;
}`;

const styles = css`
  .news-image {
    border-radius: 4px;
    margin-bottom: ${vSpacing.s}px;
    position: relative;
    overflow: hidden;
    height: 184px;
  }
 

  @media (min-width: ${screenSizes.s}px) {
    .news-image {
      min-height: 275px;
      max-height: 275px;
      height: 275px;
      margin-bottom: ${vSpacing.m}px;
    }
  }

  @media (min-width: ${screenSizes.m}px) {
    .news-image {
      min-height: 140px;
      max-height: 140px;
      min-width: 240px;
      max-width: 240px;
      margin-right: ${hSpacing.m}px;
    }
  }

  @media (min-width: ${screenSizes.l}px) {
    .news-image {
      min-height: 150px;
      max-height: 150px;
      min-width: 310px;
      max-width: 310px;
    }
  }`;

export function NewsImage(props) {
    let {image, imageAlt} = props;

    return (
        <div className={'news-image'}>
            <Image
                image={image}
                imageAlt={imageAlt}
                layout={'cover'}
            />
            <style jsx>{styles}</style>
            <style jsx global>{newsImgStyle}</style>
        </div>
    );
}

NewsImage.propTypes = {
    image:    PropTypes.object,
    imageAlt: PropTypes.string
};
