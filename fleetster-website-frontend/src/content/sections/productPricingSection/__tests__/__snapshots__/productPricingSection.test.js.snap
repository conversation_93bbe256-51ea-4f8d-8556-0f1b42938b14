// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`productPricingSection clicking a different currency changes the values 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding product-pricing-section"
>
  <div
    className="pricing-tabs"
  >
    <div
      className="pricing-periods"
    >
      <div
        className="pricing-period"
        onClick={[Function]}
      >
        <h6>
          Monthly
        </h6>
        <p>
          +
          25
          %
        </p>
      </div>
      <div
        className="pricing-period"
        onClick={[Function]}
      >
        <h6>
          Quarterly
        </h6>
        <p>
          +
          10
          %
        </p>
      </div>
      <div
        className="pricing-period active"
        onClick={[Function]}
      >
        <h6>
          Yearly
        </h6>
        <p>
          +
          0
          %
        </p>
      </div>
      <style />
    </div>
    <div
      className="pricing-currencies"
    >
      <div
        className="dropdown-item active"
        onClick={[Function]}
      >
        <div
          className="label-container"
        >
          <h6>
            Dollar
          </h6>
          <p>
            $/USD
          </p>
        </div>
        <span
          className="bp3-icon"
        >
          <div
            height={20}
            src="/v4/svgs/caretDown.svg"
            width="20px"
          />
        </span>
        <style />
      </div>
      <ul
        className=""
      >
        <div
          className="dropdown-item"
          onClick={[Function]}
        >
          <div
            className="label-container"
          >
            <h6>
              Euro
            </h6>
            <p>
              €/EUR
            </p>
          </div>
          <span
            className="bp3-icon"
          >
            <div
              height={20}
              src="/v4/svgs/.svg"
              width="20px"
            />
          </span>
          <style />
        </div>
      </ul>
      <style />
    </div>
  </div>
  <div
    className="pricing-cards-container"
    onClick={[Function]}
    onDragStart={[Function]}
    onMouseDown={[Function]}
    onMouseMove={[Function]}
    onMouseUp={[Function]}
    onTouchEnd={[Function]}
    onTouchMove={[Function]}
    onTouchStart={[Function]}
  >
    <div
      className="rf-cell pricing-card"
    >
      <div
        className="rf-flex-container pricing-card-content"
      >
        <h4
          className="h4 pricing-card-heading"
        >
          Basic
        </h4>
        <div
          className="p-small"
        >
          <p>
            What you need to kickstart your mobility
          </p>
        </div>
        <div
          className="product-price"
        >
          8,00 $
        </div>
        <style />
        <div
          className="pricing-condition"
        >
          per vehicle per month, billed annually
        </div>
        <div
          className="call-to-action small"
        >
          <a
            className="center-content  hover-enabled"
            rel="noreferrer"
            target="_blank"
          >
            Free Trial
            <style />
          </a>
          <style />
        </div>
        <div
          className="product-features"
        >
          <div
            className="product-features-container"
          >
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Book vehicles with your computer or phone
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Add vehicles so they can be shared
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Calendar integration
              </p>
            </div>
          </div>
        </div>
        <style />
        <style />
      </div>
      <style />
    </div>
    <div
      className="rf-cell pricing-card"
    >
      <div
        className="center-content most-popular-title"
      >
        <span>
          carsharing.prices.mostPopular
        </span>
      </div>
      <div
        className="rf-flex-container pricing-card-content most-popular"
      >
        <h4
          className="h4 pricing-card-heading"
        >
          Pro
        </h4>
        <div
          className="p-small"
        >
          <p>
            Unlock the power of analytics for your fleet
          </p>
        </div>
        <div
          className="product-price"
        >
          16,00 $
        </div>
        <style />
        <div
          className="pricing-condition"
        >
          per vehicle per month, billed anually
        </div>
        <div
          className="call-to-action small"
        >
          <a
            className="center-content  hover-enabled"
            rel="noreferrer"
            target="_blank"
          >
            Free Trial
            <style />
          </a>
          <style />
        </div>
        <div
          className="product-features"
        >
          <p
            className="plus"
          >
            Everything in the Basic package plus:
          </p>
          <div
            className="product-features-container"
          >
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Booking behaviour statistics
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Vehicle utilization data
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Excel compatible data export
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Be informed of damages to the vehicle
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Manage the process of exchanging keys
              </p>
            </div>
          </div>
        </div>
        <style />
        <style />
      </div>
      <style />
    </div>
    <div
      className="rf-cell pricing-card"
    >
      <div
        className="rf-flex-container pricing-card-content"
      >
        <h4
          className="h4 pricing-card-heading"
        >
          Enterprise
        </h4>
        <div
          className="p-small"
        >
          <p>
            Automate part of your vehicle management
          </p>
        </div>
        <div
          className="product-price"
        >
          25,00 $
        </div>
        <style />
        <div
          className="pricing-condition"
        >
          per vehicle per month, billed anually
        </div>
        <div
          className="call-to-action small"
        >
          <a
            className="center-content  hover-enabled"
            rel="noreferrer"
            target="_blank"
          >
            Free Trial
            <style />
          </a>
          <style />
        </div>
        <div
          className="product-features"
        >
          <p
            className="plus"
          >
            Everything in the Pro package plus:
          </p>
          <div
            className="product-features-container"
          >
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Book vehicles according to categories
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Minimize late returns with booking buffers
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Automatic re-booking in cases of late return
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Manage fuel cards and pins in the system
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Your users can have access to the fuel cards and code with the app
              </p>
            </div>
          </div>
        </div>
        <style />
        <style />
      </div>
      <style />
    </div>
    <style />
    <style>
      
              .pricing-cards-container {
                transform: translateX(calc(50vw - 150% - 10px - 35px));
                transition: transform 0.5s ease-in-out;
              }

              @media (min-width: 361px) {
                .pricing-cards-container {
                  transform: translateX(calc(50vw - 150% - 10px - 45px));
                }
              }

              @media (min-width: 641px) {
                .pricing-cards-container {
                  transform: translateX(calc(50vw - 150% - 30px - 65px));
                }
              }

              @media (min-width: 1025px) {
                .pricing-cards-container {
                  transform: none;
                  transition: none;
                }
              }
            
    </style>
  </div>
  <div
    className="observation-text"
  />
  <style />
</section>
`;

exports[`productPricingSection clicking a pricing period changes the values 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding product-pricing-section"
>
  <div
    className="pricing-tabs"
  >
    <div
      className="pricing-periods"
    >
      <div
        className="pricing-period active"
        onClick={[Function]}
      >
        <h6>
          Monthly
        </h6>
        <p>
          +
          25
          %
        </p>
      </div>
      <div
        className="pricing-period"
        onClick={[Function]}
      >
        <h6>
          Quarterly
        </h6>
        <p>
          +
          10
          %
        </p>
      </div>
      <div
        className="pricing-period"
        onClick={[Function]}
      >
        <h6>
          Yearly
        </h6>
        <p>
          +
          0
          %
        </p>
      </div>
      <style />
    </div>
    <div
      className="pricing-currencies"
    >
      <div
        className="dropdown-item active"
        onClick={[Function]}
      >
        <div
          className="label-container"
        >
          <h6>
            Euro
          </h6>
          <p>
            €/EUR
          </p>
        </div>
        <span
          className="bp3-icon"
        >
          <div
            height={20}
            src="/v4/svgs/caretDown.svg"
            width="20px"
          />
        </span>
        <style />
      </div>
      <ul
        className=""
      >
        <div
          className="dropdown-item"
          onClick={[Function]}
        >
          <div
            className="label-container"
          >
            <h6>
              Dollar
            </h6>
            <p>
              $/USD
            </p>
          </div>
          <span
            className="bp3-icon"
          >
            <div
              height={20}
              src="/v4/svgs/.svg"
              width="20px"
            />
          </span>
          <style />
        </div>
      </ul>
      <style />
    </div>
  </div>
  <div
    className="pricing-cards-container"
    onClick={[Function]}
    onDragStart={[Function]}
    onMouseDown={[Function]}
    onMouseMove={[Function]}
    onMouseUp={[Function]}
    onTouchEnd={[Function]}
    onTouchMove={[Function]}
    onTouchStart={[Function]}
  >
    <div
      className="rf-cell pricing-card"
    >
      <div
        className="rf-flex-container pricing-card-content"
      >
        <h4
          className="h4 pricing-card-heading"
        >
          Basic
        </h4>
        <div
          className="p-small"
        >
          <p>
            What you need to kickstart your mobility
          </p>
        </div>
        <div
          className="product-price"
        >
          6,25 €
        </div>
        <style />
        <div
          className="pricing-condition"
        >
          per vehicle per month, billed annually
        </div>
        <div
          className="call-to-action small"
        >
          <a
            className="center-content  hover-enabled"
            rel="noreferrer"
            target="_blank"
          >
            Free Trial
            <style />
          </a>
          <style />
        </div>
        <div
          className="product-features"
        >
          <div
            className="product-features-container"
          >
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Book vehicles with your computer or phone
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Add vehicles so they can be shared
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Calendar integration
              </p>
            </div>
          </div>
        </div>
        <style />
        <style />
      </div>
      <style />
    </div>
    <div
      className="rf-cell pricing-card"
    >
      <div
        className="center-content most-popular-title"
      >
        <span>
          carsharing.prices.mostPopular
        </span>
      </div>
      <div
        className="rf-flex-container pricing-card-content most-popular"
      >
        <h4
          className="h4 pricing-card-heading"
        >
          Pro
        </h4>
        <div
          className="p-small"
        >
          <p>
            Unlock the power of analytics for your fleet
          </p>
        </div>
        <div
          className="product-price"
        >
          12,50 €
        </div>
        <style />
        <div
          className="pricing-condition"
        >
          per vehicle per month, billed anually
        </div>
        <div
          className="call-to-action small"
        >
          <a
            className="center-content  hover-enabled"
            rel="noreferrer"
            target="_blank"
          >
            Free Trial
            <style />
          </a>
          <style />
        </div>
        <div
          className="product-features"
        >
          <p
            className="plus"
          >
            Everything in the Basic package plus:
          </p>
          <div
            className="product-features-container"
          >
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Booking behaviour statistics
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Vehicle utilization data
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Excel compatible data export
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Be informed of damages to the vehicle
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Manage the process of exchanging keys
              </p>
            </div>
          </div>
        </div>
        <style />
        <style />
      </div>
      <style />
    </div>
    <div
      className="rf-cell pricing-card"
    >
      <div
        className="rf-flex-container pricing-card-content"
      >
        <h4
          className="h4 pricing-card-heading"
        >
          Enterprise
        </h4>
        <div
          className="p-small"
        >
          <p>
            Automate part of your vehicle management
          </p>
        </div>
        <div
          className="product-price"
        >
          18,75 €
        </div>
        <style />
        <div
          className="pricing-condition"
        >
          per vehicle per month, billed anually
        </div>
        <div
          className="call-to-action small"
        >
          <a
            className="center-content  hover-enabled"
            rel="noreferrer"
            target="_blank"
          >
            Free Trial
            <style />
          </a>
          <style />
        </div>
        <div
          className="product-features"
        >
          <p
            className="plus"
          >
            Everything in the Pro package plus:
          </p>
          <div
            className="product-features-container"
          >
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Book vehicles according to categories
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Minimize late returns with booking buffers
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Automatic re-booking in cases of late return
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Manage fuel cards and pins in the system
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Your users can have access to the fuel cards and code with the app
              </p>
            </div>
          </div>
        </div>
        <style />
        <style />
      </div>
      <style />
    </div>
    <style />
    <style>
      
              .pricing-cards-container {
                transform: translateX(calc(50vw - 150% - 10px - 35px));
                transition: transform 0.5s ease-in-out;
              }

              @media (min-width: 361px) {
                .pricing-cards-container {
                  transform: translateX(calc(50vw - 150% - 10px - 45px));
                }
              }

              @media (min-width: 641px) {
                .pricing-cards-container {
                  transform: translateX(calc(50vw - 150% - 30px - 65px));
                }
              }

              @media (min-width: 1025px) {
                .pricing-cards-container {
                  transform: none;
                  transition: none;
                }
              }
            
    </style>
  </div>
  <div
    className="observation-text"
  />
  <style />
</section>
`;

exports[`productPricingSection does not fail to render pricing content for unknown provided currency 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding product-pricing-section"
>
  <div
    className="pricing-tabs"
  >
    <div
      className="pricing-periods"
    >
      <div
        className="pricing-period"
        onClick={[Function]}
      >
        <h6>
          Monthly
        </h6>
        <p>
          +
          25
          %
        </p>
      </div>
      <div
        className="pricing-period"
        onClick={[Function]}
      >
        <h6>
          Quarterly
        </h6>
        <p>
          +
          10
          %
        </p>
      </div>
      <div
        className="pricing-period active"
        onClick={[Function]}
      >
        <h6>
          Yearly
        </h6>
        <p>
          +
          0
          %
        </p>
      </div>
      <style />
    </div>
  </div>
  <div
    className="pricing-cards-container"
    onClick={[Function]}
    onDragStart={[Function]}
    onMouseDown={[Function]}
    onMouseMove={[Function]}
    onMouseUp={[Function]}
    onTouchEnd={[Function]}
    onTouchMove={[Function]}
    onTouchStart={[Function]}
  >
    <div
      className="rf-cell pricing-card"
    >
      <div
        className="rf-flex-container pricing-card-content"
      >
        <h4
          className="h4 pricing-card-heading"
        >
          Basic
        </h4>
        <div
          className="p-small"
        >
          <p>
            What you need to kickstart your mobility
          </p>
        </div>
        <div
          className="product-price"
        />
        <style />
        <div
          className="pricing-condition"
        >
          per vehicle per month, billed annually
        </div>
        <div
          className="call-to-action small"
        >
          <a
            className="center-content  hover-enabled"
            rel="noreferrer"
            target="_blank"
          >
            Free Trial
            <style />
          </a>
          <style />
        </div>
        <div
          className="product-features"
        >
          <div
            className="product-features-container"
          >
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Book vehicles with your computer or phone
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Add vehicles so they can be shared
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Calendar integration
              </p>
            </div>
          </div>
        </div>
        <style />
        <style />
      </div>
      <style />
    </div>
    <style />
    <style>
      
              .pricing-cards-container {
                transform: translateX(calc(50vw - 150% - 10px - 35px));
                transition: transform 0.5s ease-in-out;
              }

              @media (min-width: 361px) {
                .pricing-cards-container {
                  transform: translateX(calc(50vw - 150% - 10px - 45px));
                }
              }

              @media (min-width: 641px) {
                .pricing-cards-container {
                  transform: translateX(calc(50vw - 150% - 30px - 65px));
                }
              }

              @media (min-width: 1025px) {
                .pricing-cards-container {
                  transform: none;
                  transition: none;
                }
              }
            
    </style>
  </div>
  <div
    className="observation-text"
  />
  <style />
</section>
`;

exports[`productPricingSection renders even without pricings 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding product-pricing-section"
>
  <div
    className="pricing-tabs"
  >
    <div
      className="pricing-periods"
    >
      <div
        className="pricing-period"
        onClick={[Function]}
      >
        <h6>
          Monthly
        </h6>
        <p>
          +
          25
          %
        </p>
      </div>
      <div
        className="pricing-period"
        onClick={[Function]}
      >
        <h6>
          Quarterly
        </h6>
        <p>
          +
          10
          %
        </p>
      </div>
      <div
        className="pricing-period active"
        onClick={[Function]}
      >
        <h6>
          Yearly
        </h6>
        <p>
          +
          0
          %
        </p>
      </div>
      <style />
    </div>
  </div>
  <div
    className="pricing-cards-container"
    onClick={[Function]}
    onDragStart={[Function]}
    onMouseDown={[Function]}
    onMouseMove={[Function]}
    onMouseUp={[Function]}
    onTouchEnd={[Function]}
    onTouchMove={[Function]}
    onTouchStart={[Function]}
  >
    <style />
    <style>
      
              .pricing-cards-container {
                transform: translateX(calc(50vw - 150% - 10px - 35px));
                transition: transform 0.5s ease-in-out;
              }

              @media (min-width: 361px) {
                .pricing-cards-container {
                  transform: translateX(calc(50vw - 150% - 10px - 45px));
                }
              }

              @media (min-width: 641px) {
                .pricing-cards-container {
                  transform: translateX(calc(50vw - 150% - 30px - 65px));
                }
              }

              @media (min-width: 1025px) {
                .pricing-cards-container {
                  transform: none;
                  transition: none;
                }
              }
            
    </style>
  </div>
  <div
    className="observation-text"
  />
  <style />
</section>
`;

exports[`productPricingSection renders with background image 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding product-pricing-section"
>
  <div
    className="hero-bg"
  >
    <div
      className="center-content"
      style={
        {
          "height": "100%",
          "width": "100%",
        }
      }
    >
      <img
        alt="Hero Background"
        decoding="async"
        height={3000}
        loading="lazy"
        sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, (max-width: 1282px) 1281px, (max-width: 1682px) 1681px, (max-width: 1922px) 1921px, 2000px"
        src="https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?w=2000&h=3000&auto=format"
        srcSet="https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?rect=1,0,1998,3000&w=361&h=542&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?rect=1,0,1999,3000&w=641&h=962&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?rect=0,1,2000,2999&w=1025&h=1537&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?rect=0,1,2000,2999&w=1281&h=1921&auto=format 1281w, https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?w=1681&h=2522&auto=format 1681w, https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?w=1921&h=2882&auto=format 1921w, https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?w=2000&h=3000&auto=format 2000w"
        style={
          {
            "height": "100%",
            "objectFit": "cover",
            "width": "100%",
          }
        }
        width={2000}
      />
    </div>
    <style>
      
                .hero-bg {
                    position: absolute;
                    height: 100%;
                    width: 100%;
                    z-index: -1;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                }
            
    </style>
  </div>
  <div
    className="pricing-tabs"
  />
  <div
    className="pricing-cards-container"
    onClick={[Function]}
    onDragStart={[Function]}
    onMouseDown={[Function]}
    onMouseMove={[Function]}
    onMouseUp={[Function]}
    onTouchEnd={[Function]}
    onTouchMove={[Function]}
    onTouchStart={[Function]}
  >
    <style />
    <style>
      
              .pricing-cards-container {
                transform: translateX(calc(50vw - 150% - 10px - 35px));
                transition: transform 0.5s ease-in-out;
              }

              @media (min-width: 361px) {
                .pricing-cards-container {
                  transform: translateX(calc(50vw - 150% - 10px - 45px));
                }
              }

              @media (min-width: 641px) {
                .pricing-cards-container {
                  transform: translateX(calc(50vw - 150% - 30px - 65px));
                }
              }

              @media (min-width: 1025px) {
                .pricing-cards-container {
                  transform: none;
                  transition: none;
                }
              }
            
    </style>
  </div>
  <div
    className="observation-text"
  />
  <style />
</section>
`;

exports[`productPricingSection renders without errors 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding product-pricing-section"
>
  <div
    className="pricing-tabs"
  >
    <div
      className="pricing-periods"
    >
      <div
        className="pricing-period"
        onClick={[Function]}
      >
        <h6>
          Monthly
        </h6>
        <p>
          +
          25
          %
        </p>
      </div>
      <div
        className="pricing-period"
        onClick={[Function]}
      >
        <h6>
          Quarterly
        </h6>
        <p>
          +
          10
          %
        </p>
      </div>
      <div
        className="pricing-period active"
        onClick={[Function]}
      >
        <h6>
          Yearly
        </h6>
        <p>
          +
          0
          %
        </p>
      </div>
      <style />
    </div>
    <div
      className="pricing-currencies"
    >
      <div
        className="dropdown-item active"
        onClick={[Function]}
      >
        <div
          className="label-container"
        >
          <h6>
            Euro
          </h6>
          <p>
            €/EUR
          </p>
        </div>
        <span
          className="bp3-icon"
        >
          <div
            height={20}
            src="/v4/svgs/caretDown.svg"
            width="20px"
          />
        </span>
        <style />
      </div>
      <ul
        className=""
      >
        <div
          className="dropdown-item"
          onClick={[Function]}
        >
          <div
            className="label-container"
          >
            <h6>
              Dollar
            </h6>
            <p>
              $/USD
            </p>
          </div>
          <span
            className="bp3-icon"
          >
            <div
              height={20}
              src="/v4/svgs/.svg"
              width="20px"
            />
          </span>
          <style />
        </div>
      </ul>
      <style />
    </div>
  </div>
  <div
    className="pricing-cards-container"
    onClick={[Function]}
    onDragStart={[Function]}
    onMouseDown={[Function]}
    onMouseMove={[Function]}
    onMouseUp={[Function]}
    onTouchEnd={[Function]}
    onTouchMove={[Function]}
    onTouchStart={[Function]}
  >
    <div
      className="rf-cell pricing-card"
    >
      <div
        className="rf-flex-container pricing-card-content"
      >
        <h4
          className="h4 pricing-card-heading"
        >
          Basic
        </h4>
        <div
          className="p-small"
        >
          <p>
            What you need to kickstart your mobility
          </p>
        </div>
        <div
          className="product-price"
        >
          5,00 €
        </div>
        <style />
        <div
          className="pricing-condition"
        >
          per vehicle per month, billed annually
        </div>
        <div
          className="call-to-action small"
        >
          <a
            className="center-content  hover-enabled"
            rel="noreferrer"
            target="_blank"
          >
            Free Trial
            <style />
          </a>
          <style />
        </div>
        <div
          className="product-features"
        >
          <div
            className="product-features-container"
          >
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Book vehicles with your computer or phone
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Add vehicles so they can be shared
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Calendar integration
              </p>
            </div>
          </div>
        </div>
        <style />
        <style />
      </div>
      <style />
    </div>
    <div
      className="rf-cell pricing-card"
    >
      <div
        className="center-content most-popular-title"
      >
        <span>
          carsharing.prices.mostPopular
        </span>
      </div>
      <div
        className="rf-flex-container pricing-card-content most-popular"
      >
        <h4
          className="h4 pricing-card-heading"
        >
          Pro
        </h4>
        <div
          className="p-small"
        >
          <p>
            Unlock the power of analytics for your fleet
          </p>
        </div>
        <div
          className="product-price"
        >
          10,00 €
        </div>
        <style />
        <div
          className="pricing-condition"
        >
          per vehicle per month, billed anually
        </div>
        <div
          className="call-to-action small"
        >
          <a
            className="center-content  hover-enabled"
            rel="noreferrer"
            target="_blank"
          >
            Free Trial
            <style />
          </a>
          <style />
        </div>
        <div
          className="product-features"
        >
          <p
            className="plus"
          >
            Everything in the Basic package plus:
          </p>
          <div
            className="product-features-container"
          >
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Booking behaviour statistics
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Vehicle utilization data
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Excel compatible data export
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Be informed of damages to the vehicle
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Manage the process of exchanging keys
              </p>
            </div>
          </div>
        </div>
        <style />
        <style />
      </div>
      <style />
    </div>
    <div
      className="rf-cell pricing-card"
    >
      <div
        className="rf-flex-container pricing-card-content"
      >
        <h4
          className="h4 pricing-card-heading"
        >
          Enterprise
        </h4>
        <div
          className="p-small"
        >
          <p>
            Automate part of your vehicle management
          </p>
        </div>
        <div
          className="product-price"
        >
          15,00 €
        </div>
        <style />
        <div
          className="pricing-condition"
        >
          per vehicle per month, billed anually
        </div>
        <div
          className="call-to-action small"
        >
          <a
            className="center-content  hover-enabled"
            rel="noreferrer"
            target="_blank"
          >
            Free Trial
            <style />
          </a>
          <style />
        </div>
        <div
          className="product-features"
        >
          <p
            className="plus"
          >
            Everything in the Pro package plus:
          </p>
          <div
            className="product-features-container"
          >
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Book vehicles according to categories
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Minimize late returns with booking buffers
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Automatic re-booking in cases of late return
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Manage fuel cards and pins in the system
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Your users can have access to the fuel cards and code with the app
              </p>
            </div>
          </div>
        </div>
        <style />
        <style />
      </div>
      <style />
    </div>
    <style />
    <style>
      
              .pricing-cards-container {
                transform: translateX(calc(50vw - 150% - 10px - 35px));
                transition: transform 0.5s ease-in-out;
              }

              @media (min-width: 361px) {
                .pricing-cards-container {
                  transform: translateX(calc(50vw - 150% - 10px - 45px));
                }
              }

              @media (min-width: 641px) {
                .pricing-cards-container {
                  transform: translateX(calc(50vw - 150% - 30px - 65px));
                }
              }

              @media (min-width: 1025px) {
                .pricing-cards-container {
                  transform: none;
                  transition: none;
                }
              }
            
    </style>
  </div>
  <div
    className="observation-text"
  />
  <style />
</section>
`;

exports[`productPricingSection renders without pricing currencies when only one is provided 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding product-pricing-section"
>
  <div
    className="pricing-tabs"
  >
    <div
      className="pricing-periods"
    >
      <div
        className="pricing-period"
        onClick={[Function]}
      >
        <h6>
          Monthly
        </h6>
        <p>
          +
          25
          %
        </p>
      </div>
      <div
        className="pricing-period"
        onClick={[Function]}
      >
        <h6>
          Quarterly
        </h6>
        <p>
          +
          10
          %
        </p>
      </div>
      <div
        className="pricing-period active"
        onClick={[Function]}
      >
        <h6>
          Yearly
        </h6>
        <p>
          +
          0
          %
        </p>
      </div>
      <style />
    </div>
  </div>
  <div
    className="pricing-cards-container"
    onClick={[Function]}
    onDragStart={[Function]}
    onMouseDown={[Function]}
    onMouseMove={[Function]}
    onMouseUp={[Function]}
    onTouchEnd={[Function]}
    onTouchMove={[Function]}
    onTouchStart={[Function]}
  >
    <div
      className="rf-cell pricing-card"
    >
      <div
        className="rf-flex-container pricing-card-content"
      >
        <h4
          className="h4 pricing-card-heading"
        >
          Basic
        </h4>
        <div
          className="p-small"
        >
          <p>
            What you need to kickstart your mobility
          </p>
        </div>
        <div
          className="product-price"
        >
          15,00 €
        </div>
        <style />
        <div
          className="pricing-condition"
        >
          per vehicle per month, billed annually
        </div>
        <div
          className="call-to-action small"
        >
          <a
            className="center-content  hover-enabled"
            rel="noreferrer"
            target="_blank"
          >
            Free Trial
            <style />
          </a>
          <style />
        </div>
        <div
          className="product-features"
        >
          <div
            className="product-features-container"
          >
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Book vehicles with your computer or phone
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Add vehicles so they can be shared
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Calendar integration
              </p>
            </div>
          </div>
        </div>
        <style />
        <style />
      </div>
      <style />
    </div>
    <style />
    <style>
      
              .pricing-cards-container {
                transform: translateX(calc(50vw - 150% - 10px - 35px));
                transition: transform 0.5s ease-in-out;
              }

              @media (min-width: 361px) {
                .pricing-cards-container {
                  transform: translateX(calc(50vw - 150% - 10px - 45px));
                }
              }

              @media (min-width: 641px) {
                .pricing-cards-container {
                  transform: translateX(calc(50vw - 150% - 30px - 65px));
                }
              }

              @media (min-width: 1025px) {
                .pricing-cards-container {
                  transform: none;
                  transition: none;
                }
              }
            
    </style>
  </div>
  <div
    className="observation-text"
  />
  <style />
</section>
`;

exports[`productPricingSection renders without pricing periods 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding product-pricing-section"
>
  <div
    className="pricing-tabs"
  >
    <div
      className="pricing-currencies"
    >
      <div
        className="dropdown-item active"
        onClick={[Function]}
      >
        <div
          className="label-container"
        >
          <h6>
            Euro
          </h6>
          <p>
            €/EUR
          </p>
        </div>
        <span
          className="bp3-icon"
        >
          <div
            height={20}
            src="/v4/svgs/caretDown.svg"
            width="20px"
          />
        </span>
        <style />
      </div>
      <ul
        className=""
      >
        <div
          className="dropdown-item"
          onClick={[Function]}
        >
          <div
            className="label-container"
          >
            <h6>
              Dollar
            </h6>
            <p>
              $/USD
            </p>
          </div>
          <span
            className="bp3-icon"
          >
            <div
              height={20}
              src="/v4/svgs/.svg"
              width="20px"
            />
          </span>
          <style />
        </div>
      </ul>
      <style />
    </div>
  </div>
  <div
    className="pricing-cards-container"
    onClick={[Function]}
    onDragStart={[Function]}
    onMouseDown={[Function]}
    onMouseMove={[Function]}
    onMouseUp={[Function]}
    onTouchEnd={[Function]}
    onTouchMove={[Function]}
    onTouchStart={[Function]}
  >
    <div
      className="rf-cell pricing-card"
    >
      <div
        className="rf-flex-container pricing-card-content"
      >
        <h4
          className="h4 pricing-card-heading"
        >
          Basic
        </h4>
        <div
          className="p-small"
        >
          <p>
            What you need to kickstart your mobility
          </p>
        </div>
        <div
          className="product-price"
        >
          5,00 €
        </div>
        <style />
        <div
          className="pricing-condition"
        >
          per vehicle per month, billed annually
        </div>
        <div
          className="call-to-action small"
        >
          <a
            className="center-content  hover-enabled"
            rel="noreferrer"
            target="_blank"
          >
            Free Trial
            <style />
          </a>
          <style />
        </div>
        <div
          className="product-features"
        >
          <div
            className="product-features-container"
          >
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Book vehicles with your computer or phone
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Add vehicles so they can be shared
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Calendar integration
              </p>
            </div>
          </div>
        </div>
        <style />
        <style />
      </div>
      <style />
    </div>
    <div
      className="rf-cell pricing-card"
    >
      <div
        className="center-content most-popular-title"
      >
        <span>
          carsharing.prices.mostPopular
        </span>
      </div>
      <div
        className="rf-flex-container pricing-card-content most-popular"
      >
        <h4
          className="h4 pricing-card-heading"
        >
          Pro
        </h4>
        <div
          className="p-small"
        >
          <p>
            Unlock the power of analytics for your fleet
          </p>
        </div>
        <div
          className="product-price"
        >
          10,00 €
        </div>
        <style />
        <div
          className="pricing-condition"
        >
          per vehicle per month, billed anually
        </div>
        <div
          className="call-to-action small"
        >
          <a
            className="center-content  hover-enabled"
            rel="noreferrer"
            target="_blank"
          >
            Free Trial
            <style />
          </a>
          <style />
        </div>
        <div
          className="product-features"
        >
          <p
            className="plus"
          >
            Everything in the Basic package plus:
          </p>
          <div
            className="product-features-container"
          >
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Booking behaviour statistics
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Vehicle utilization data
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Excel compatible data export
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Be informed of damages to the vehicle
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Manage the process of exchanging keys
              </p>
            </div>
          </div>
        </div>
        <style />
        <style />
      </div>
      <style />
    </div>
    <div
      className="rf-cell pricing-card"
    >
      <div
        className="rf-flex-container pricing-card-content"
      >
        <h4
          className="h4 pricing-card-heading"
        >
          Enterprise
        </h4>
        <div
          className="p-small"
        >
          <p>
            Automate part of your vehicle management
          </p>
        </div>
        <div
          className="product-price"
        >
          15,00 €
        </div>
        <style />
        <div
          className="pricing-condition"
        >
          per vehicle per month, billed anually
        </div>
        <div
          className="call-to-action small"
        >
          <a
            className="center-content  hover-enabled"
            rel="noreferrer"
            target="_blank"
          >
            Free Trial
            <style />
          </a>
          <style />
        </div>
        <div
          className="product-features"
        >
          <p
            className="plus"
          >
            Everything in the Pro package plus:
          </p>
          <div
            className="product-features-container"
          >
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Book vehicles according to categories
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Minimize late returns with booking buffers
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Automatic re-booking in cases of late return
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Manage fuel cards and pins in the system
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Your users can have access to the fuel cards and code with the app
              </p>
            </div>
          </div>
        </div>
        <style />
        <style />
      </div>
      <style />
    </div>
    <style />
    <style>
      
              .pricing-cards-container {
                transform: translateX(calc(50vw - 150% - 10px - 35px));
                transition: transform 0.5s ease-in-out;
              }

              @media (min-width: 361px) {
                .pricing-cards-container {
                  transform: translateX(calc(50vw - 150% - 10px - 45px));
                }
              }

              @media (min-width: 641px) {
                .pricing-cards-container {
                  transform: translateX(calc(50vw - 150% - 30px - 65px));
                }
              }

              @media (min-width: 1025px) {
                .pricing-cards-container {
                  transform: none;
                  transition: none;
                }
              }
            
    </style>
  </div>
  <div
    className="observation-text"
  />
  <style />
</section>
`;

exports[`productPricingSection selects dollar as default currency 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding product-pricing-section"
>
  <div
    className="pricing-tabs"
  >
    <div
      className="pricing-periods"
    >
      <div
        className="pricing-period"
        onClick={[Function]}
      >
        <h6>
          Monthly
        </h6>
        <p>
          +
          25
          %
        </p>
      </div>
      <div
        className="pricing-period"
        onClick={[Function]}
      >
        <h6>
          Quarterly
        </h6>
        <p>
          +
          10
          %
        </p>
      </div>
      <div
        className="pricing-period active"
        onClick={[Function]}
      >
        <h6>
          Yearly
        </h6>
        <p>
          +
          0
          %
        </p>
      </div>
      <style />
    </div>
    <div
      className="pricing-currencies"
    >
      <div
        className="dropdown-item active"
        onClick={[Function]}
      >
        <div
          className="label-container"
        >
          <h6>
            Dollar
          </h6>
          <p>
            $/USD
          </p>
        </div>
        <span
          className="bp3-icon"
        >
          <div
            height={20}
            src="/v4/svgs/caretDown.svg"
            width="20px"
          />
        </span>
        <style />
      </div>
      <ul
        className=""
      >
        <div
          className="dropdown-item"
          onClick={[Function]}
        >
          <div
            className="label-container"
          >
            <h6>
              Euro
            </h6>
            <p>
              €/EUR
            </p>
          </div>
          <span
            className="bp3-icon"
          >
            <div
              height={20}
              src="/v4/svgs/.svg"
              width="20px"
            />
          </span>
          <style />
        </div>
      </ul>
      <style />
    </div>
  </div>
  <div
    className="pricing-cards-container"
    onClick={[Function]}
    onDragStart={[Function]}
    onMouseDown={[Function]}
    onMouseMove={[Function]}
    onMouseUp={[Function]}
    onTouchEnd={[Function]}
    onTouchMove={[Function]}
    onTouchStart={[Function]}
  >
    <div
      className="rf-cell pricing-card"
    >
      <div
        className="rf-flex-container pricing-card-content"
      >
        <h4
          className="h4 pricing-card-heading"
        >
          Basic
        </h4>
        <div
          className="p-small"
        >
          <p>
            What you need to kickstart your mobility
          </p>
        </div>
        <div
          className="product-price"
        >
          8,00 $
        </div>
        <style />
        <div
          className="pricing-condition"
        >
          per vehicle per month, billed annually
        </div>
        <div
          className="call-to-action small"
        >
          <a
            className="center-content  hover-enabled"
            rel="noreferrer"
            target="_blank"
          >
            Free Trial
            <style />
          </a>
          <style />
        </div>
        <div
          className="product-features"
        >
          <div
            className="product-features-container"
          >
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Book vehicles with your computer or phone
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Add vehicles so they can be shared
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Calendar integration
              </p>
            </div>
          </div>
        </div>
        <style />
        <style />
      </div>
      <style />
    </div>
    <div
      className="rf-cell pricing-card"
    >
      <div
        className="center-content most-popular-title"
      >
        <span>
          carsharing.prices.mostPopular
        </span>
      </div>
      <div
        className="rf-flex-container pricing-card-content most-popular"
      >
        <h4
          className="h4 pricing-card-heading"
        >
          Pro
        </h4>
        <div
          className="p-small"
        >
          <p>
            Unlock the power of analytics for your fleet
          </p>
        </div>
        <div
          className="product-price"
        >
          16,00 $
        </div>
        <style />
        <div
          className="pricing-condition"
        >
          per vehicle per month, billed anually
        </div>
        <div
          className="call-to-action small"
        >
          <a
            className="center-content  hover-enabled"
            rel="noreferrer"
            target="_blank"
          >
            Free Trial
            <style />
          </a>
          <style />
        </div>
        <div
          className="product-features"
        >
          <p
            className="plus"
          >
            Everything in the Basic package plus:
          </p>
          <div
            className="product-features-container"
          >
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Booking behaviour statistics
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Vehicle utilization data
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Excel compatible data export
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Be informed of damages to the vehicle
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Manage the process of exchanging keys
              </p>
            </div>
          </div>
        </div>
        <style />
        <style />
      </div>
      <style />
    </div>
    <div
      className="rf-cell pricing-card"
    >
      <div
        className="rf-flex-container pricing-card-content"
      >
        <h4
          className="h4 pricing-card-heading"
        >
          Enterprise
        </h4>
        <div
          className="p-small"
        >
          <p>
            Automate part of your vehicle management
          </p>
        </div>
        <div
          className="product-price"
        >
          25,00 $
        </div>
        <style />
        <div
          className="pricing-condition"
        >
          per vehicle per month, billed anually
        </div>
        <div
          className="call-to-action small"
        >
          <a
            className="center-content  hover-enabled"
            rel="noreferrer"
            target="_blank"
          >
            Free Trial
            <style />
          </a>
          <style />
        </div>
        <div
          className="product-features"
        >
          <p
            className="plus"
          >
            Everything in the Pro package plus:
          </p>
          <div
            className="product-features-container"
          >
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Book vehicles according to categories
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Minimize late returns with booking buffers
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Automatic re-booking in cases of late return
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Manage fuel cards and pins in the system
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Your users can have access to the fuel cards and code with the app
              </p>
            </div>
          </div>
        </div>
        <style />
        <style />
      </div>
      <style />
    </div>
    <style />
    <style>
      
              .pricing-cards-container {
                transform: translateX(calc(50vw - 150% - 10px - 35px));
                transition: transform 0.5s ease-in-out;
              }

              @media (min-width: 361px) {
                .pricing-cards-container {
                  transform: translateX(calc(50vw - 150% - 10px - 45px));
                }
              }

              @media (min-width: 641px) {
                .pricing-cards-container {
                  transform: translateX(calc(50vw - 150% - 30px - 65px));
                }
              }

              @media (min-width: 1025px) {
                .pricing-cards-container {
                  transform: none;
                  transition: none;
                }
              }
            
    </style>
  </div>
  <div
    className="observation-text"
  />
  <style />
</section>
`;

exports[`productPricingSection selects euro as default currency when dollar not available 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding product-pricing-section"
>
  <div
    className="pricing-tabs"
  >
    <div
      className="pricing-periods"
    >
      <div
        className="pricing-period"
        onClick={[Function]}
      >
        <h6>
          Monthly
        </h6>
        <p>
          +
          25
          %
        </p>
      </div>
      <div
        className="pricing-period"
        onClick={[Function]}
      >
        <h6>
          Quarterly
        </h6>
        <p>
          +
          10
          %
        </p>
      </div>
      <div
        className="pricing-period active"
        onClick={[Function]}
      >
        <h6>
          Yearly
        </h6>
        <p>
          +
          0
          %
        </p>
      </div>
      <style />
    </div>
  </div>
  <div
    className="pricing-cards-container"
    onClick={[Function]}
    onDragStart={[Function]}
    onMouseDown={[Function]}
    onMouseMove={[Function]}
    onMouseUp={[Function]}
    onTouchEnd={[Function]}
    onTouchMove={[Function]}
    onTouchStart={[Function]}
  >
    <div
      className="rf-cell pricing-card"
    >
      <div
        className="rf-flex-container pricing-card-content"
      >
        <h4
          className="h4 pricing-card-heading"
        >
          Basic
        </h4>
        <div
          className="p-small"
        >
          <p>
            What you need to kickstart your mobility
          </p>
        </div>
        <div
          className="product-price"
        >
          15,00 €
        </div>
        <style />
        <div
          className="pricing-condition"
        >
          per vehicle per month, billed annually
        </div>
        <div
          className="call-to-action small"
        >
          <a
            className="center-content  hover-enabled"
            rel="noreferrer"
            target="_blank"
          >
            Free Trial
            <style />
          </a>
          <style />
        </div>
        <div
          className="product-features"
        >
          <div
            className="product-features-container"
          >
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Book vehicles with your computer or phone
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Add vehicles so they can be shared
              </p>
            </div>
            <div
              className="product-feature p-small"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={16}
                  src="/v4/svgs/check.svg"
                  width="16px"
                />
              </span>
              <p>
                Calendar integration
              </p>
            </div>
          </div>
        </div>
        <style />
        <style />
      </div>
      <style />
    </div>
    <style />
    <style>
      
              .pricing-cards-container {
                transform: translateX(calc(50vw - 150% - 10px - 35px));
                transition: transform 0.5s ease-in-out;
              }

              @media (min-width: 361px) {
                .pricing-cards-container {
                  transform: translateX(calc(50vw - 150% - 10px - 45px));
                }
              }

              @media (min-width: 641px) {
                .pricing-cards-container {
                  transform: translateX(calc(50vw - 150% - 30px - 65px));
                }
              }

              @media (min-width: 1025px) {
                .pricing-cards-container {
                  transform: none;
                  transition: none;
                }
              }
            
    </style>
  </div>
  <div
    className="observation-text"
  />
  <style />
</section>
`;
