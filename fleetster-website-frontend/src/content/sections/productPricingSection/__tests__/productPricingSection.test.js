import React from 'react';

import {ProductPricingSection} from '../productPricingSection';
import {PricingCard} from '../pricingCard';
import {DOLLAR_CURRENCY_KEY, EURO_CURRENCY_KEY} from '../constants';
import {currencies} from '../priceHelpers';

const pricings = [
    {
        _key:         '09d4df6a165b',
        _type:        'pricingCard',
        callToAction: {
            _type: 'callToAction',
            route: {
                _ref:  '3f96221c-5ffc-4bc2-aa84-021d84e5290a',
                _type: 'reference'
            },
            title: 'Free Trial'
        },
        condition:   'per vehicle per month, billed annually',
        description: 'What you need to kickstart your mobility',
        features:    [
            'Book vehicles with your computer or phone',
            'Add vehicles so they can be shared',
            'Calendar integration'
        ],
        heading: 'Basic',
        prices:  [
            {
                value:    5,
                currency: EURO_CURRENCY_KEY
            },
            {
                value:    8,
                currency: DOLLAR_CURRENCY_KEY
            }
        ]
    },
    {
        _key:         '54a2bf598a85',
        _type:        'pricingCard',
        callToAction: {
            _type: 'callToAction',
            route: {
                _ref:  '4d26d21e-d543-4e41-85e0-63163a762c02',
                _type: 'reference'
            },
            title: 'Free Trial'
        },
        condition:   'per vehicle per month, billed anually',
        description: 'Unlock the power of analytics for your fleet',
        features:    [
            'Booking behaviour statistics',
            'Vehicle utilization data',
            'Excel compatible data export',
            'Be informed of damages to the vehicle',
            'Manage the process of exchanging keys'
        ],
        heading:       'Pro',
        plus:          'Everything in the Basic package plus:',
        isMostPopular: true,
        prices:        [
            {
                value:    10,
                currency: EURO_CURRENCY_KEY
            },
            {
                value:    16,
                currency: DOLLAR_CURRENCY_KEY
            }
        ]
    },
    {
        _key:         'e95bb24bed5f',
        _type:        'pricingCard',
        callToAction: {
            _type: 'callToAction',
            route: {
                _ref:  '747cd4b6-0477-43f3-90ef-09fef0cbe793',
                _type: 'reference'
            },
            title: 'Free Trial'
        },
        condition:   'per vehicle per month, billed anually',
        description: 'Automate part of your vehicle management',
        features:    [
            'Book vehicles according to categories',
            'Minimize late returns with booking buffers',
            'Automatic re-booking in cases of late return',
            'Manage fuel cards and pins in the system',
            'Your users can have access to the fuel cards and code with the app'
        ],
        heading: 'Enterprise',
        plus:    'Everything in the Pro package plus:',
        prices:  [
            {
                value:    15,
                currency: EURO_CURRENCY_KEY
            },
            {
                value:    25,
                currency: DOLLAR_CURRENCY_KEY
            }
        ]
    }
];

const pricingPeriods = [
    {_key: 1, label: 'Monthly', percentage: 25, default: false},
    {_key: 2, label: 'Quarterly', percentage: 10, default: false},
    {_key: 3, label: 'Yearly', percentage: 0, default: true}
];

const bgImage = {
    _type: 'image',
    asset: {
        _ref:  'image-Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000-jpg',
        _type: 'reference'
    },
    height: 3000,
    width:  2000
};

Object.defineProperty(window.navigator, 'languages', {
    value:    ['de-DE'],
    writable: true
});

describe('productPricingSection', () => {
    test('renders without errors', () => {
        const component = render(<ProductPricingSection periods={pricingPeriods} pricings={pricings}/>);

        expect(component).toMatchSnapshot();
    });

    test('renders the most popular pricing', () => {
        const component = render(<ProductPricingSection periods={pricingPeriods} pricings={pricings}/>);

        expect(component.root.findAllByProps({className: 'center-content most-popular-title'})).toHaveLength(1);
    });

    test('renders even without pricings', () => {
        const component = render(<ProductPricingSection periods={pricingPeriods}/>);

        expect(component).toMatchSnapshot();
    });

    test('renders with background image', () => {
        const component = render(<ProductPricingSection periods={[]} pricings={[]} backgroundImage={bgImage}/>);

        expect(component).toMatchSnapshot();
    });

    test('takes only first 3 pricings', () => {
        const component = render(<ProductPricingSection
            periods={pricingPeriods}
            pricings={[...pricings, ...pricings]}
            backgroundImage={bgImage}/>);

        expect(component.root.findAllByType(PricingCard)).toHaveLength(3);
    });

    test('renders without pricing periods', () => {
        const component = render(<ProductPricingSection pricings={pricings}/>);

        expect(component).toMatchSnapshot();
    });

    test('renders without pricing currencies when only one is provided', () => {
        const noDollarCurrencyPricings = [{...pricings[0], prices: [{
            value:    15,
            currency: EURO_CURRENCY_KEY
        }]}];
        const component = render(<ProductPricingSection periods={pricingPeriods} pricings={noDollarCurrencyPricings}/>);

        expect(component).toMatchSnapshot();
    });

    test('takes only first 3 pricing periods', () => {
        let component;

        act(() => {
            component = render(<ProductPricingSection
                periods={[...pricingPeriods, ...pricingPeriods]}
                pricings={pricings}/>);
        });

        const pricingPeriodsComponent = component.root.findAllByProps({className: 'pricing-periods'});

        expect(pricingPeriodsComponent).toHaveLength(1);

        const activePeriods = component.root.findAllByProps({className: 'pricing-period active'});
        const otherPeriods = component.root.findAllByProps({className: 'pricing-period'});

        expect(activePeriods).toHaveLength(1);
        expect(otherPeriods).toHaveLength(2);
    });

    test('clicking a pricing period changes the values', () => {
        let component;

        act(() => {
            component = render(<ProductPricingSection periods={pricingPeriods} pricings={pricings}/>);
        });

        const otherPeriods = component.root.findAllByProps({className: 'pricing-period'});
        otherPeriods[0].props.onClick();

        expect(component).toMatchSnapshot();
    });

    test('renders all currencies in currency menu', () => {
        let component;

        act(() => {
            component = render(<ProductPricingSection periods={pricingPeriods} pricings={pricings}/>);
        });

        const active = component.root.findAllByProps({className: 'dropdown-item active'});
        const other = component.root.findAllByProps({className: 'dropdown-item'});

        expect(active).toHaveLength(1);
        expect(other).toHaveLength(_.keys(currencies).length - 1);
    });

    test('clicking the selected currency opens the menu', () => {
        let component;

        act(() => {
            component = render(<ProductPricingSection periods={pricingPeriods} pricings={pricings}/>);
        });

        const active = component.root.findAllByProps({className: 'dropdown-item active'});

        active[0].props.onClick();

        expect(component.root.findByProps({className: 'menu-open'})).toBeDefined();
    });

    test('clicking a different currency changes the values', () => {
        let component;

        act(() => {
            component = render(<ProductPricingSection periods={pricingPeriods} pricings={pricings}/>);
        });

        const other = component.root.findAllByProps({className: 'dropdown-item'});
        other[0].props.onClick();

        expect(component).toMatchSnapshot();
    });

    test('selects dollar as default currency', () => {
        let component;

        Object.defineProperty(window.navigator, 'languages', {
            value:    ['en-US'],
            writable: true
        });

        act(() => {
            component = render(<ProductPricingSection periods={pricingPeriods} pricings={pricings}/>);
        });

        expect(component).toMatchSnapshot();
    });

    test('selects euro as default currency when dollar not available', () => {
        let component;

        Object.defineProperty(window.navigator, 'languages', {
            value:    ['en-US'],
            writable: true
        });

        const noDollarCurrencyPricings = [{...pricings[0], prices: [{
            value:    15,
            currency: EURO_CURRENCY_KEY
        }]}];

        act(() => {
            component = render(<ProductPricingSection periods={pricingPeriods} pricings={noDollarCurrencyPricings}/>);
        });

        expect(component).toMatchSnapshot();
    });

    test('does not fail to render pricing content for unknown provided currency', () => {
        let component;

        const unknownCurrencyPricings = [{...pricings[0], prices: [{
            value:    30,
            currency: 'unknown'
        }]}];

        act(() => {
            component = render(<ProductPricingSection periods={pricingPeriods} pricings={unknownCurrencyPricings}/>);
        });

        expect(component).toMatchSnapshot();
    });
});
