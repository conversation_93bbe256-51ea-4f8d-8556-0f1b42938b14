import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {colors, screenSizes, vSpacing} from 'rf-styles';
import {FleetsterIcon} from 'rf-icon';

const styles = css`
  .product-features {
    margin-top: ${vSpacing.s}px;
    padding: 0 20px;  
  }
  
  @media (min-width: ${screenSizes.l}px) {
    .product-features {
      padding: 0 23px;
    }
  }
  @media (min-width: ${screenSizes.xl}px) {
    .product-features {
      padding: 0 92px;
    }
  }

  .product-features .plus {
    font-size: 14px;
    line-height: 18px;
    text-align: center;
    color: ${colors.blue};
    margin-bottom: ${vSpacing.s}px;
  }

  .product-features-container {
    display: flex;
    flex-flow: column;
  }

  .product-feature {
    display: flex;
    text-align: left;
    align-items: flex-start;
    margin-bottom: 10px;
  }

  @media (min-width: ${screenSizes.xxs}px) {
    .product-feature {
      width: 100%;
    }
  }

  @media (min-width: ${screenSizes.xs - 1}px) {
    .product-features-container {
      flex-flow: wrap;
    }

    .product-feature {
      width: 50%;
    }
  }

  @media (min-width: ${screenSizes.s}px) {
    .product-feature {
      width: 100%;
    }
  }

  @media (min-width: ${screenSizes.m}px) {
    .product-feature {
      width: 50%;
    }
  }
`;

export function Features({plus, features}) {
    return (
        <>
            <div className={'product-features'}>
                {plus && <p className={'plus'}>{plus}</p>}
                <div className={'product-features-container'}>
                    {features.map(feature => (<div key={feature} className={'product-feature p-small'}>
                        <FleetsterIcon
                            iconSize={16}
                            customStyles={{color: colors.blue, marginRight: '8px', marginTop: '5px'}}
                            icon={'check'}/>
                        <p>{feature}</p>
                    </div>))}
                </div>
            </div>
            <style jsx>{styles}</style>
        </>
    );
}

Features.propTypes = {
    plus:     PropTypes.string,
    features: PropTypes.array
};
