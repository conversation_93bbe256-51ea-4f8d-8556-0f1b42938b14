import React from 'react';
import _ from 'lodash';
import css from 'styled-jsx/css';

import {colors, fontWeights} from 'rf-styles';

const styles = css`
  .pricing-periods {
    display: flex;
    border-radius: 32px;
    background-color: ${colors.white};
    max-width: 320px;
    height: 50px;
    align-items: center;
    user-select: none;
    flex-grow: 1;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.15);
  }

  .pricing-period {
    flex-grow: 1;
    height: calc(100% - 10px);
    width: 33.33%;
    border: 5px solid white;
    border-radius: 32px;
    cursor: pointer;
    text-align: center;
    color: ${colors.blue};
    background-color: ${colors.white};
    transition: background-color ease-in 0.1s;
  }
  
  .pricing-period h6 {
    margin-top: 4px;
    margin-bottom: 0;
    font-size: 14px;
    line-height: 16px;
    font-weight: ${fontWeights.bold};
  }

  .pricing-period p {
    color: ${colors.blue};
    font-size: 12px;
    line-height: 16px;
    font-weight: ${fontWeights.semiBold};
    margin: 0;
  }

  .active {
    color: ${colors.white};
    background-color: ${colors.blue};
  }
  
  .active p {
    color: ${colors.white};
  }
`;

export function PricingPeriod({periods, selected, set}) {
    if (periods.length > 1) {
        return (<div className={'pricing-periods'}>
            {_.take(periods, 3).map(p => (<div
                onClick={() => set(p)}
                className={`pricing-period${selected?._key === p._key ? ' active' : ''}`}
                key={p.key}>
                <h6>{p.label}</h6>
                <p>+{p.percentage}%</p>
            </div>))}
            <style jsx>{styles}</style>
        </div>);
    }

    return null;
}
