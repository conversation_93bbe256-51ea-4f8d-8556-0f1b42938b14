import React, {Children} from 'react';
import css from 'styled-jsx/css';
import PropTypes from 'prop-types';

import {hSpacing, screenSizes, vSpacing} from 'rf-styles';
import {useCarouselContext, useHorizontalSwipeEvent} from '../hero/carousel';

const CARD_COLUMN_GAP_LARGE = hSpacing.m;
const CARD_COLUMN_GAP_SMALL = hSpacing.xs;

const styles = css`
  .pricing-cards-container {
    display: flex;
    column-gap: ${CARD_COLUMN_GAP_SMALL}px;
    padding: ${vSpacing.s}px 0;
    width: 100%;
  }

  @media (min-width: ${screenSizes.xxs}px) {
    .pricing-cards-container {
      flex-direction: row;
    }
  }
  @media (min-width: ${screenSizes.xs}px) {
    .pricing-cards-container {
      column-gap: ${CARD_COLUMN_GAP_LARGE}px;
      width: 66.66%;
    }
  }
  @media (min-width: ${screenSizes.s}px) {
    .pricing-cards-container {
      flex-wrap: nowrap;
      touch-action: none;
      width: unset;
    }
  }
`;

function calculateX(currentIndex, columnGap = CARD_COLUMN_GAP_SMALL) {
    return `50vw - ${50 + (currentIndex * 100)}% - ${columnGap * currentIndex}px`;
}

export function PricingCarousel({children}) {
    const {goToPrevNoLooping, goToNextNoLooping, currentIndex} = useCarouselContext();
    const touchHandlers = useHorizontalSwipeEvent({
        onLeftSwipe:  goToNextNoLooping,
        onRightSwipe: goToPrevNoLooping
    });

    return (
        <div className={'pricing-cards-container'} {...touchHandlers}>
            {Children.map(children, child => <>{child}</>)}
            <style jsx>{styles}</style>
            <style jsx>{`
              .pricing-cards-container {
                transform: translateX(calc(${calculateX(currentIndex)} - 35px));
                transition: transform 0.5s ease-in-out;
              }

              @media (min-width: ${screenSizes.xxs}px) {
                .pricing-cards-container {
                  transform: translateX(calc(${calculateX(currentIndex)} - 45px));
                }
              }

              @media (min-width: ${screenSizes.xs}px) {
                .pricing-cards-container {
                  transform: translateX(calc(${calculateX(currentIndex, CARD_COLUMN_GAP_LARGE)} - 65px));
                }
              }

              @media (min-width: ${screenSizes.s}px) {
                .pricing-cards-container {
                  transform: none;
                  transition: none;
                }
              }
            `}
            </style>
        </div>
    );
}

PricingCarousel.propTypes = {children: PropTypes.array};
