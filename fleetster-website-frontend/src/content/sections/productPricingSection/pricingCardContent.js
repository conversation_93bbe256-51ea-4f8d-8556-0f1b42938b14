import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {colors, vSpacing, fontWeights, hSpacing} from 'rf-styles';
import {CallToAction} from 'rf-components';

import {Price} from './price';
import {Features} from './features';
import {getCalculatedPrice, getCurrentPriceAndCurrency} from './priceHelpers';

const styles = css`
  .pricing-card-content {
    display: flex;
    background-color: white;
    flex-direction: column;
    text-align: center;
    color: ${colors.darkGray};
    padding: 20px;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    flex-grow: 1;
  }

  .pricing-card-heading {
    font-weight: ${fontWeights.regular};
    margin-top: 0;
    margin-bottom: ${hSpacing.xs}px;
  }

  .pricing-condition {
    margin-bottom: ${vSpacing.s}px;
    color: ${colors.blue};
    font-size: 12px;
    line-height: 12px;
  }

  .most-popular {
    border: 2px solid ${colors.blue};
    background-color: ${colors.white}
  }
}
`;

export function PricingCardContent({
    isMostPopular,
    heading,
    description,
    pricePercentage,
    priceCurrency,
    prices,
    condition,
    callToAction,
    plus,
    features = []
}) {
    const mostPopularClass = isMostPopular ? ' most-popular' : '';
    const currentPrice = getCurrentPriceAndCurrency(prices, priceCurrency);

    return (
        <div className={`rf-flex-container pricing-card-content${mostPopularClass}`}>
            <h4 className={'h4 pricing-card-heading'}>{heading}</h4>
            <div className={'p-small'}>
                <p>{description}</p>
            </div>
            <Price price={getCalculatedPrice(pricePercentage, currentPrice?.value)}
                currency={currentPrice?.currency}/>
            <div className={'pricing-condition'}>{condition}</div>
            <CallToAction small {...callToAction}/>
            <Features plus={plus} features={features}/>
            <style jsx>{styles}</style>
        </div>
    );
}

PricingCardContent.propTypes = {
    isMostPopular: PropTypes.bool,
    heading:       PropTypes.string,
    description:   PropTypes.string,
    price:         PropTypes.string,
    condition:     PropTypes.string,
    callToAction:  PropTypes.object,
    plus:          PropTypes.string,
    features:      PropTypes.array
};
