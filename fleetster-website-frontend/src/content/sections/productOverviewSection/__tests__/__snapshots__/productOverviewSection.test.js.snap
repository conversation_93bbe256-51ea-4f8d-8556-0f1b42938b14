// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`productOverviewSection it renders correctly 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding"
>
  <div
    className="modules-menu-container rf-flex-container"
  >
    <div
      className="module-card "
    >
      <div
        className="image-container big-image"
      >
        <div
          className="center-content"
          style={
            {
              "height": "100%",
              "width": "100%",
            }
          }
        >
          <img
            alt="Sick car"
            decoding="async"
            height={224}
            loading="lazy"
            sizes="(max-width: 362px) 361px, 630px"
            src="https://cdn.sanity.io/images/dp11egz7/development/1b5ec6834c77ed22e4fdc526494f928b1998f47a-630x224.png?w=630&h=224&auto=format"
            srcSet="https://cdn.sanity.io/images/dp11egz7/development/1b5ec6834c77ed22e4fdc526494f928b1998f47a-630x224.png?rect=0,1,630,223&w=361&h=128&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/1b5ec6834c77ed22e4fdc526494f928b1998f47a-630x224.png?w=630&h=224&auto=format 630w"
            style={
              {
                "height": "auto",
                "maxHeight": 224,
                "maxWidth": 630,
                "width": "100%",
              }
            }
            width={630}
          />
        </div>
      </div>
      <div
        className="image-container small-image"
      />
      <div
        className="rf-flex-1 module-card-bottom"
      >
        <div
          className="center-content center-text category gray-text"
        >
          HARDWARE
        </div>
        <div
          className="center-content title gray-text"
        >
          Dongle
        </div>
        <div
          className="rf-flex center-content description-container"
        >
          <p
            className="description"
          >
            Track your vehicles 
          </p>
        </div>
      </div>
      <style />
    </div>
    <div
      className="module-card "
    >
      <div
        className="image-container big-image"
      >
        <div
          className="center-content"
          style={
            {
              "height": "100%",
              "width": "100%",
            }
          }
        >
          <img
            alt="Purple car"
            decoding="async"
            height={852}
            loading="lazy"
            sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, 1136px"
            src="https://cdn.sanity.io/images/dp11egz7/development/066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852.jpg?w=1136&h=852&auto=format"
            srcSet="https://cdn.sanity.io/images/dp11egz7/development/066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852.jpg?rect=1,0,1135,852&w=361&h=271&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852.jpg?rect=1,0,1135,852&w=641&h=481&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852.jpg?w=1025&h=769&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852.jpg?w=1136&h=852&auto=format 1136w"
            style={
              {
                "height": "auto",
                "maxHeight": 852,
                "maxWidth": 1136,
                "width": "100%",
              }
            }
            width={1136}
          />
        </div>
      </div>
      <div
        className="image-container small-image"
      />
      <div
        className="rf-flex-1 module-card-bottom"
      >
        <div
          className="center-content center-text category gray-text"
        >
          SOFTWARE
        </div>
        <div
          className="center-content title gray-text"
        >
          Fleet Management
        </div>
        <div
          className="rf-flex center-content description-container"
        >
          <p
            className="description"
          >
            Control your whole fleet, know everything from required tyre changes and fuel consumption to quality assurance.                         
          </p>
        </div>
      </div>
      <style />
    </div>
    <a
      className="module-card module-with-ref"
      href="https://www.google.com"
      rel="noreferrer"
      target="_blank"
    >
      <div
        className="image-container big-image"
      >
        <div
          className="center-content"
          style={
            {
              "height": "100%",
              "width": "100%",
            }
          }
        >
          <img
            alt="Google car"
            decoding="async"
            height={852}
            loading="lazy"
            sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, 1136px"
            src="https://cdn.sanity.io/images/dp11egz7/development/066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852.jpg?w=1136&h=852&auto=format"
            srcSet="https://cdn.sanity.io/images/dp11egz7/development/066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852.jpg?rect=1,0,1135,852&w=361&h=271&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852.jpg?rect=1,0,1135,852&w=641&h=481&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852.jpg?w=1025&h=769&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852.jpg?w=1136&h=852&auto=format 1136w"
            style={
              {
                "height": "auto",
                "maxHeight": 852,
                "maxWidth": 1136,
                "width": "100%",
              }
            }
            width={1136}
          />
        </div>
      </div>
      <div
        className="image-container small-image"
      />
      <div
        className="rf-flex-1 module-card-bottom"
      >
        <div
          className="center-content center-text category gray-text"
        >
          SOFTWARE
        </div>
        <div
          className="center-content title gray-text"
        >
          Fleet Management
        </div>
        <div
          className="rf-flex center-content description-container"
        >
          <p
            className="description"
          >
            Control your whole fleet, know everything from required tyre changes and fuel consumption to quality assurance.                         
          </p>
        </div>
      </div>
      <style />
    </a>
    <a
      className="module-card module-with-ref"
      href="http://www.testing-env.net"
      rel="noreferrer"
      target="_blank"
    >
      <div
        className="image-container big-image"
      >
        <div
          className="center-content"
          style={
            {
              "height": "100%",
              "width": "100%",
            }
          }
        >
          <img
            alt="hmmm"
            decoding="async"
            height={852}
            loading="lazy"
            sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, 1136px"
            src="https://cdn.sanity.io/images/dp11egz7/development/066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852.jpg?w=1136&h=852&auto=format"
            srcSet="https://cdn.sanity.io/images/dp11egz7/development/066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852.jpg?rect=1,0,1135,852&w=361&h=271&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852.jpg?rect=1,0,1135,852&w=641&h=481&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852.jpg?w=1025&h=769&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852.jpg?w=1136&h=852&auto=format 1136w"
            style={
              {
                "height": "auto",
                "maxHeight": 852,
                "maxWidth": 1136,
                "width": "100%",
              }
            }
            width={1136}
          />
        </div>
      </div>
      <div
        className="image-container small-image"
      />
      <div
        className="rf-flex-1 module-card-bottom"
      >
        <div
          className="center-content center-text category gray-text"
        >
          TESTING
        </div>
        <div
          className="center-content title gray-text"
        >
          whatever
        </div>
        <div
          className="rf-flex center-content description-container"
        >
          <p
            className="description"
          >
            Should not open in new tab
          </p>
        </div>
      </div>
      <style />
    </a>
    <div
      className="module-card "
    >
      <div
        className="image-container big-image"
      >
        <div
          className="center-content"
          style={
            {
              "height": "100%",
              "width": "100%",
            }
          }
        >
          <img
            alt="Some article with bad route ref"
            decoding="async"
            height={852}
            loading="lazy"
            sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, 1136px"
            src="https://cdn.sanity.io/images/dp11egz7/development/066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852.jpg?w=1136&h=852&auto=format"
            srcSet="https://cdn.sanity.io/images/dp11egz7/development/066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852.jpg?rect=1,0,1135,852&w=361&h=271&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852.jpg?rect=1,0,1135,852&w=641&h=481&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852.jpg?w=1025&h=769&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852.jpg?w=1136&h=852&auto=format 1136w"
            style={
              {
                "height": "auto",
                "maxHeight": 852,
                "maxWidth": 1136,
                "width": "100%",
              }
            }
            width={1136}
          />
        </div>
      </div>
      <div
        className="image-container small-image"
      />
      <div
        className="rf-flex-1 module-card-bottom"
      >
        <div
          className="center-content center-text category gray-text"
        >
          SOFTWARE
        </div>
        <div
          className="center-content title gray-text"
        >
          Software go mu mu
        </div>
        <div
          className="rf-flex center-content description-container"
        >
          <p
            className="description"
          >
            Some article with bad route ref
          </p>
        </div>
      </div>
      <style />
    </div>
  </div>
  <style />
</section>
`;
