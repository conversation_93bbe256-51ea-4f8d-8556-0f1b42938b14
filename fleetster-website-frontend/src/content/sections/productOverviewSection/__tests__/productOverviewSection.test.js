import React from 'react';

describe('productOverviewSection', () => {
    const mockProductOverview = [
        {
            _key:        '1',
            _type:       'productOverview',
            category:    'Hardware',
            description: 'Track your vehicles ',
            bigImage:    {
                _type: 'image',
                asset: {
                    _ref:  'image-1b5ec6834c77ed22e4fdc526494f928b1998f47a-630x224-png',
                    _type: 'reference'
                },
                height: 224,
                width:  630
            },
            imageAlt: 'Sick car',
            title:    'Dongle'
        },
        {
            _key:        '2',
            _type:       'productOverview',
            category:    'Software',
            description: 'Control your whole fleet, know everything from required tyre changes and fuel ' +
                             'consumption to quality assurance.                         ',
            bigImage: {
                _type: 'image',
                asset: {
                    _ref:  'image-066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852-jpg',
                    _type: 'reference'
                },
                height: 852,
                width:  1136
            },
            route: {
                _ref: 'image-798fd3b0c381c547e8d00c33f8b835f44e9183fd-1280x1465-jpg',
                href: ''
            },
            imageAlt: 'Purple car',
            title:    'Fleet Management'
        },

        {
            _key:        '3',
            _type:       'productOverview',
            category:    'Software',
            description: 'Control your whole fleet, know everything from required tyre changes and fuel ' +
                'consumption to quality assurance.                         ',
            bigImage: {
                _type: 'image',
                asset: {
                    _ref:  'image-066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852-jpg',
                    _type: 'reference'
                },
                height: 852,
                width:  1136
            },
            url:      'https://www.google.com',
            imageAlt: 'Google car',
            title:    'Fleet Management'
        },
        {
            _key:        '4',
            _type:       'productOverview',
            category:    'Testing',
            description: 'Should not open in new tab',
            bigImage:    {
                _type: 'image',
                asset: {
                    _ref:  'image-066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852-jpg',
                    _type: 'reference'
                },
                height: 852,
                width:  1136
            },
            url:      'http://www.testing-env.net',
            imageAlt: 'hmmm',
            title:    'whatever'
        },
        {
            _key:        '5',
            _type:       'productOverview',
            category:    'Software',
            description: 'Some article with bad route ref',
            bigImage:    {
                _type: 'image',
                asset: {
                    _ref:  'image-066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852-jpg',
                    _type: 'reference'
                },
                height: 852,
                width:  1136
            },
            route: {
                _ref: '798fd3b0c381c547e8d00c33f8b835f44e9183fd'
            },
            imageAlt: 'Some article with bad route ref',
            title:    'Software go mu mu'
        }
    ];
    test('it renders correctly', () => {
        const {ProductOverviewSection} = require('../');

        const component = render(<ProductOverviewSection productOverview={mockProductOverview}/>);

        expect(component).toMatchSnapshot();
    });
});
