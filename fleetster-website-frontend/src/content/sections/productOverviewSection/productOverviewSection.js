import _ from 'lodash';
import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {ProductOverviewCard} from './productOverviewCard';
import {RFTitle} from 'rf-components';

const styles = css`
  .modules-menu-container {
    display: flex;
    flex-direction: row;
    width: 100%;
    flex-wrap: wrap;
  }
`;

function ProductOverviewSection({productOverview, title, index}) {
    const anchor = title ? {id: _.kebabCase(title)} : {};

    return (
        <>
            {title && <RFTitle index={index} title={title} />}
            <section className={'page-horizontal-padding section-vertical-padding'} {...anchor}>
                <div className={'modules-menu-container rf-flex-container'}>
                    {_.map(productOverview, props => <ProductOverviewCard key={props._key} {...props}/>)}
                </div>
                <style jsx>{styles}</style>
            </section>
        </>
    );
}

ProductOverviewSection.propTypes = {
    title:           PropTypes.string,
    productOverview: PropTypes.array,
    _key:            PropTypes.string,
    index:           PropTypes.number
};

export {ProductOverviewSection};
