// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ProductStructuredData renders the parsed data in a script tag 1`] = `
<div>
  <script
    dangerouslySetInnerHTML={
      {
        "__html": "{"@context":"https://schema.org","@type":"Product","brand":{"foo":"bar","@type":"Brand"},"manufacturer":{"foo":"bar","location":{"foo":"bar","@type":"PostalAddress"},"@type":"Organization"}}",
      }
    }
    type="application/ld+json"
  />
</div>
`;
