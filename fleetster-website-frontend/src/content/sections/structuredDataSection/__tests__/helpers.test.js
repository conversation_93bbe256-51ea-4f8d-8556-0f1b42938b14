jest.mock('next/router', () => ({
    useRouter: jest.fn().mockReturnValue({asPath: '/some/path'})
}));

import {parseStructuredData} from '../helpers';

describe('helpers', () => {
    describe('parseStructuredData', () => {
        const url = 'http://www.testing-env.net/some/path';

        it('ignores the sanity properties', () => {
            const input = {index: 1, _type: 'object', _key: 'qwe123'};
            const parsed = parseStructuredData(input);

            expect(parsed).toStrictEqual({});
        });

        it('parses string values', () => {
            const input = {foo: 'bar'};
            const parsed = parseStructuredData(input);

            expect(parsed).toStrictEqual({foo: 'bar'});
        });

        it('adds the url to structured data if it has property and is not set', () => {
            const input = {manufacturer: {foo: 'url', url: 'boo'}, url: ''};
            const parsed = parseStructuredData(input, {}, 'Product');

            expect(parsed).toStrictEqual({url, manufacturer: {'@type': 'Organization', foo: 'url', url: 'boo'}});
        });

        it('parses an image', () => {
            const image = {
                _type: 'image',
                asset: {_ref: 'image-example-200x300-jpg', _type: 'reference'}
            };
            const input = {image};
            const parsed = parseStructuredData(input);

            expect(parsed).toStrictEqual({
                image: 'https://cdn.sanity.io/images/dp11egz7/development/example-200x300.jpg?auto=format'
            });
        });

        it('parses objects', () => {
            const input = {
                foo: {
                    bar: 'baz'
                },
                manufacturer: {
                    foo:      'bar',
                    location: {
                        foo: 'bar'
                    }
                },
                ignore:     {},
                ignoreDeep: {
                    empty: {}
                }
            };
            const parsed = parseStructuredData(input);

            expect(parsed).toStrictEqual({
                foo: {
                    '@type': 'Foo',
                    bar:     'baz'
                },
                manufacturer: {
                    '@type':  'Organization',
                    foo:      'bar',
                    location: {
                        '@type': 'PostalAddress',
                        foo:     'bar'
                    }
                }
            });
        });

        it('parses arrays', () => {
            const input = {
                offers: {
                    offers: [
                        {
                            price:         '15',
                            priceCurrency: 'EUR'
                        },
                        {
                            price:         '19',
                            priceCurrency: 'USD'
                        }
                    ]
                },
                ignore:       [],
                manufacturer: {
                    ignore: []
                }
            };
            const parsed = parseStructuredData(input);

            expect(parsed).toStrictEqual({
                offers: {
                    '@type':       'AggregateOffer',
                    lowPrice:      '15',
                    offerCount:    2,
                    priceCurrency: 'EUR',
                    offers:        [
                        {
                            '@type':       'Offer',
                            price:         '15',
                            priceCurrency: 'EUR'
                        },
                        {
                            '@type':       'Offer',
                            price:         '19',
                            priceCurrency: 'USD'
                        }
                    ]
                }
            });
        });
    });
});
