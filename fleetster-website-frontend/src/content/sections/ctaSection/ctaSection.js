import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';
import _ from 'lodash';

import {screenSizes, colors, vSpacing, fontWeights, hSpacing} from 'rf-styles';
import {CallToAction} from 'rf-components';
import {FleetsterIcon} from 'rf-icon';

const styles = css`
  .cta-section {
    background-color: ${colors.blue};
    padding-top: ${vSpacing.m}px;
    padding-bottom: ${vSpacing.m}px;
  }

  .cta-section-container {
    width: auto;
    flex-flow: column;
    row-gap: ${vSpacing.xs * 2}px;
    padding: 0 ${vSpacing.xs}px;
  }

  .prefixed-text {
    color: ${colors.white};
    font-size: 20px;
    line-height: 24px;
    font-weight: ${fontWeights.semiBold};
  }
  
  .prefixed-text .icon-container {
    display: none;
    margin: 0 ${hSpacing.xxs}px;
  }

  @media (min-width: ${screenSizes.xxs}px) {
    .prefixed-text {
      font-size: 24px;
      line-height: 28px;
    }
  }

  @media (min-width: ${screenSizes.s}px) {
    .cta-section-container {
      padding: 0 30px;
      flex-flow: nowrap;
    }

    .prefixed-text .icon-container {
      display: block;
    }
  }

  @media (min-width: ${screenSizes.m}px) {
    .prefixed-text {
      font-size: 28px;
      line-height: 32px;
    }
  }`;

function CtaSection({callToAction}) {
    return (
        <section className={'page-horizontal-padding cta-section'} id={_.kebabCase(callToAction.title)}>
            <div className={'center-content cta-section-container'}>
                {callToAction.prefixedText && <div className={'prefixed-text center-content'}>
                    {callToAction.prefixedText}
                    <div className={'icon-container'}>
                        <FleetsterIcon icon={'arrowRight'} iconSize={30} customStyles={{color: colors.white}}/>
                    </div>
                </div>}
                <CallToAction {...callToAction} lightTheme/>
            </div>
            <style jsx>{styles}</style>
        </section>
    );
}

CtaSection.propTypes = {
    _key:         PropTypes.string,
    callToAction: PropTypes.shape({
        link:         PropTypes.string,
        lightTheme:   PropTypes.bool,
        small:        PropTypes.bool,
        title:        PropTypes.string,
        enableHover:  PropTypes.bool,
        prefixedText: PropTypes.string
    })
};

export {CtaSection};
