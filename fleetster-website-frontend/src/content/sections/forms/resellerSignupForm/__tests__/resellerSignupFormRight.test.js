import React from 'react';
import {ResellerSignupFormRight} from '../ResellerSignupFormRight';
import {salesApi} from 'rf-form-utils';
import {navigationUtil} from 'rf-utils';

jest.mock('rf-form-utils', () => ({
    ...jest.requireActual('rf-form-utils'),
    salesApi: {
        createLead: jest.fn()
    }
}));

jest.mock('rf-utils', () => ({
    ...jest.requireActual('rf-utils'),
    navigationUtil: {
        navigateTo: jest.fn()
    },
    queryStringUtil: {
        getFromUrl: jest.fn().mockImplementation(() => ({internalReferrer: 'huehuebr'}))
    }
}));

describe('ResellerSignupFormRight', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    test('Renders without errors', () => {
        const component = render(<ResellerSignupFormRight
            title={'Test title'}
            subtitle={'Some subtitle'}
            privacyPolicyLink={'http://www.privacy-policy.com'}
            successURL={'http://www.success.com'}
        />);

        expect(component).toMatchSnapshot();
    });

    test('Successfully submit the form when all fields are valid and redirects to successUrl', async() => {
        const component = render(<ResellerSignupFormRight successURL={'www.success.com'}/>);

        const formData = {
            salutation:            'female',
            firstName:             'first',
            lastName:              'last',
            company:               'fleetster',
            email:                 '<EMAIL>',
            phone:                 ' 0633333331',
            subscribeToNewsletter: true
        };

        component.root.instance.setState({fields: _.mapValues(formData, value => ({value}))});

        salesApi.createLead.mockReturnValueOnce(Promise.resolve({hashedId: 'generatedId'}));

        await component.root.instance.onSubmit({preventDefault: () => {}});

        const currentState = component.root.instance.state;

        expect(currentState.submitting).toBe(true);

        expect(salesApi.createLead).toHaveBeenLastCalledWith(
            expect.objectContaining({
                extended: {
                    resellerData: {
                        ...formData,
                        phone:            '0049 0633333331',
                        language:         'de',
                        internalReferrer: 'huehuebr'
                    }
                }
            }),
            undefined
        );
        expect(navigationUtil.navigateTo).toHaveBeenCalledWith('www.success.com', {hashedId: 'generatedId'});
    });

    test('Validates all required fields', async() => {
        const component = render(<ResellerSignupFormRight successURL={'www.success.com'}/>);

        await component.root.instance.onSubmit({preventDefault: () => {}});

        const validatedState = component.root.instance.state;

        expect(validatedState.fields).toEqual(expect.objectContaining({
            salutation: {error: 'form.error.salutationMissing'},
            firstName:  {error: 'form.error.firstNameMissing'},
            lastName:   {error: 'form.error.lastNameMissing'},
            company:    {error: 'form.error.companyMissing'},
            phone:      {error: 'form.error.phoneMissing'},
            email:      {error: 'form.error.emailMissing'}
        }));
    });

    test('Validates email and phone formats', async() => {
        const component = render(<ResellerSignupFormRight successURL={'www.success.com'}/>);

        const formData = {
            salutation: 'female',
            firstName:  'first',
            lastName:   'last',
            company:    'fleetster',
            email:      'testAttest.com',
            phone:      ' 0633lalalalala333331'
        };

        component.root.instance.setState({fields: _.mapValues(formData, value => ({value}))});

        await component.root.instance.onSubmit({preventDefault: () => {}});

        const validatedState = component.root.instance.state;

        expect(validatedState.fields.phone).toEqual({error: 'form.error.phoneWrongFormat', value: ' 0633lalalalala333331'});
        expect(validatedState.fields.email).toEqual({error: 'form.error.emailWrongFormat', value: 'testAttest.com'});
    });

    test('Validates disallowed characters', async() => {
        const component = render(<ResellerSignupFormRight successURL={'www.success.com'}/>);

        const formData = {
            salutation: 'male',
            firstName:  '<>',
            lastName:   '😂',
            company:    '\\',
            email:      '<EMAIL>',
            phone:      ' 0633333331'
        };

        component.root.instance.setState({fields: _.mapValues(formData, value => ({value}))});

        await component.root.instance.onSubmit({preventDefault: () => {}});

        const validatedState = component.root.instance.state;

        expect(validatedState.fields.firstName).toEqual({error: 'form.error.onlyAlphanumericChars', value: formData.firstName});
        expect(validatedState.fields.lastName).toEqual({error: 'form.error.onlyAlphanumericChars', value: formData.lastName});
        expect(validatedState.fields.company).toEqual({error: 'form.error.onlyAlphanumericChars', value: formData.company});
    });

    test('Allows accented characters and some special characters', async() => {
        const component = render(<ResellerSignupFormRight successURL={'www.success.com'}/>);

        const formData = {
            salutation: 'male',
            firstName:  'áüéãß',
            lastName:   '---___',
            company:    '(testing™®℠㋏#$%&)',
            email:      '<EMAIL>',
            phone:      ' 0633333331'
        };

        component.root.instance.setState({fields: _.mapValues(formData, value => ({value}))});

        await component.root.instance.onSubmit({preventDefault: () => {}});

        const validatedState = component.root.instance.state;

        expect(validatedState.fields.firstName.error).toBeUndefined();
        expect(validatedState.fields.lastName.error).toBeUndefined();
        expect(validatedState.fields.company.error).toBeUndefined();
    });

    test('Sets form to an error state if exception happens on submit information', async() => {
        const component = render(<ResellerSignupFormRight successURL={'www.success.com'}/>);

        const formData = {
            salutation:            'female',
            firstName:             'first',
            lastName:              'last',
            company:               'fleetster',
            email:                 '<EMAIL>',
            phone:                 ' 0633333331',
            subscribeToNewsletter: true
        };

        component.root.instance.setState({fields: _.mapValues(formData, value => ({value}))});

        await component.root.instance.onSubmit({preventDefault: () => {}});

        const errorMessage = 'some error message';

        salesApi.createLead.mockImplementationOnce(() => { throw {message: errorMessage}; });

        await component.root.instance.onSubmit({preventDefault: () => {}});

        const currentState = component.root.instance.state;

        expect(currentState).toEqual(expect.objectContaining({submitError: errorMessage, submitting: false}));
    });

    test('trims whitespace around the email before submitting', async() => {
        const component = render(<ResellerSignupFormRight successURL={'www.success.com'}/>);

        const formData = {
            salutation:  'female',
            firstName:   'first',
            lastName:    'last',
            company:     'fleetster',
            email:       ' <EMAIL> ',
            phone:       '*********',
            phonePrefix: '351'
        };

        component.root.instance.setState({fields: _.mapValues(formData, value => ({value}))});

        await component.root.instance.onSubmit({preventDefault: () => {}});

        expect(salesApi.createLead).toHaveBeenCalledWith(expect.objectContaining({
            extended: expect.objectContaining({resellerData: expect.objectContaining({email: '<EMAIL>'})})
        }), undefined);
    });
});
