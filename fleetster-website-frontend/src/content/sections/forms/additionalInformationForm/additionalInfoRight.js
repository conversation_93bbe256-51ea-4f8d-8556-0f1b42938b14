import React, {Component} from 'react';
import PropTypes from 'prop-types';
import * as _ from 'lodash';

import {navigationUtil, queryStringUtil} from 'rf-utils';
import {salesApi, FormContext, getProcessedForm, updateField} from 'rf-form-utils';

import {<PERSON>Footer, FormHeader, Form} from '../components';

import {NumberInput, GoLiveDatePicker, TextAreaInput, MainObjectiveSelector} from './components';

export class AdditionalInfoRight extends Component {
    static propTypes = {
        title:         PropTypes.string,
        subtitle:      PropTypes.string,
        successURL:    PropTypes.string,
        redirectURL:   PropTypes.string,
        salesEndpoint: PropTypes.string
    };

    constructor(props) {
        super(props);

        this.state = {fields: {}};
    }

    componentDidMount() {
        const {redirectURL} = this.props;

        const {hashedId} = queryStringUtil.getFromUrl();
        if (!hashedId) {
            navigationUtil.navigateTo(redirectURL);
        }

        this.setState({hashedId});
    }

    async onSubmit(evt) {
        evt.preventDefault();
        const processedForm = getProcessedForm(this.state);
        this.setState({submitError: undefined, submitting: true});

        const {hashedId} = this.state;

        try {
            await salesApi.updateLead(hashedId, processedForm, this.props.salesEndpoint);
        } catch (error) {
            this.setState({submitError: _.get(error, 'message', error), submitting: false});

            return;
        }

        navigationUtil.navigateTo(this.props.successURL, {hashedId});
    }

    render() {
        const {title, subtitle} = this.props;

        return (
            <FormContext.Provider value={{...this.state, updateField: updateField.bind(this)}}>
                <Form onSubmit={this.onSubmit.bind(this)}>
                    <FormHeader title={title} subtitle={subtitle}/>

                    <GoLiveDatePicker/>
                    <MainObjectiveSelector />
                    <TextAreaInput fieldName={'description'}/>
                    <NumberInput fieldName={'totalNumberManagedVehicles'}/>

                    <FormFooter pages={2} currentPage={0} submitButtonLabel={'form.action.next'}/>
                </Form>
            </FormContext.Provider>
        );
    }
}
