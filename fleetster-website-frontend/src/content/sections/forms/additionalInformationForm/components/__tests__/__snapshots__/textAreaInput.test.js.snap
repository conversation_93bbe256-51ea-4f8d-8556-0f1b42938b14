// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`TextAreaInput Should render without errors 1`] = `
<div
  className="bp5-form-group bp5-text-large"
>
  <label
    className="bp5-label"
    htmlFor="textAreaInput"
  >
    form.field.textAreaInput
     
    <span
      className="bp5-text-muted"
    />
  </label>
  <div
    className="bp5-form-content"
  >
    <textarea
      className="bp5-input bp5-text-area bp5-fill"
      id="textAreaInput"
      onChange={[Function]}
      placeholder="form.field.textAreaInputPlaceholder"
      style={
        {
          "minHeight": "120px",
          "resize": "none",
        }
      }
    />
  </div>
</div>
`;
