import React from 'react';

describe('NumberInput', () => {
    const mockUpdateField = jest.fn();

    test('Should render without errors', () => {
        const {NumberInput} = require('../numberInput');

        const component = render(<NumberInput fieldName={'numberInput'}/>);

        expect(component).toMatchSnapshot();
    });

    test('Initializes with context value', () => {
        const {NumberInput} = require('../numberInput');
        const {FormContext} = require('rf-form-utils');

        const component = render(
            <FormContext.Provider value={{fields: {numberInput: {value: 1337}}}}><NumberInput fieldName={'numberInput'}/> </FormContext.Provider>
        );

        const numberFieldValue = component.root.findByProps({id: 'numberInput'}).props.value;

        expect(numberFieldValue).toEqual(1337);
    });

    test('Updates context field on value change', () => {
        const {NumberInput} = require('../numberInput');
        const {FormContext} = require('rf-form-utils');

        const component = render(
            <FormContext.Provider value={{
                updateField: mockUpdateField,
                fields:      {numberInput: {value: 1337}}
            }}>
                <NumberInput fieldName={'numberInput'}/>
            </FormContext.Provider>
        );

        const onValueChange = component.root.findByProps({id: 'numberInput'}).props.onValueChange;

        onValueChange(1338, '1338');

        expect(mockUpdateField).toBeCalledWith({path: 'numberInput', evt: {target: {value: 1338}}});
    });

    test('Value should default to 0 if NaN', () => {
        const {NumberInput} = require('../numberInput');
        const {FormContext} = require('rf-form-utils');

        const component = render(
            <FormContext.Provider value={{
                updateField: mockUpdateField,
                fields:      {numberInput: {value: 1337}}
            }}>
                <NumberInput fieldName={'numberInput'}/>
            </FormContext.Provider>
        );

        const onValueChange = component.root.findByProps({id: 'numberInput'}).props.onValueChange;

        onValueChange(NaN, 'NaN');

        expect(mockUpdateField).toBeCalledWith({path: 'numberInput', evt: {target: {value: 0}}});
    });

    test('Non-digit values should be filtered out', () => {
        const {NumberInput} = require('../numberInput');
        const {FormContext} = require('rf-form-utils');

        const component = render(
            <FormContext.Provider value={{
                updateField: mockUpdateField,
                fields:      {numberInput: {value: 1337}}
            }}>
                <NumberInput fieldName={'numberInput'}/>
            </FormContext.Provider>
        );

        const onValueChange = component.root.findByProps({id: 'numberInput'}).props.onValueChange;

        onValueChange(NaN, '200-250');

        expect(mockUpdateField).toBeCalledWith({path: 'numberInput', evt: {target: {value: 200250}}});
    });
});
