import React from 'react';

describe('TextAreaInput', () => {
    const mockUpdateField = jest.fn();
    let mockContext = {
        fields:      {},
        updateField: mockUpdateField
    };

    beforeAll(() => {
        jest.mock('react', () => ({
            ...jest.requireActual('react'),
            useContext: () => mockContext
        }));
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    test('Should render without errors', () => {
        const {TextAreaInput} = require('../textAreaInput');

        const component = render(<TextAreaInput fieldName={'textAreaInput'}/>);

        expect(component).toMatchSnapshot();
    });

    test('Initializes with context value', () => {
        const {TextAreaInput} = require('../textAreaInput');

        mockContext.fields.textAreaInput = {value: 'huehuebr'};

        const component = render(<TextAreaInput fieldName={'textAreaInput'}/>);

        const textAreaValue = component.root.findByProps({id: 'textAreaInput'}).props.value;

        expect(textAreaValue).toEqual('huehuebr');
    });

    test('Updates context field on value change', () => {
        const {TextAreaInput} = require('../textAreaInput');

        const component = render(<TextAreaInput fieldName={'textAreaInput'}/>);

        const onChange = component.root.findByProps({id: 'textAreaInput'}).props.onChange;

        onChange({target: {value: 'huehuebr'}});

        expect(mockUpdateField).toBeCalledWith({fieldName: 'textAreaInput', evt: {target: {value: 'huehuebr'}}});
    });
});
