import React, {useContext, useEffect, useState} from 'react';
import moment from 'rf-moment-locale';
import {Classes, FormGroup, PopoverPosition} from '@blueprintjs/core';
import {DateInput3} from '@blueprintjs/datetime2';
import '@blueprintjs/datetime2/lib/css/blueprint-datetime2.css';
import _ from 'lodash';

import {t} from 'rf-i18n';
import {FormContext} from 'rf-form-utils';
import {FleetsterIcon} from 'rf-icon';
import {colors} from 'rf-styles';

const timeHorizons = ['oneMonth', 'twoMonths', 'threeMonths', 'fourMonths', 'sixMonths'];

const datepickerShortcuts = timeHorizons.map((timeHorizon, i) => {
    return {
        date:  moment().add(i + 1, 'months').toDate(),
        label: t(`form.shortcut.${timeHorizon}FromNow`)
    };
});

export function GoLiveDatePicker() {
    const formContext = useContext(FormContext);
    const [locale, setLocale] = useState();

    useEffect(() => {
        setLocale(_.get(navigator, 'languages[0]', navigator.language));
    }, []);

    const onDateChange = value => {
        const startOfDay = moment().startOf('day');
        const maxDate = moment().add(20, 'y').endOf('y');

        if (startOfDay.isAfter(value)) {
            formContext.updateField({fieldName: 'goLiveDate', evt: {target: {value: startOfDay.valueOf()}}});

            return;
        }

        if (maxDate.isBefore(value)) {
            formContext.updateField({fieldName: 'goLiveDate', evt: {target: {value: maxDate.valueOf()}}});

            return;
        }

        if (value) {
            formContext.updateField({fieldName: 'goLiveDate', evt: {target: {value: moment(value).valueOf()}}});
        } else {
            formContext.updateField({fieldName: 'goLiveDate', evt: {target: {value: null}}});
        }
    };

    const goLiveDate = _.get(formContext, 'fields.goLiveDate.value', Date.now());

    return (
        <FormGroup
            label={t('form.field.goLiveDate')}
            labelFor={'goLiveDate'}
            vertical={true}
            className={Classes.TEXT_LARGE}
        >
            <DateInput3
                id={'goLiveDate'}
                fill={true}
                onChange={onDateChange}
                onError={onDateChange}
                value={moment(goLiveDate).format()}
                shortcuts={datepickerShortcuts}
                closeOnSelection={false}
                popoverProps={{
                    position: PopoverPosition.BOTTOM_LEFT,
                    minimal:  true
                }}
                minDate={moment().startOf('day').toDate()}
                maxDate={moment().add(20, 'y').endOf('y').toDate()}
                inputProps={{
                    leftElement:
                        <FleetsterIcon icon={'calendar'} iconSize={16} customStyles={{display: 'inline-block', margin: '7px', color: colors.blue}} />
                }}
                locale={locale}
                placeholder={t('form.field.goLiveDatePlaceholder')}
                formatDate={(date, locale) => moment(date).locale(locale).format('L')}
                parseDate={(str, locale) => moment(str, 'L').locale(locale).toDate()}
            />
        </FormGroup>
    );
}
