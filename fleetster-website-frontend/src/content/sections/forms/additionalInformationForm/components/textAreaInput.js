import React, {useContext} from 'react';
import PropTypes from 'prop-types';
import {Classes, FormGroup, TextArea} from '@blueprintjs/core';
import _ from 'lodash';

import {t} from 'rf-i18n';
import {FormContext} from 'rf-form-utils';

export function TextAreaInput({fieldName}) {
    const form = useContext(FormContext);

    return (
        <FormGroup
            label={t(`form.field.${fieldName}`)}
            labelFor={fieldName}
            className={Classes.TEXT_LARGE}>
            <TextArea
                id={fieldName}
                fill
                value={_.get(form, `fields.${fieldName}.value`)}
                placeholder={t(`form.field.${fieldName}Placeholder`)}
                onChange={evt => form.updateField({fieldName, evt})}
                style={{minHeight: '120px', resize: 'none'}}/>
        </FormGroup>
    );
}

TextAreaInput.propTypes = {fieldName: PropTypes.string};
