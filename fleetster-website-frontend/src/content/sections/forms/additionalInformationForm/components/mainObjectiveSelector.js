import React, {useState, useContext} from 'react';
import * as _ from 'lodash';
import {global} from 'styled-jsx/css';

import {Select2} from '@blueprintjs/select';
import {MenuItem, Button, Intent, Classes, FormGroup} from '@blueprintjs/core';

import {t} from 'rf-i18n';
import {FormContext} from 'rf-form-utils';

export const selectItems = _.compact(_.range(0, 100).map(i => {
    const paddedIndex = i.toString().padStart(3, '0');
    const label = `form.field.mainObjectiveOption${paddedIndex}`;
    const value = `mainObjectiveOption${paddedIndex}`;

    const name = t(label);

    if (name === label) {
        return null;
    }

    return {
        label,
        value
    };
}));

const styles = global`
    .main-objective-selector .bp3-select-popover {
        width: 100%;
    }

    .main-objective-selector button.bp3-button {
        display: flex;
        justify-content: space-between;
    }
`;

export function MainObjectiveSelector() {
    const [selectedItem, setSelectedItem] = useState(undefined);
    const [activeItem, setActiveItem] = useState(selectItems[0]);
    const formContext = useContext(FormContext);

    const itemRenderer = (item, itemProps) => {
        if (!item) {
            return null;
        }

        return (
            <MenuItem
                key={item.label}
                text={t(item.label)}
                active={_.get(itemProps, 'modifiers.active', false)}
                onClick={() => setSelectedItem(item)}
            />
        );
    };

    const handleItemSelect = item => {
        setSelectedItem(item);
        setActiveItem(item);
        formContext.updateField({
            fieldName: 'mainObjective',
            evt:       {target: {value: item.value}}
        });
    };

    const buttonText = selectedItem ? t(selectedItem.label) : t('form.field.chooseAnOption');

    return (
        <>
            <FormGroup
                label={t('form.field.mainObjective')}
                labelFor={'mainObjective'}
                className={[Classes.TEXT_LARGE, 'main-objective-selector']}
            >
                <Select2
                    filterable={false}
                    onItemSelect={handleItemSelect}
                    activeItem={activeItem}
                    onActiveItemChange={setActiveItem}
                    items={selectItems}
                    itemRenderer={itemRenderer}
                >
                    <Button intent={Intent.NONE} text={buttonText} rightIcon={'caret-down'} fill={true} />
                </Select2>
            </FormGroup>
            <style jsx global>
                {styles}
            </style>
        </>
    );
}
