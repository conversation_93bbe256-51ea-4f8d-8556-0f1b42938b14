import React, {Component} from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';

import {navigationUtil, queryStringUtil} from 'rf-utils';
import {salesApi, FormContext, getProcessedForm, updateField, getFormErrors} from 'rf-form-utils';

import {FormFooter, FormHeader, Form, TextInput, SalutationInput, PhoneInput} from '../components';

export class CustomerInformationFormRight extends Component {
    static propTypes = {
        title:             PropTypes.string,
        subtitle:          PropTypes.string,
        privacyPolicyLink: PropTypes.string,
        successURL:        PropTypes.string,
        redirectURL:       PropTypes.string,
        salesEndpoint:     PropTypes.string
    };

    constructor(props) {
        super(props);

        this.state = {fields: {}};
    }

    componentDidMount() {
        const {redirectURL} = this.props;

        const {hashedId} = queryStringUtil.getFromUrl();
        if (!hashedId) {
            navigationUtil.navigateTo(redirectURL);
        }

        this.setState({hashedId});
    }

    validateForm(processedForm) {
        const formErrors = getFormErrors(processedForm);

        _.forEach(formErrors, (error, fieldName) => {
            this.setState(state => _.set(state, `fields.${fieldName}.error`, error));
        });

        return formErrors;
    }

    async onSubmit(evt) {
        evt.preventDefault();

        this.setState({submitError: undefined});

        const processedForm = getProcessedForm(this.state);

        const hasErrors = this.validateForm(processedForm);

        if (!_.isEmpty(hasErrors)) { return; }

        this.setState({submitting: true});

        try {
            const {hashedId} = this.state;

            await salesApi.updateLead(hashedId, processedForm, this.props.salesEndpoint);

            navigationUtil.navigateTo(this.props.successURL, {hashedId});
        } catch (error) {
            this.setState({submitError: _.get(error, 'message', error), submitting: false});
        }
    }

    render() {
        const {title, subtitle} = this.props;

        return (
            <FormContext.Provider value={{...this.state, updateField: updateField.bind(this)}}>
                <Form onSubmit={this.onSubmit.bind(this)}>
                    <FormHeader title={title} subtitle={subtitle}/>

                    <SalutationInput labelPrefix={'customerData'}/>
                    <TextInput fieldName={'firstName'} labelPrefix={'customerData'}/>
                    <TextInput fieldName={'lastName'} labelPrefix={'customerData'}/>
                    <TextInput fieldName={'company'} labelPrefix={'customerData'}/>
                    <TextInput fieldName={'email'} labelPrefix={'customerData'} type={'email'}/>
                    <PhoneInput labelPrefix={'customerData'}/>

                    <FormFooter submitButtonLabel={'form.action.submit'}/>
                </Form>
            </FormContext.Provider>
        );
    }
}
