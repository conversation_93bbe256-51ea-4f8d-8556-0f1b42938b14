import _ from 'lodash';
import phoneMetadata from 'libphonenumber-js/metadata.min.json';
import {getName, registerLocale} from 'i18n-iso-countries';

import siteConfig from 'rf-data/siteConfig.json';

let {language} = siteConfig;
let translations = require(`i18n-iso-countries/langs/${language}.json`);
registerLocale(translations);

export const phoneCountryList = _.reduce(phoneMetadata.country_calling_codes, (accumulator, countries, callingCode) => {
    if (countries[0] === '001') {
        return accumulator;
    }

    let allCountries = _.compact(
        _.map(countries, country => {
            const countryName = getName(country, language);

            if (!countryName) {
                return null;
            }

            return {country, countryName, callingCode};
        })
    );

    accumulator = [...accumulator, ...allCountries];

    return accumulator;
}, []);
