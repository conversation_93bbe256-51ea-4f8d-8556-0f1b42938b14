import React from 'react';
import {Intent} from '@blueprintjs/core';
import {PhonePrefixSelect} from '../phonePrefixSelect';
import {Select2} from '@blueprintjs/select';

jest.mock('rf-form-utils', () => ({
    ...jest.requireActual('rf-form-utils'),
    FormContext: jest.requireActual('react').createContext({updateField: mockStoreFn('mockUpdateField')})
}));

describe('PhonePrefixSelect', () => {
    const mockUpdateField = mockStoreFn('mockUpdateField');

    beforeEach(() => {
        jest.clearAllMocks();
    });

    test('Should render without errors', () => {
        const component = render(<PhonePrefixSelect />);

        expect(component.toJSON()).toMatchSnapshot();

        expect(component.root.instance.state.defaults.country).toBe('US');
    });

    test('Should use another country if browser language is different', () => {
        const navigatorLanguage = 'de-DE';
        Object.defineProperty(navigator, 'language', {get: () => navigatorLanguage});

        const component = render(<PhonePrefixSelect />);

        expect(component.root.instance.state.defaults.country).toBe('DE');
    });

    test('Updates context fields on value change', () => {
        const component = render(<PhonePrefixSelect />);

        const previousState = component.root.instance.state;

        const onActiveItemChange = component.root.findByType(Select2).props.onActiveItemChange;

        onActiveItemChange({country: 'BR', callingCode: '55'});

        const currentState = component.root.instance.state;

        expect(currentState).not.toEqual(previousState);
        expect(currentState.phonePrefix).toEqual({country: 'BR', callingCode: '55'});
        expect(mockUpdateField).toBeCalledWith({fieldName: 'country', evt: {target: {value: 'BR'}}});
        expect(mockUpdateField).toBeCalledWith({fieldName: 'phonePrefix', evt: {target: {value: '55'}}});
    });

    test('Search works with country callingCode', () => {
        const component = render(<PhonePrefixSelect />);

        const itemPredicate = component.root.findByType(Select2).props.itemPredicate;

        const found = itemPredicate('55', {countryName: 'Brazil', callingCode: '55'});

        expect(found).toBeTruthy();
    });

    test('Intent changes when value has an error', async() => {
        jest.doMock('rf-form-utils', () => ({
            ...jest.requireActual('rf-form-utils'),
            FormContext: React.createContext({
                fields:      {phone: {value: '', error: 'I am wrong'}},
                updateField: mockUpdateField
            })
        }));

        const {PhonePrefixSelect} = await import('../phonePrefixSelect');

        let component;
        await act(() => {
            component = render(<PhonePrefixSelect />);
        });

        const dangerButton = component.root.findByProps({intent: Intent.DANGER});

        expect(dangerButton).toBeTruthy();
    });

    test('Properly displays calling code', () => {
        const component = render(<PhonePrefixSelect />);

        component.root.instance.setState({phonePrefix: {callingCode: '55'}, defaults: {}});

        expect(component.root.findByProps({text: '+55'})).toBeTruthy();
    });

    test('Shows N/A if phonePrefix is undefined', () => {
        const component = render(<PhonePrefixSelect />);

        component.root.instance.setState({phonePrefix: undefined, defaults: {}});

        expect(component.root.findByProps({text: 'N/A'})).toBeTruthy();
    });

    test('Shows N/A if no phone prefix has no calling code', () => {
        const component = render(<PhonePrefixSelect />);

        component.root.instance.setState({phonePrefix: {}, defaults: {}});

        expect(component.root.findByProps({text: 'N/A'})).toBeTruthy();
    });
});
