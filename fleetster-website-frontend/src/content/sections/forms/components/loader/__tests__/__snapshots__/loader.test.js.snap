// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Loader Renders without errors 1`] = `
<div
  autoFocus={true}
  isOpen={true}
>
  <div
    className="loader-overlay rf-flex-container"
  >
    <div
      style={
        {
          "flex": 1,
        }
      }
    />
    <div
      className="center-content"
    >
      <img
        alt="company logo icon"
        className="spinner-logo"
        src="/assets/images/spinner-logo.png"
      />
      <img
        alt="company logo text"
        src="/assets/images/spinner-text.png"
      />
    </div>
    <div
      style={
        {
          "flex": 1,
        }
      }
    />
  </div>
  <style />
</div>
`;
