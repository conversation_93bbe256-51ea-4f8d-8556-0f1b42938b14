import React from 'react';
import PropTypes from 'prop-types';
import {Classes} from '@blueprintjs/core';

import {T} from 'rf-i18n';
import {removeEndSlash} from 'rf-utils';

export function PrivacyPolicy({privacyPolicyLink}) {
    return (
        <div style={{marginTop: '15px'}}>
            <T className={Classes.TEXT_SMALL}>form.privacyPolicyText</T>&nbsp;
            <a href={removeEndSlash(privacyPolicyLink)}><T className={Classes.TEXT_SMALL}>form.privacyPolicy</T></a>.
        </div>
    );
}

PrivacyPolicy.propTypes = {privacyPolicyLink: PropTypes.string};
