import React, {useContext} from 'react';
import {Radio, RadioGroup} from '@blueprintjs/core';
import PropTypes from 'prop-types';
import _ from 'lodash';

import {T} from 'rf-i18n';
import {FormContext} from 'rf-form-utils';

import {ValidatedField} from './validatedField';

export function SalutationInput({labelPrefix}) {
    const form = useContext(FormContext);

    return (
        <ValidatedField labelFor={'salutation'} labelPrefix={labelPrefix}>
            <RadioGroup
                inline
                id={'salutation'}
                selectedValue={_.get(form, 'fields.salutation.value')}
                onChange={evt => form.updateField({fieldName: 'salutation', evt})}>
                <Radio label={<T>form.field.salutationFemale</T>} value={'female'}/>
                <Radio label={<T>form.field.salutationMale</T>} value={'male'}/>
            </RadioGroup>
        </ValidatedField>
    );
}

SalutationInput.propTypes = {
    labelPrefix: PropTypes.string
};

