import React from 'react';

describe('Form', () => {
    test('Renders without errors', () => {
        const {Form} = require('../form');

        const onSubmit = () => {};

        const component = render(<Form onSubmit={onSubmit}>
            <span id={'child1'} />
            <span id={'child2'} />
        </Form>);

        expect(component.root.props.onSubmit).toBe(onSubmit);
        expect(component.root.findByProps({id: 'child1'})).toBeTruthy();
        expect(component.root.findByProps({id: 'child2'})).toBeTruthy();
        expect(component).toMatchSnapshot();
    });
});
