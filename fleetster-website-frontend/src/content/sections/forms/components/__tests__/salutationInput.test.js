import React from 'react';

describe('PhoneInput', () => {
    const mockUpdateField = jest.fn();
    let mockContext = {
        fields:      {},
        updateField: mockUpdateField
    };

    beforeAll(() => {
        jest.mock('react', () => ({
            ...jest.requireActual('react'),
            useContext: () => mockContext
        }));
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    test('Should render without errors', () => {
        const {SalutationInput} = require('../salutationInput');

        const component = render(<SalutationInput />);

        expect(component).toMatchSnapshot();
    });

    test('Initializes with context value', () => {
        const {SalutationInput} = require('../salutationInput');

        mockContext.fields.salutation = {value: 'female'};

        const component = render(<SalutationInput />);

        const radioField = component.root.findByProps({selectedValue: 'female'});

        expect(radioField).toBeTruthy();
    });

    test('Updates field on value change', () => {
        const {SalutationInput} = require('../salutationInput');
        const {RadioGroup} = require('@blueprintjs/core');

        const component = render(<SalutationInput />);

        const textField = component.root.findByType(RadioGroup);

        textField.props.onChange({target: {value: 'male'}});

        expect(mockUpdateField).toBeCalledWith({fieldName: 'salutation', evt: {target: {value: 'male'}}});
    });
});
