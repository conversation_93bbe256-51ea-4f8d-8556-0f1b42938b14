// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`PhoneInput Should render without errors 1`] = `
<div
  className="bp5-form-group bp5-text-large "
>
  <label
    className="bp5-label"
    htmlFor="salutation"
  >
    form.field.salutation
     
    <span
      className="bp5-text-muted"
    />
  </label>
  <div
    className="bp5-form-content"
  >
    <div
      className="bp5-radio-group"
    >
      <label
        className="bp5-control bp5-radio bp5-inline"
      >
        <input
          checked={false}
          name="Blueprint5.RadioGroup-0"
          onChange={[Function]}
          type="radio"
          value="female"
        />
        <span
          className="bp5-control-indicator"
        />
        form.field.salutationFemale
      </label>
      <label
        className="bp5-control bp5-radio bp5-inline"
      >
        <input
          checked={false}
          name="Blueprint5.RadioGroup-0"
          onChange={[Function]}
          type="radio"
          value="male"
        />
        <span
          className="bp5-control-indicator"
        />
        form.field.salutationMale
      </label>
    </div>
    <style />
  </div>
</div>
`;
