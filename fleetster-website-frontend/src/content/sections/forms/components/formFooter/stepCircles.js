import React from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';
import css from 'styled-jsx/css';

import {colors} from 'rf-styles';

const styles = css`
.circle {
    height: 15px;
    width: 15px;
    border-radius: 15px;
    margin: 5px;
    background-color: ${colors.gray};
}

.circle.selected {
    height: 18px;
    width: 18px;
    background-color: ${colors.lightBlue};
}`;

export function Circle({selected}) {
    return (
        <div className={`circle ${selected ? 'selected' : ''}`}>
            <style jsx>{styles}</style>
        </div>
    );
}

Circle.propTypes = {selected: PropTypes.bool};

export function StepCircles({pages, currentPage}) {
    if (pages === 0) {
        return null;
    }

    return (
        <div className={'center-content'}>
            {_.times(pages, i => <Circle selected={currentPage === i} key={`stepCircles-${i}`}/>)}
        </div>
    );
}

StepCircles.propTypes = {
    pages:       PropTypes.number,
    currentPage: PropTypes.number
};
