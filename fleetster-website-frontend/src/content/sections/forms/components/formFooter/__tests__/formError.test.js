import React from 'react';

describe('FormError', () => {
    let mockContext = {};

    beforeAll(() => {
        jest.mock('react', () => ({
            ...jest.requireActual('react'),
            useContext: () => mockContext
        }));
    });

    test('Renders without errors when submitError is true', () => {
        const {FormError} = require('../formError');

        mockContext.submitError = 'This is an error message';

        const component = render(<FormError />);
        const componentText = JSON.stringify(component.toJSON());

        expect(componentText.includes('form.error.formSubmission')).toBe(true);
    });

    test('Renders nothing if submitError is false', () => {
        const {FormError} = require('../formError');

        mockContext.submitError = false;

        const component = render(<FormError />);

        expect(component.toJSON()).toBe(null);
    });
});
