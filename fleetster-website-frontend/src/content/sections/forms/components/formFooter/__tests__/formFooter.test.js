import React from 'react';

describe('FormFooter', () => {
    test('Renders without errors', () => {
        const {FormFooter} = require('../formFooter');

        const component = render(<FormFooter
            pages={3}
            currentPage={1}
            submitButtonIcon={'some icon'}
            submitButtonLabel={'this is a label'}
        />);

        expect(component).toMatchSnapshot();
    });
});
