// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FormFooter Renders without errors 1`] = `
<div>
  <div
    className="bp5-form-group"
    style={
      {
        "margin": 0,
      }
    }
  >
    <div
      className="bp5-form-content button-row"
    >
      <div
        className="center-content"
      >
        <div
          className="circle "
        >
          <style />
        </div>
        <div
          className="circle selected"
        >
          <style />
        </div>
        <div
          className="circle "
        >
          <style />
        </div>
      </div>
      <button
        className="bp5-button bp5-large bp5-intent-primary"
        disabled={false}
        onBlur={[Function]}
        onKeyDown={[Function]}
        onKeyUp={[Function]}
        type="submit"
      >
        <span
          className="bp5-button-text"
        >
          this is a label
        </span>
        <span
          aria-hidden={true}
          className="bp5-icon bp5-icon-standard bp5-icon-some icon"
          data-icon="some icon"
        />
      </button>
    </div>
  </div>
  <style />
</div>
`;
