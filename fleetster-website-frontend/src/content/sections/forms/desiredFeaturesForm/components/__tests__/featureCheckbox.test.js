import React from 'react';

describe('Feature Checkbox', () => {
    const mockUpdateField = jest.fn();

    test('Should render without errors', () => {
        const {FeatureCheckbox} = require('../featureCheckbox');

        const component = render(<FeatureCheckbox feature={'someFeature'}/>);

        expect(component).toMatchSnapshot();
    });

    test('Should render without errors, when the checkbox is checked (shows number input)', () => {
        const {FeatureCheckbox} = require('../featureCheckbox');
        const {FormContext} = require('rf-form-utils');

        const component = render(
            <FeatureCheckbox feature={'someFeature'}/>,
            {
                wrappingComponent:      FormContext.Provider,
                wrappingComponentProps: {
                    value: {fields: {someFeature: {value: true}}}
                }
            }
        );

        expect(component).toMatchSnapshot();
    });

    test('Initializes with context value', () => {
        const {FeatureCheckbox} = require('../featureCheckbox');
        const {FormContext} = require('rf-form-utils');

        const component = render(
            <FormContext.Provider value={{fields: {someFeature: {value: true}}}}> <FeatureCheckbox feature={'someFeature'}/> </FormContext.Provider>
        );

        const fieldValue = component.root.findByProps({id: 'someFeature'}).props.defaultChecked;

        expect(fieldValue).toBe(true);
    });

    test('Updates context checkbox checked field on value change', () => {
        const {FeatureCheckbox} = require('../featureCheckbox');
        const {FormContext} = require('rf-form-utils');

        const component = render(
            <FormContext.Provider value={{
                updateField: mockUpdateField,
                fields:      {someFeature: {value: true}}
            }}>
                <FeatureCheckbox feature={'someFeature'}/>
            </FormContext.Provider>
        );

        const onChange = component.root.findByProps({id: 'someFeature'}).props.onChange;

        onChange({target: {checked: false}});

        expect(mockUpdateField).toBeCalledWith({
            path: 'extended.desiredFeatures.someFeature.size',
            evt:  {target: {value: ''}}
        });
        expect(mockUpdateField).toBeCalledWith({fieldName: 'someFeature', evt: {target: {checked: false}}});
    });

    test('Updates context checkbox checkedaaA field on value change', () => {
        const {FeatureCheckbox} = require('../featureCheckbox');
        const {FormContext} = require('rf-form-utils');

        const component = render(
            <FormContext.Provider value={{
                updateField: mockUpdateField,
                fields:      {}
            }}>
                <FeatureCheckbox feature={'someFeature'}/>
            </FormContext.Provider>
        );

        const onChange = component.root.findByProps({id: 'someFeature'}).props.onChange;

        onChange({target: {checked: true}});

        expect(mockUpdateField).toBeCalledWith({fieldName: 'someFeature', evt: {target: {checked: true}}});
    });

    test('Updates context feature size value on input change', () => {
        const {FeatureCheckbox} = require('../featureCheckbox');
        const {NumericInput} = require('@blueprintjs/core');
        const {FormContext} = require('rf-form-utils');

        const component = render(
            <FormContext.Provider value={{
                updateField: mockUpdateField,
                fields:      {someFeature: {value: true}}
            }}>
                <FeatureCheckbox feature={'someFeature'}/>
            </FormContext.Provider>
        );

        component.root.findByType(NumericInput).props.onValueChange(123, '123');

        expect(mockUpdateField).toHaveBeenCalledWith({path: 'extended.desiredFeatures.someFeature.size', evt: {target: {value: 123}}});
    });
});
