import React from 'react';
import PropTypes from 'prop-types';
import {FormGroup} from '@blueprintjs/core';
import css from 'styled-jsx/css';
import * as _ from 'lodash';

import {T} from 'rf-i18n';
import {FleetsterIcon} from 'rf-icon';
import {colors, screenSizes} from 'rf-styles';

import {FeatureCheckbox} from './featureCheckbox';

const styles = css`
.feature-group-header {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
}

.feature-group-title {
    color: ${colors.darkGray};
    font-size: 20px;
    margin: 0 10px;
}

.feature-group-title-no-icon {
    color: ${colors.darkGray};
    font-size: 20px;
    margin: 0;
}

.feature-group-separator {
    height: 1px;
    border-width: 0;
    background-color: ${colors.blue};
}

.feature-group {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
}

@media (min-width: ${screenSizes.xs}px) { .feature-group {
    flex-direction: row;
}}`;

export function FeatureGroup({name, icon, features, placeholderLabelGenerator = _.identity}) {
    return (
        <FormGroup>
            <div className={'feature-group-header'}>
                {icon && <div className={'center-content'}>
                    <FleetsterIcon icon={icon} customStyles={{color: colors.blue}}/>
                </div>}
                <h4 className={icon ? 'feature-group-title' : 'feature-group-title-no-icon'}>
                    <T>{`form.featureGroup.${name}`}</T>
                </h4>
            </div>
            <hr className={'feature-group-separator'}/>
            <div className={'feature-group'}>
                {features.map((feature, key) => {
                    return <FeatureCheckbox feature={feature} key={key} placeholder={placeholderLabelGenerator(feature)}/>;
                })}
            </div>
            <style jsx>{styles}</style>
        </FormGroup>
    );
}

FeatureGroup.propTypes = {
    name:                      PropTypes.string,
    icon:                      PropTypes.string,
    features:                  PropTypes.arrayOf(PropTypes.string),
    placeholderLabelGenerator: PropTypes.func
};
