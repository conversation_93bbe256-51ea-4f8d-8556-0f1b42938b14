import React from 'react';
import {Card, Classes, FormGroup} from '@blueprintjs/core';

import {colors} from 'rf-styles';
import {T} from 'rf-i18n';

export function WhiteLabelInfo() {
    return (
        <FormGroup>
            <Card elevation={1} className={Classes.TEXT_LARGE} style={{backgroundColor: colors.infoCardBackground}}>
                <T>form.info.whitelabelExplanation</T>
            </Card>
        </FormGroup>
    );
}
