import _ from 'lodash';
import {parsePhoneNumberFromString} from 'libphonenumber-js/max';

// eslint-disable-next-line no-control-regex,max-len
const emailRegex = /(?:[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?\.)+[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?|\[(?:(?:(2(5[0-5]|[0-4][0-9])|1[0-9][0-9]|[1-9]?[0-9]))\.){3}(?:(2(5[0-5]|[0-4][0-9])|1[0-9][0-9]|[1-9]?[0-9])|[a-zA-Z0-9-]*[a-zA-Z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])/;

const disallowedCharactersRegex = /[^\w\d\u00C0-\u017F ,!#$%&'*+/=?^_`{|}~()™®℠㋏ªº-]/iu;

const requiredFields = ['salutation', 'firstName', 'lastName', 'company', 'phone', 'email'];

const checkForDisallowedCharsFields = ['firstName', 'lastName', 'company'];

function addMissingFieldErrors(form) {
    return _.transform(requiredFields, (formErrors, fieldName) => {
        if (!_.get(form, fieldName, '').trim()) {
            formErrors[fieldName] = `form.error.${fieldName}Missing`;
        }
    }, {});
}

function addNonAlphaNumericFieldErrors(formErrors, form) {
    return _.transform(checkForDisallowedCharsFields, (formErrors, fieldName) => {
        if (disallowedCharactersRegex.test(_.get(form, fieldName, ''))) {
            formErrors[fieldName] = 'form.error.onlyAlphanumericChars';
        }
    }, formErrors);
}

function addWrongFormatErrors(formErrors, form) {
    const email = _.get(form, 'email', '');

    if (!emailRegex.test(email)) {
        formErrors.email = 'form.error.emailWrongFormat';
    }

    const phoneNumberObject = parsePhoneNumberFromString(form.phone.replace('00', '+'));

    if (!phoneNumberObject || !phoneNumberObject.isValid()) {
        formErrors.phone = 'form.error.phoneWrongFormat';
    }
}

export function getFormErrors(form) {
    const formErrors = addMissingFieldErrors(form);

    if (!_.isEmpty(formErrors)) {
        return formErrors;
    }

    addNonAlphaNumericFieldErrors(formErrors, form);
    addWrongFormatErrors(formErrors, form);

    return formErrors;
}
