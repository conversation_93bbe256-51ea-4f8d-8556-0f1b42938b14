import siteConfig from 'rf-data/siteConfig.json';

import {performNetworkRequest} from '../performNetworkRequest';
import {salesApi} from '../salesApi';

jest.mock('../performNetworkRequest', () => ({
    performNetworkRequest: jest.fn()
}));

const languageSiteConfig = siteConfig.i18n[siteConfig.language];

const createLeadRequestURL = languageSiteConfig.salesEndpoints[0].url;

const getLeadUpdateRequestURL =
    hashedId => `${languageSiteConfig.salesEndpoints[0].url}/${hashedId}`;

describe('Unit tests: salesApi', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('createLead', () => {
        const exampleForm = {
            firstName: 'firstName',
            lastName:  'lastName',
            company:   'company',
            email:     '<EMAIL>',
            phone:     '00351918273647'
        };

        test('correctly sends a request to create a lead', async() => {
            const hashedId = 'testHashedId';
            performNetworkRequest.mockImplementationOnce(() => {
                return Promise.resolve({
                    hashedId,
                    errors: []
                });
            });

            const response = await salesApi.createLead(exampleForm);

            expect(performNetworkRequest).toHaveBeenLastCalledWith(createLeadRequestURL, {
                method: 'POST',
                body:   exampleForm
            });

            expect(response.hashedId).toEqual(hashedId);
        });

        test('uses alternative salesServiceUrl to create a lead, if provided', async() => {
            const hashedId = 'testHashedId';
            performNetworkRequest.mockImplementationOnce(() => {
                return Promise.resolve({
                    hashedId,
                    errors: []
                });
            });

            const response = await salesApi.createLead(exampleForm, 'test2');

            expect(performNetworkRequest).toHaveBeenLastCalledWith(languageSiteConfig.salesEndpoints[1].url, {
                method: 'POST',
                body:   exampleForm
            });

            expect(response.hashedId).toEqual(hashedId);
        });

        test('uses default salesServiceUrl to create a lead, if provided endpoint is not found', async() => {
            await salesApi.createLead(exampleForm, 'test3');

            expect(performNetworkRequest).toHaveBeenLastCalledWith(createLeadRequestURL, {
                method: 'POST',
                body:   exampleForm
            });
        });
    });

    describe('updateLead', () => {
        const exampleForm = {
            firstName: 'firstName',
            lastName:  'lastName',
            company:   'company',
            email:     '<EMAIL>',
            phone:     '00351918273647'
        };

        test('correctly sends a request to update a lead', async() => {
            const hashedId = 'testHashedId';
            performNetworkRequest.mockImplementationOnce(() => {
                return Promise.resolve({
                    errors: []
                });
            });

            await salesApi.updateLead(hashedId, exampleForm);

            expect(performNetworkRequest).toHaveBeenLastCalledWith(getLeadUpdateRequestURL(hashedId), {
                method: 'PUT',
                body:   exampleForm
            });
        });

        test('uses alternative salesServiceUrl to update a lead, if provided', async() => {
            const hashedId = 'testHashedId';
            performNetworkRequest.mockImplementationOnce(() => {
                return Promise.resolve({
                    errors: []
                });
            });

            await salesApi.updateLead(hashedId, exampleForm);

            expect(performNetworkRequest).toHaveBeenLastCalledWith(getLeadUpdateRequestURL(hashedId), {
                method: 'PUT',
                body:   exampleForm
            });
        });
    });
});
