describe('Unit tests: performNetworkRequest', () => {
    let performNetworkRequest;

    beforeEach(() => {
        fetch.mockImplementation(() => Promise.resolve({
            status: 200,
            json:   () => Promise.resolve({}),
            ok:     true
        }));

        performNetworkRequest = require('../performNetworkRequest').performNetworkRequest;
    });

    test('does not overwrite the contents of the input requestInit object\'s body', async() => {
        const originalBody = 'dontOverwriteMe';
        const requestInit = {
            body: originalBody
        };

        await performNetworkRequest('localhost', requestInit);

        expect(requestInit.body).toEqual(originalBody);
    });

    test('adds the headers field (with Content-Type application/json) to the input requestInit object if it is not present', async() => {
        const requestInit = {
            headers: {
                'Content-type': 'application/json'
            },
            body: {}
        };

        await performNetworkRequest('localhost', requestInit);

        expect(fetch).toHaveBeenLastCalledWith('localhost', expect.objectContaining({
            body:    JSON.stringify(requestInit.body),
            headers: {
                'Content-type': 'application/json'
            }
        }));
    });

    test('stringifies the body object before sending it', async() => {
        const requestInit = {
            headers: {
                'Content-type': 'application/json'
            },
            body: {test: 1}
        };

        await performNetworkRequest('localhost', requestInit);

        expect(fetch).toHaveBeenLastCalledWith('localhost', expect.objectContaining({
            body: JSON.stringify(requestInit.body)
        }));
    });

    test('sends a GET request if the method is not specified in the input requestInit object', async() => {
        await performNetworkRequest('localhost', {});

        expect(fetch).toHaveBeenLastCalledWith('localhost', expect.objectContaining({
            method: 'GET'
        }));
    });

    test('uses the method sent in the input requestInit object if present', async() => {
        await performNetworkRequest('localhost', {method: 'HEAD'});

        expect(fetch).toHaveBeenLastCalledWith('localhost', expect.objectContaining({
            method: 'HEAD'
        }));
    });

    test('throws an error if the resulting status code is >= 400 (error)', async() => {
        fetch.mockReturnValueOnce(Promise.resolve({
            status:     400,
            statusText: 'Bad Request',
            ok:         true
        }));

        await expect(performNetworkRequest('localhost', {})).rejects.toEqual(
            new Error('Error performing GET request to localhost: 400 Bad Request')
        );
    });

    test('does not throw an error if the resulting status code is 3xx', async() => {
        fetch.mockReturnValueOnce(Promise.resolve({
            status: 301,
            json:   () => Promise.resolve({}),
            ok:     true
        }));

        await expect(performNetworkRequest('localhost', {})).resolves.toEqual({});
    });

    test('does not throw an error if the resulting status code is 2xx', async() => {
        fetch.mockReturnValueOnce(Promise.resolve({
            status: 201,
            json:   () => Promise.resolve({}),
            ok:     true
        }));

        await expect(performNetworkRequest('localhost', {})).resolves.toEqual({});
    });

    test('returns the response data if it is wrapped in a data field', async() => {
        fetch.mockReturnValueOnce(Promise.resolve({
            status: 201,
            json:   () => Promise.resolve({data: {test: 1}}),
            ok:     true
        }));

        await expect(performNetworkRequest('localhost', {})).resolves.toEqual({test: 1});
    });

    test('returns the response data if it is not wrapped in a data field', async() => {
        fetch.mockReturnValueOnce(Promise.resolve({
            status: 201,
            json:   () => Promise.resolve({test: 1}),
            ok:     true
        }));

        await expect(performNetworkRequest('localhost', {})).resolves.toEqual({test: 1});
    });
});
