import dynamic from 'next/dynamic';

export const ResellerSignupForm = dynamic(() => import('./forms/resellerSignupForm').then(module => module.ResellerSignupForm));
export const CustomerInformationForm = dynamic(() => import('./forms/customerInformationForm').then(module => module.CustomerInformationForm));
export const SignupForm = dynamic(() => import('./forms/signupForm').then(module => module.SignupForm));
export const AdditionalInformationForm = dynamic(() => import('./forms/additionalInformationForm').then(module => module.AdditionalInformationForm));
export const DesiredFeaturesForm = dynamic(() => import('./forms/desiredFeaturesForm').then(module => module.DesiredFeaturesForm));
export const Hero = dynamic(() => import('./hero').then(module => module.Hero));
export const TextSection = dynamic(() => import('./textSection').then(module => module.TextSection));
export const ImageTextSection = dynamic(() => import('./imageTextSection').then(module => module.ImageTextSection));
export const VideoSection = dynamic(() => import('./videoSection').then(module => module.VideoSection));
export const FeaturesSection = dynamic(() => import('./featuresSection').then(module => module.FeaturesSection));
export const FeaturesComparisonSection = dynamic(() => import('./featuresComparisonSection').then(module => module.FeaturesComparisonSection));
export const ProductPricingSection = dynamic(() => import('./productPricingSection').then(module => module.ProductPricingSection));
export const ProductOverviewSection = dynamic(() => import('./productOverviewSection').then(module => module.ProductOverviewSection));
export const CtaSection = dynamic(() => import('./ctaSection').then(module => module.CtaSection));
export const ProductStructuredData = dynamic(() => import('./structuredDataSection').then(module => module.ProductStructuredData));
export const ArticleHeadingSection = dynamic(() => import('./articleHeadingSection').then(module => module.ArticleHeadingSection));
export const ImagesSection = dynamic(() => import('./imagesSection').then(module => module.ImagesSection));
export const TitleSection = dynamic(() => import('./titleSection').then(module => module.TitleSection));
export const ContactCardsSection = dynamic(() => import('./contactCardsSection').then(module => module.ContactCardsSection));
