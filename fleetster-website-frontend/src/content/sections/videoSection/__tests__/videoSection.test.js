import React from 'react';
import _ from 'lodash';

import {VideoSection} from '../';

jest.mock('rf-components/simpleBlockContent', () => ({
    __esModule: true,
    default:    ({blocks, ...rest}) => <div {...rest}>{blocks}</div> // eslint-disable-line react/display-name, react/prop-types
}));
jest.mock('next/head', () => props => <div {...props} />);

describe('videoSection', () => {
    let mockProps;

    beforeEach(() => {
        mockProps = {
            heading:      'huehuebr',
            content:      [{_type: 'embedHTML', _key: 1}, {_type: 'figure', _key: 2}],
            videoUrl:     {href: 'https://www.vimeo.com/1212431'},
            sendToRight:  false,
            _key:         'some key',
            callToAction: {
                link:      'link',
                className: 'className',
                _key:      'key',
                title:     'title',
                route:     {}
            }
        };

        window.IntersectionObserver = jest.fn(() => ({
            observe: jest.fn()
        }));
    });

    function renderVideoSection(props, showVideo = true, setRef = true) {
        const element = <VideoSection {...props}/>;

        const ref = setRef ? 'workaround to make useRef work: https://github.com/facebook/react/issues/7740' : null;

        const component = render(element, {createNodeMock: () => ref});

        act(() => {
            component.update(element);
        });

        const callback = _.get(window, 'IntersectionObserver.mock.calls.0.0');

        act(() => {
            callback && callback([{isIntersecting: showVideo}]);
        });

        return component;
    }

    test('renders without errors', () => {
        const component = renderVideoSection(mockProps);

        expect(component.toJSON()).toMatchSnapshot();
    });

    test('Correctly loads vimeo video', () => {
        const vimeoPropsWithoutSlash = {
            ...mockProps,
            videoUrl: {href: 'https://www.vimeo.com/1212431'}
        };

        const component = renderVideoSection(vimeoPropsWithoutSlash);

        expect(component.root.findByType('iframe')).toBeTruthy();
        expect(component.toJSON()).toMatchSnapshot();
    });

    test('Correctly parses and loads vimeo url with slash at the end', () => {
        const vimeoProps = {
            ...mockProps,
            videoUrl: {href: 'https://www.vimeo.com/1212431/'}
        };

        const component = renderVideoSection(vimeoProps);

        expect(component.root.findByType('iframe')).toBeTruthy();
        expect(component.toJSON()).toMatchSnapshot();
    });

    test('Correctly parses and loads youtube url', () => {
        const youtubeProps = {
            ...mockProps,
            videoUrl: {href: 'https://www.youtube.com/watch?v=1234567'}
        };

        const component = renderVideoSection(youtubeProps);

        expect(component.root.findByType('iframe')).toBeTruthy();
        expect(component.toJSON()).toMatchSnapshot();
    });

    test('Correctly parses and loads youtube url with extra params', () => {
        const youtubePropsWithParams = {
            ...mockProps,
            videoUrl: {href: 'https://www.youtube.com/watch?v=1234567&extraParam=huehuebr'}
        };

        const component = renderVideoSection(youtubePropsWithParams);

        expect(component.root.findByType('iframe')).toBeTruthy();
        expect(component.toJSON()).toMatchSnapshot();
    });

    test('renders without errors with no heading or content', () => {
        delete mockProps.heading;
        delete mockProps.content;

        const component = renderVideoSection(mockProps);

        expect(component.toJSON()).toMatchSnapshot();
    });

    test('renders without errors with no callToAction', () => {
        delete mockProps.callToAction;

        const component = renderVideoSection(mockProps);

        expect(component.toJSON()).toMatchSnapshot();
    });

    test('adds .reverse-section class when video is set to be on the left', () => {
        mockProps.sendToRight = false;
        const component = renderVideoSection(mockProps);

        expect(component.root.findByType('div').props.className).toContain('reverse-section');
    });

    test('removes .reverse-section class when video is set to be on the right', () => {
        mockProps.sendToRight = true;
        const component = renderVideoSection(mockProps);

        expect(component.root.findByType('div').props.className).not.toContain('reverse-section');
    });

    test('If callToAction has no title, dont show it', () => {
        delete mockProps.callToAction.title;

        const component = renderVideoSection(mockProps);

        expect(() => component.root.findByType('a')).toThrow();
    });

    test('If heading is falsy, dont show it', () => {
        delete mockProps.heading;

        const component = renderVideoSection(mockProps);

        expect(() => component.root.findByType('h4')).toThrow();
    });

    test('writes correct structuredData', () => {
        const youtubeVideoData = {
            id:          'rOqUiXhECos',
            publishedAt: '2019-05-17T15:00:13Z',
            title:       'Keanu Reeves Plays With Puppies While Answering Fan Questions',
            description: 'Keanu Reeves plays with puppies and answers questions',
            thumbnails:  {
                standard: {
                    url: 'https://i.ytimg.com/vi/rOqUiXhECos/sddefault.jpg'
                }
            },
            duration:  'PT5M27S',
            viewCount: '18030191'
        };

        mockProps.youtubeVideoData = youtubeVideoData;

        const component = renderVideoSection(mockProps);

        expect(component.toJSON()).toMatchSnapshot();
    });

    test('renders no video when isIntersecting is false', () => {
        const component = renderVideoSection(mockProps, false);

        expect(() => component.root.findByType('iframe')).toThrow();
    });

    test('renders video component if IntersectionObserver is not supported', () => {
        delete window.IntersectionObserver;

        const component = renderVideoSection(mockProps);

        expect(component.root.findByType('iframe')).toBeTruthy();
    });

    test('when section is rendered the first time and ref is not set, do not call observe', () => {
        renderVideoSection(mockProps, true, false);

        expect(window.IntersectionObserver).not.toHaveBeenCalled();
    });
});
