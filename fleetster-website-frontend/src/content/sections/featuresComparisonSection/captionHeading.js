import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {colors, screenSizes, vSpacing} from 'rf-styles';
import {SVGIcon} from 'rf-components';

const styles = css`
.caption-heading {
    display: flex;
    justify-content: flex-start;
    column-gap: 10px;
    white-space: nowrap;
    flex-wrap: wrap;
}
@media (min-width: ${screenSizes.xs}px) { .caption-heading {
    column-gap: 20px;
}}  
@media (min-width: ${screenSizes.s}px) { .caption-heading {
    display: none;
}}
  
.header-container {
    display: flex;
    align-items: center;
    gap: 5px;
}
  
.header-icon {
    width: 12px; 
    height: 12px;
    background-color: ${colors.darkGray};
}
@media (min-width: ${screenSizes.xs}px) { .header-icon {
    width: 14px;
    height: 14px;
}}

.p {
  margin-bottom: ${vSpacing.xxs}px;
  font-weight: 600;
}  
`;

export function CaptionHeading({icons}) {
    if (!icons) {
        return null;
    }

    return (<div className={'caption-heading'}>

        {icons.map(icon => (<div key={icon.description} className={'header-container'}>
            <SVGIcon image={icon.icon} className={'header-icon'} alt={icon.iconAlt} styles={styles} />
            <span className={'p'}>{icon.description}</span>
        </div>))}

        <style jsx>{styles}</style>
    </div>);
}

CaptionHeading.propTypes = {
    icons: PropTypes.array
};
