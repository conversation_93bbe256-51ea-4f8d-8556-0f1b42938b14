import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';
import _ from 'lodash';

import {vSpacing, colors} from 'rf-styles';

import {Table} from './table';
import {CaptionHeading} from './captionHeading';

const styles = css`
h2 { 
    margin-bottom: ${vSpacing.l}px;
    color: ${colors.blue};
}`;

export function FeaturesComparisonSection({heading, featuresTable, icons}) {
    return (
        <section className={'page-horizontal-padding section-vertical-padding'} id={_.kebabCase(heading)}>
            <h2 className={'h1'} style={{textAlign: 'center'}}>{heading}</h2>
            <CaptionHeading icons={icons}/>
            <Table data={featuresTable} headerIcons={icons}/>
            <style jsx>{styles}</style>
        </section>
    );
}

FeaturesComparisonSection.propTypes = {
    heading:       PropTypes.string,
    featuresTable: PropTypes.object,
    icons:         PropTypes.array
};
