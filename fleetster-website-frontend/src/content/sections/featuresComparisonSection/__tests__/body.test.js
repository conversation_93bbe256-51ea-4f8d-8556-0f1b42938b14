import React from 'react';

import {Body} from '../body';
import {featuresTableData} from './mockData';

describe('Feature comparison table - Body', () => {
    test('renders correctly', () => {
        const [header, ...rows] = featuresTableData.rows;
        const columnCount = header.cells.length;

        const component = render(<Body columnCount={columnCount} rows={rows}/>);

        expect(component.toJSON()).toMatchSnapshot();
    });

    test('renders nothing if no rows', () => {
        const header = featuresTableData.rows[0];
        const columnCount = header.cells.length;

        const component = render(<Body columnCount={columnCount}/>);

        expect(component.toJSON()).toBeNull();
    });
});
