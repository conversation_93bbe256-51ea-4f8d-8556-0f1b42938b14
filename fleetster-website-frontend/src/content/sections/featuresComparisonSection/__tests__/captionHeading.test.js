import React from 'react';

import {CaptionHeading} from '../captionHeading';
import {featuresTableIcons} from './mockData';

describe('Feature comparison table - CaptionHeading Component', () => {
    beforeEach(() => {
        // clean document body
        document.body.innerHTML = '';
    });

    test('renders correctly', () => {
        const component = render(<CaptionHeading icons={featuresTableIcons} />);
        expect(component).toMatchSnapshot();
    });

    test('renders nothing if icons is undefined', () => {
        const component = render(<CaptionHeading />);
        expect(component.toJSON()).toBeNull();
    });
});
