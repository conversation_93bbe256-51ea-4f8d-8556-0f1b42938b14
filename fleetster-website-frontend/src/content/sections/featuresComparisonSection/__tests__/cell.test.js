import React from 'react';
import {Cell} from '../cell';

describe('Feature comparison table - Cell', () => {
    test('renders "negative" icon when no cell content', () => {
        const component = render(<Cell content={''}/>);

        expect(component.root.findAllByProps({icon: 'negative'})).toHaveLength(1);
    });

    test('renders "confirm" icon when content is "x"', () => {
        const component = render(<Cell content={'x'}/>);

        expect(component.root.findAllByProps({icon: 'confirm'})).toHaveLength(1);
    });

    test('Trims content before evaluating it', () => {
        const component = render(<Cell content={'  x  '}/>);

        expect(component.root.findAllByProps({icon: 'confirm'})).toHaveLength(1);
    });

    test('returns same content if it does not fall under any icon class condition', () => {
        const component = render(<Cell content={'huehuebr'}/>);

        expect(component.root.findByType('td').children[0]).toContain('huehuebr');
    });

    test('accepts a component as content', () => {
        const component = render(<Cell content={<div>huehuebr</div>}/>);

        expect(component.root.findByType('td').children[0].type).toBe('div');
    });
});
