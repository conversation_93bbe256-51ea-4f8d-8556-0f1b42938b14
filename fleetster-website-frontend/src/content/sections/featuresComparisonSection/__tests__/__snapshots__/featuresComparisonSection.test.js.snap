// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FeaturesComparisonSection renders without errors 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding"
  id="huehuebr"
>
  <h2
    className="h1"
    style={
      {
        "textAlign": "center",
      }
    }
  >
    huehuebr
  </h2>
  <div
    className="caption-heading"
  >
    <div
      className="header-container"
    >
      <div
        className="header-icon"
        role="img"
        style={
          {
            "WebkitMaskImage": "url(https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?auto=format)",
            "WebkitMaskPosition": "center",
            "WebkitMaskRepeat": "no-repeat",
            "WebkitMaskSize": "contain",
            "mask": "url(https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?auto=format) no-repeat center / contain",
          }
        }
      >
        <style />
      </div>
      <span
        className="p"
      >
        Basic
      </span>
    </div>
    <div
      className="header-container"
    >
      <div
        className="header-icon"
        role="img"
        style={
          {
            "WebkitMaskImage": "url(https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?auto=format)",
            "WebkitMaskPosition": "center",
            "WebkitMaskRepeat": "no-repeat",
            "WebkitMaskSize": "contain",
            "mask": "url(https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?auto=format) no-repeat center / contain",
          }
        }
      >
        <style />
      </div>
      <span
        className="p"
      >
        Pro
      </span>
    </div>
    <div
      className="header-container"
    >
      <div
        className="header-icon"
        role="img"
        style={
          {
            "WebkitMaskImage": "url(https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?auto=format)",
            "WebkitMaskPosition": "center",
            "WebkitMaskRepeat": "no-repeat",
            "WebkitMaskSize": "contain",
            "mask": "url(https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?auto=format) no-repeat center / contain",
          }
        }
      >
        <style />
      </div>
      <span
        className="p"
      >
        Enterprise
      </span>
    </div>
    <style />
  </div>
  <div>
    <table>
      <thead>
        <tr
          className="p"
        >
          <th>
            <div>
              <span>
                Features included
              </span>
            </div>
          </th>
          <th>
            <div>
              <div
                className="header-icon"
                role="img"
                style={
                  {
                    "WebkitMaskImage": "url(https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?auto=format)",
                    "WebkitMaskPosition": "center",
                    "WebkitMaskRepeat": "no-repeat",
                    "WebkitMaskSize": "contain",
                    "mask": "url(https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?auto=format) no-repeat center / contain",
                  }
                }
              >
                <style />
              </div>
              <span>
                Basic
              </span>
            </div>
          </th>
          <th>
            <div>
              <div
                className="header-icon"
                role="img"
                style={
                  {
                    "WebkitMaskImage": "url(https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?auto=format)",
                    "WebkitMaskPosition": "center",
                    "WebkitMaskRepeat": "no-repeat",
                    "WebkitMaskSize": "contain",
                    "mask": "url(https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?auto=format) no-repeat center / contain",
                  }
                }
              >
                <style />
              </div>
              <span>
                Pro
              </span>
            </div>
          </th>
          <th>
            <div>
              <div
                className="header-icon"
                role="img"
                style={
                  {
                    "WebkitMaskImage": "url(https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?auto=format)",
                    "WebkitMaskPosition": "center",
                    "WebkitMaskRepeat": "no-repeat",
                    "WebkitMaskSize": "contain",
                    "mask": "url(https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?auto=format) no-repeat center / contain",
                  }
                }
              >
                <style />
              </div>
              <span>
                Enterprise
              </span>
            </div>
          </th>
        </tr>
        <style />
      </thead>
      <tbody>
        <tr>
          <td
            className="p"
          >
            Booking Platform
            <style />
          </td>
          <td
            className="p"
          >
            <span
              className="bp3-icon"
            >
              <div
                height="inherit"
                src="/v4/svgs/confirm.svg"
                width="inheritpx"
              />
            </span>
            <style />
          </td>
          <td
            className="p"
          >
            <span
              className="bp3-icon"
            >
              <div
                height="inherit"
                src="/v4/svgs/negative.svg"
                width="inheritpx"
              />
            </span>
            <style />
          </td>
          <td
            className="p"
          >
            <span
              className="bp3-icon"
            >
              <div
                height="inherit"
                src="/v4/svgs/confirm.svg"
                width="inheritpx"
              />
            </span>
            <style />
          </td>
        </tr>
        <tr>
          <td
            className="p"
          >
            Smartphone access
            <style />
          </td>
          <td
            className="p"
          >
            <span
              className="bp3-icon"
            >
              <div
                height="inherit"
                src="/v4/svgs/negative.svg"
                width="inheritpx"
              />
            </span>
            <style />
          </td>
          <td
            className="p"
          >
            <span
              className="bp3-icon"
            >
              <div
                height="inherit"
                src="/v4/svgs/confirm.svg"
                width="inheritpx"
              />
            </span>
            <style />
          </td>
          <td
            className="p"
          >
            <span
              className="bp3-icon"
            >
              <div
                height="inherit"
                src="/v4/svgs/confirm.svg"
                width="inheritpx"
              />
            </span>
            <style />
          </td>
        </tr>
        <style />
      </tbody>
      <style />
    </table>
  </div>
  <style />
</section>
`;

exports[`FeaturesComparisonSection renders without table if featuresTable is undefined 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding"
  id="huehuebr"
>
  <h2
    className="h1"
    style={
      {
        "textAlign": "center",
      }
    }
  >
    huehuebr
  </h2>
  <div
    className="caption-heading"
  >
    <div
      className="header-container"
    >
      <div
        className="header-icon"
        role="img"
        style={
          {
            "WebkitMaskImage": "url(https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?auto=format)",
            "WebkitMaskPosition": "center",
            "WebkitMaskRepeat": "no-repeat",
            "WebkitMaskSize": "contain",
            "mask": "url(https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?auto=format) no-repeat center / contain",
          }
        }
      >
        <style />
      </div>
      <span
        className="p"
      >
        Basic
      </span>
    </div>
    <div
      className="header-container"
    >
      <div
        className="header-icon"
        role="img"
        style={
          {
            "WebkitMaskImage": "url(https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?auto=format)",
            "WebkitMaskPosition": "center",
            "WebkitMaskRepeat": "no-repeat",
            "WebkitMaskSize": "contain",
            "mask": "url(https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?auto=format) no-repeat center / contain",
          }
        }
      >
        <style />
      </div>
      <span
        className="p"
      >
        Pro
      </span>
    </div>
    <div
      className="header-container"
    >
      <div
        className="header-icon"
        role="img"
        style={
          {
            "WebkitMaskImage": "url(https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?auto=format)",
            "WebkitMaskPosition": "center",
            "WebkitMaskRepeat": "no-repeat",
            "WebkitMaskSize": "contain",
            "mask": "url(https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?auto=format) no-repeat center / contain",
          }
        }
      >
        <style />
      </div>
      <span
        className="p"
      >
        Enterprise
      </span>
    </div>
    <style />
  </div>
  <style />
</section>
`;
