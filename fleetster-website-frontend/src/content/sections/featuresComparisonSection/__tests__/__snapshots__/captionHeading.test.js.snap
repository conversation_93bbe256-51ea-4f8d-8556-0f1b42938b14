// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Feature comparison table - CaptionHeading Component renders correctly 1`] = `
<div
  className="caption-heading"
>
  <div
    className="header-container"
  >
    <div
      className="header-icon"
      role="img"
      style={
        {
          "WebkitMaskImage": "url(https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?auto=format)",
          "WebkitMaskPosition": "center",
          "WebkitMaskRepeat": "no-repeat",
          "WebkitMaskSize": "contain",
          "mask": "url(https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?auto=format) no-repeat center / contain",
        }
      }
    >
      <style />
    </div>
    <span
      className="p"
    >
      Basic
    </span>
  </div>
  <div
    className="header-container"
  >
    <div
      className="header-icon"
      role="img"
      style={
        {
          "WebkitMaskImage": "url(https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?auto=format)",
          "WebkitMaskPosition": "center",
          "WebkitMaskRepeat": "no-repeat",
          "WebkitMaskSize": "contain",
          "mask": "url(https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?auto=format) no-repeat center / contain",
        }
      }
    >
      <style />
    </div>
    <span
      className="p"
    >
      Pro
    </span>
  </div>
  <div
    className="header-container"
  >
    <div
      className="header-icon"
      role="img"
      style={
        {
          "WebkitMaskImage": "url(https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?auto=format)",
          "WebkitMaskPosition": "center",
          "WebkitMaskRepeat": "no-repeat",
          "WebkitMaskSize": "contain",
          "mask": "url(https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?auto=format) no-repeat center / contain",
        }
      }
    >
      <style />
    </div>
    <span
      className="p"
    >
      Enterprise
    </span>
  </div>
  <style />
</div>
`;
