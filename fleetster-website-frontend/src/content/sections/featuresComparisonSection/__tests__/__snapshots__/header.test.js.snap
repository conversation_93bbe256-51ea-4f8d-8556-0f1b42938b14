// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Feature comparison table - Header renders correctly 1`] = `
<thead>
  <tr
    className="p"
  >
    <th>
      <div>
        <span>
          Features included
        </span>
      </div>
    </th>
    <th>
      <div>
        <div
          className="header-icon"
          role="img"
          style={
            {
              "WebkitMaskImage": "url(https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?auto=format)",
              "WebkitMaskPosition": "center",
              "WebkitMaskRepeat": "no-repeat",
              "WebkitMaskSize": "contain",
              "mask": "url(https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?auto=format) no-repeat center / contain",
            }
          }
        >
          <style />
        </div>
        <span>
          Basic
        </span>
      </div>
    </th>
    <th>
      <div>
        <div
          className="header-icon"
          role="img"
          style={
            {
              "WebkitMaskImage": "url(https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?auto=format)",
              "WebkitMaskPosition": "center",
              "WebkitMaskRepeat": "no-repeat",
              "WebkitMaskSize": "contain",
              "mask": "url(https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?auto=format) no-repeat center / contain",
            }
          }
        >
          <style />
        </div>
        <span>
          Pro
        </span>
      </div>
    </th>
    <th>
      <div>
        <div
          className="header-icon"
          role="img"
          style={
            {
              "WebkitMaskImage": "url(https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?auto=format)",
              "WebkitMaskPosition": "center",
              "WebkitMaskRepeat": "no-repeat",
              "WebkitMaskSize": "contain",
              "mask": "url(https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?auto=format) no-repeat center / contain",
            }
          }
        >
          <style />
        </div>
        <span>
          Enterprise
        </span>
      </div>
    </th>
  </tr>
  <style />
</thead>
`;
