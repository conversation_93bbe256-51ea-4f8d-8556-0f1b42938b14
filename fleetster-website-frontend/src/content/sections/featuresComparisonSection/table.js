import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';
import _ from 'lodash';

import {Header} from './header';
import {Body} from './body';

const styles = css`
table { 
    width: 100%;
    table-layout: fixed;
    border-collapse:collapse;
}`;

function setLinkedRows(rows, urlIndex) {
    return _.map(rows, row => {
        const url = row.cells[urlIndex];

        const [feature, ...rest] = [...row.cells.slice(0, urlIndex), ...row.cells.slice(urlIndex + 1)];

        return {
            ...row,
            cells: [
                url ? <a href={url} key={feature}>{feature}</a> : feature,
                ...rest
            ]
        };
    });
}

export function Table({data, headerIcons}) {
    if (!data) {
        return null;
    }

    let [header, ...rows] = data.rows;

    if (_.includes(header.cells, 'url')) {
        const urlIndex = header.cells.indexOf('url');
        header = {...header, cells: _.without(header.cells, 'url')};
        rows = setLinkedRows(rows, urlIndex);
    }

    const columnCount = header.cells.length;

    return (
        <div>
            <table>
                <Header cells={header.cells} icons={headerIcons} key={header.key}/>
                <Body columnCount={columnCount} rows={rows}/>
                <style jsx>{styles}</style>
            </table>
        </div>
    );
}

Table.propTypes = {
    data:        PropTypes.object,
    headerIcons: PropTypes.array
};
