// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`TextSection Renders with no data 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding"
  id=""
>
  <div
    className="rf-flex-container"
  >
    <div
      className="rf-cell rf-flex-6"
    >
      <style />
    </div>
  </div>
</section>
`;

exports[`TextSection does not render empty props 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding"
  id="heading-left"
>
  <div
    className="rf-flex-container"
  >
    <div
      className="rf-cell rf-flex-6"
    >
      <p
        className="h5 subheading"
      >
        Subheading Left
      </p>
      <h2
        className="h2"
      >
        Heading Left
      </h2>
      <div />
      <p
        className="footnote"
        dangerouslySetInnerHTML={
          {
            "__html": "<a href="#" target="_blank">Footnote Left</a>",
          }
        }
      />
      <style />
    </div>
    <div
      className="rf-flex-1"
    />
    <div
      className="rf-cell rf-flex-5"
    >
      <div
        className="h5 heading-dash"
      >
        _
      </div>
      <h3
        className="h3"
        dangerouslySetInnerHTML={
          {
            "__html": "Content Right <script>alert("Evil Script")</script>",
          }
        }
        style={
          {
            "fontWeight": 600,
          }
        }
      />
      <p
        className="footnote"
        dangerouslySetInnerHTML={
          {
            "__html": "<em>Footnote</em> Right <img src="http://notagoodwebsite.com/evil-image.png"/>",
          }
        }
      />
      <style />
    </div>
  </div>
</section>
`;
