import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {colors, fontWeights} from 'rf-styles';

import {MainHeading, SimpleBlockContent} from 'rf-components';

const styles = css`
.subheading {
    color: ${colors.lightBlue};
    font-weight: ${fontWeights.semiBold};
}
.footnote {
    font-weight: ${fontWeights.semiBold};
    margin-bottom: 0;
}`;

export function TextSectionLeft({subheading, heading, content, footnote, index}) {
    return (
        <div className={'rf-cell rf-flex-6'}>
            {subheading && <p className={'h5 subheading'}>{subheading}</p>}
            {heading && <MainHeading index={index} heading={heading} classNames={'h2'}/>}
            {typeof content === 'object' && <SimpleBlockContent blocks={content}/>}
            {footnote && <p className={'footnote'} dangerouslySetInnerHTML={{__html: footnote}}/>}
            <style jsx>{styles}</style>
        </div>
    );
}

TextSectionLeft.propTypes = {
    subheading: PropTypes.string,
    heading:    PropTypes.string,
    content:    PropTypes.array,
    footnote:   PropTypes.string,
    index:      PropTypes.number
};
