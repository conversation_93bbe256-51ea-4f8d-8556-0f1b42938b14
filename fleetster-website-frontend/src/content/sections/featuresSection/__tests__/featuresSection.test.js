import React from 'react';

import {FeaturesSection} from '../featuresSection';

describe('FeaturesSection', () => {
    const features = Array(4).fill()
        .map((item, index) => ({_key: index.toString()}));

    test('renders without errors', () => {
        const component = render(<FeaturesSection features={features} caption={'SECTION TITLE'}/>);

        expect(component).toMatchSnapshot();
    });

    test('renders with a hexcolor', () => {
        const component = render(<FeaturesSection
            headingHexColor={'18609D'}
            features={features} caption={'SECTION TITLE'}/>);

        expect(component).toMatchSnapshot();
    });
});
