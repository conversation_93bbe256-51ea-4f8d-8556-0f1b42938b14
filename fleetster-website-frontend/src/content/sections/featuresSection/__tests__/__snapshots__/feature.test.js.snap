// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Feature renders without errors 1`] = `
<a
  className="feature feature-with-ref"
  href="http://localhost:3000/v4/corsoroute"
>
  <div
    className="icon"
  >
    <div
      className="center-content"
      style={
        {
          "height": "100%",
          "width": "100%",
        }
      }
    >
      <img
        alt="FEATURE TITLE"
        decoding="async"
        height={40}
        loading="lazy"
        sizes="40px"
        src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=635,0,563,563&w=40&h=40&auto=format"
        srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=635,0,563,563&w=40&h=40&auto=format 40w"
        style={
          {
            "height": "auto",
            "maxHeight": 40,
            "maxWidth": 40,
            "width": "100%",
          }
        }
        width={40}
      />
    </div>
  </div>
  <div
    className="text"
  >
    <h3
      className="h4"
    >
      FEATURE TITLE
    </h3>
    <p>
      feature description
    </p>
  </div>
  <style />
</a>
`;
