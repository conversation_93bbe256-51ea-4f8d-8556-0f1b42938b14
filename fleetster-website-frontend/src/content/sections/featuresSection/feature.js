import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {colors, screenSizes, vSpacing} from 'rf-styles';
import {Image} from 'rf-components';
import {FEATURE_COLUMN_GAP, FEATURE_SECTION_COLUMN_GAP, ICON_SIZE_L, ICON_SIZE_M, ICON_SIZE_SM} from './constants';

const totalColumnGap = (FEATURE_COLUMN_GAP / 2) + (FEATURE_SECTION_COLUMN_GAP / 2);

const styles = css`
  .feature {
    display: flex;
    flex-direction: row;
    column-gap: ${FEATURE_COLUMN_GAP}px;
    text-decoration: none;
    align-self: stretch;
  }

  .feature-with-ref:hover {
    transform: scale(1.05);
    background: ${colors.hover};
    cursor: pointer;
  }

  .icon {
    min-width: ${ICON_SIZE_SM}px;
    max-width: ${ICON_SIZE_SM}px;
    height: ${ICON_SIZE_SM}px;
  }

  @media (min-width: ${screenSizes.xxs}px) {
    .icon {
      min-width: ${ICON_SIZE_M}px;
      max-width: ${ICON_SIZE_M}px;
      height: ${ICON_SIZE_M}px;
    }
  }

  @media (min-width: ${screenSizes.m}px) {
    .icon {
      min-width: ${ICON_SIZE_L}px;
      max-width: ${ICON_SIZE_L}px;
      height: ${ICON_SIZE_L}px;
    }
  }

  @media (min-width: ${screenSizes.xs}px) {
    .feature {
      flex-basis: calc(50% - ${totalColumnGap}px);
    }
  }
  @media (min-width: ${screenSizes.s}px) {
    .feature {
      flex-basis: calc(${100 / 3}% - ${totalColumnGap}px);
    }
  }

  .feature .h4 {
    margin: 0 0 ${vSpacing.xs * 2}px 0;
  }

  .feature p {
    margin: 0;
    color: ${colors.darkGray};
  }

  .feature .text {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
  }

  .feature:last-child {
    padding-bottom: 50px;
  }
`;

export function Feature({caption, description, icon, route, headerStyle}) {
    let className = '';
    let WrapperElement = 'div';
    if (route) {
        className = 'feature-with-ref';
        WrapperElement = 'a';
    }

    return (
        <WrapperElement className={`feature ${className}`} href={route?.href}>
            <div className={'icon'}>
                <Image image={icon} width={40} height={40} imageAlt={caption} layout={'intrinsic'}/>
            </div>
            <div className={'text'}>
                <h3 style={headerStyle} className={'h4'}>{caption}</h3>
                <p>{description}</p>
            </div>
            <style jsx>{styles}</style>
        </WrapperElement>
    );
}

Feature.propTypes = {
    caption:     PropTypes.string,
    description: PropTypes.string,
    icon:        PropTypes.object,
    route:       PropTypes.object,
    headerStyle: PropTypes.object
};
