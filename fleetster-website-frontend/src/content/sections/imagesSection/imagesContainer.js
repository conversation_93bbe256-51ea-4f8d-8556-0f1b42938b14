import React from 'react';
import _ from 'lodash';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {RFTitle} from 'rf-components';
import {hSpacing, screenSizes} from 'rf-styles';

import {SectionImage} from './sectionImage';

const styles = css`
.regular-images {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    column-gap: ${hSpacing.sm}px;
    background-origin: border-box;
    padding: 0px ${hSpacing.sm}px 0px ${hSpacing.sm}px;
}

@media (min-width: ${screenSizes.xs}px) {
    .regular-images {
        column-gap: ${hSpacing.m}px;
    }
}

@media (min-width: ${screenSizes.m}px) {
    .regular-images {
        column-gap: ${hSpacing.l}px;
    }
}

* :global(.h1) {
    margin-bottom: 0;
}
`;

export function ImagesContainer({images, title, index}) {
    return (
        <div>
            <RFTitle index={index} title={title} />
            <div className={'regular-images'}>
                {_.map(images, image => (
                    <SectionImage
                        image={image.image}
                        key={`${_.get(image, 'image.asset._ref')}-${index}`}
                        alt={image.alt}
                        link={image.link}
                        route={image.route}
                    />))}
            </div>
            <style jsx>{styles}</style>
        </div>
    );
}

ImagesContainer.propTypes = {
    title:  PropTypes.string,
    images: PropTypes.array,
    index:  PropTypes.number
};
