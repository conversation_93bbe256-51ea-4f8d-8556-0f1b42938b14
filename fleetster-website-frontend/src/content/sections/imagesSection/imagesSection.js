import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {ImagesContainer} from './imagesContainer';
import {MiniImagesContainer} from './miniImagesContainer';
import {vSpacing} from 'rf-styles';

const styles = css`
.images-section {
  padding: ${vSpacing.s}px 0;
}
`;

export function ImagesSection({content, minimized, index}) {
    return (
        <section className={'page-horizontal-padding'}>
            <div className={'images-section'}>
                {
                    minimized
                        ? <MiniImagesContainer content={content} index={index}/>
                        : <ImagesContainer title={content[0].title} images={content[0].images} index={index}/>
                }
            </div>
            <style jsx>{styles}</style>
        </section>
    );
}

ImagesSection.propTypes = {
    content:   PropTypes.array,
    minimized: PropTypes.bool,
    index:     PropTypes.number
};
