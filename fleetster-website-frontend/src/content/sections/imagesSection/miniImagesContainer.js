import * as _ from 'lodash';
import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {screenSizes} from 'rf-styles';

import {MiniImagesContent} from './miniImagesContent';

const styles = css`
.mini-images-container{
    display: block;
}

@media (min-width: ${screenSizes.s}px) {
    .mini-images-container{
        display: flex;
        justify-content: space-between;
    }
}
`;

export function MiniImagesContainer({content, index}) {
    const [leftContent, rightContent] = content;

    return (
        <div className={'mini-images-container'}>
            <MiniImagesContent title={leftContent.title} images={leftContent.images} index={index} />
            {!_.isEmpty(rightContent) && <MiniImagesContent title={rightContent.title} images={rightContent.images} index={index}/>}
            <style jsx>{styles}</style>
        </div>
    );
}

MiniImagesContainer.propTypes = {
    content: PropTypes.array,
    index:   PropTypes.number
};
