import React from 'react';
import {Hero} from '../hero';

describe('Hero', () => {
    let callToAction = {
        link:      'link',
        className: 'className',
        _key:      'key',
        title:     'title',
        route:     {_ref: 'route'}
    };

    let image = {
        _type: 'image',
        asset: {
            _ref:  'image-f6291580450291e8e61b85db49f1307f9e89149a-24x20-png',
            _type: 'reference'
        },
        height: 20,
        width:  24
    };

    let heroImages = [
        {
            title: 'image 1',
            image: {_key: '1', ...image},
            icon:  {_key: '2', ...image},
            alt:   'image 1'
        },
        {
            title: 'image 2',
            image: {_key: '3', ...image},
            icon:  {_key: '4', ...image},
            alt:   'image 2',
            link:  {
                _ref: 'page1'
            }
        },
        {
            title: 'image 3',
            image: {_key: '5', ...image},
            icon:  {_key: '6', ...image},
            alt:   'image 3'
        }
    ];

    test('renders without errors with single image', () => {
        const component = render(<Hero
            text={'huhuebr'}
            callToAction={callToAction}
            heroImages={heroImages.slice(0, 1)}
            backgroundImageLandscape={image}
        />);

        expect(component).toMatchSnapshot();
    });

    test('renders without errors with single image and no amp', () => {
        const component = render(<Hero
            text={'huhuebr'}
            callToAction={callToAction}
            heroImages={heroImages.slice(0, 1)}
            backgroundImageLandscape={image}
        />);

        expect(component).toMatchSnapshot();
    });

    test('renders without errors with multiple images', () => {
        const component = render(<Hero
            text={'huhuebr'}
            callToAction={callToAction}
            heroImages={heroImages}
            backgroundImageLandscape={image}
        />);

        expect(component).toMatchSnapshot();
    });

    test('renders without errors with multiple images and no-amp', () => {
        const component = render(<Hero
            text={'huhuebr'}
            callToAction={callToAction}
            heroImages={heroImages}
            backgroundImageLandscape={image}
        />);

        expect(component).toMatchSnapshot();
    });

    test('renders H1 if is the first element of the page', () => {
        const component = render(<Hero
            index={0}
            text={'huhuebr'}
            heroImages={heroImages.slice(0, 1)}
            backgroundImageLandscape={image}
            backgroundImageAlt={'bgAlt'}
        />);

        expect(component).toMatchSnapshot();
    });

    test('Adds light theme class when lightTheme is true', () => {
        const component = render(<Hero
            index={0}
            lightTheme={true}
            text={'huhuebr'}
            callToAction={callToAction}
            heroImages={heroImages.slice(0, 1)}
            backgroundImageLandscape={image}
            backgroundImageAlt={'bgAlt'}
        />);

        expect(component).toMatchSnapshot();
    });

    test('renders without background image', () => {
        const component = render(<Hero callToAction={callToAction} heroImages={heroImages.slice(0, 1)}/>);

        expect(component).toMatchSnapshot();
    });

    test('renders without hero image', () => {
        const component = render(<Hero
            text={'huhuebr'}
            callToAction={callToAction}
            backgroundImageLandscape={image}
        />);

        expect(component).toMatchSnapshot();
    });

    test('renders without the cta if is undefined', () => {
        const component = render(<Hero
            text={'huhuebr'}
            backgroundImageLandscape={image}
        />);

        expect(component).toMatchSnapshot();
    });

    test('renders without the cta if there is no title', () => {
        const component = render(<Hero
            text={'huhuebr'}
            callToAction={{}}
            backgroundImageLandscape={image}
        />);

        expect(component).toMatchSnapshot();
    });

    test('Does not render h5 if there`s no text for it', () => {
        const component = render(<Hero
            callToAction={callToAction}
            backgroundImageLandscape={image}
        />);

        expect(component.root.findAllByProps({className: 'h5'})).toHaveLength(0);
    });

    test('Eagerly loads background images if section shows at the top of the page', () => {
        let component = render(<Hero
            callToAction={callToAction}
            backgroundImageLandscape={image}
            index={1}/>);

        expect(component.root.findAllByProps({loading: 'eager'})).toHaveLength(1);
        expect(component.root.findAllByProps({loading: 'lazy'})).toHaveLength(0);
    });

    test('Lazily loads background images if section is down below in the page', () => {
        let component = render(<Hero
            callToAction={callToAction}
            backgroundImageLandscape={image}
            index={2}/>);

        expect(component.root.findAllByProps({loading: 'eager'})).toHaveLength(0);
        expect(component.root.findAllByProps({loading: 'lazy'})).toHaveLength(1);
    });
});
