import React from 'react';
import PropTypes from 'prop-types';

import {Image} from 'rf-components';

const heroBackgroundAlt = 'Hero Background';

export function HeroBackgroundImage({backgroundImageLandscape, optimizeForLCP}) {
    if (!backgroundImageLandscape) { return null; }

    return (
        <div className={'hero-bg'} >
            <Image
                image={backgroundImageLandscape}
                imageAlt={heroBackgroundAlt}
                layout={'cover'}
                priority={optimizeForLCP}/>
            <style jsx>{`
                .hero-bg {
                    position: absolute;
                    height: 100%;
                    width: 100%;
                    z-index: -1;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                }
            `}</style>
        </div>
    );
}

HeroBackgroundImage.propTypes = {
    backgroundImageLandscape: PropTypes.object,
    backgroundImagePortrait:  PropTypes.object,
    optimizeForLCP:           PropTypes.bool
};
