import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {Carousel, CarouselButtons, CarouselProvider, CarouselImage} from './';

const heroCarouselStyles = css`
.hero-carousel {
    width: 100%;
}
`;

function getImagesConfig(heroImages) {
    const {width, height} = heroImages[0].image;
    const heroIcons = heroImages.map(image => ({title: image.title, ...image.icon}));

    return {width, height, heroIcons};
}

export function HeroCarousel({id, heroImages, optimizeForLCP}) {
    const {heroIcons} = getImagesConfig(heroImages);

    return (
        <CarouselProvider context={{currentIndex: 0, intervalMs: 5000, totalItems: heroImages.length}}>
            <div className={'hero-carousel'}>
                <Carousel>
                    {heroImages.map((heroImage, index) => (<CarouselImage
                        optimizeForLCP={optimizeForLCP}
                        index={index}
                        key={`${heroImage.image?.asset?._ref}-${index}`}
                        heroImage={heroImage}/>))}
                </Carousel>
                <CarouselButtons carouselId={id} buttonIcons={heroIcons}/>

                <style jsx>{heroCarouselStyles}</style>
            </div>
        </CarouselProvider>
    );
}

HeroCarousel.propTypes = {
    id:             PropTypes.string,
    heroImages:     PropTypes.array,
    optimizeForLCP: PropTypes.bool
};

