import React from 'react';
import css from 'styled-jsx/css';
import PropTypes from 'prop-types';

import {hSpacing, screenSizes, vSpacing} from 'rf-styles';

import {CarouselButton} from './carouselButton';

const styles = css`
  .carousel-buttons {
    position: absolute;
    left: 0;
    right: 0;
    width: calc(100% - ${hSpacing.xxs}px);
    margin-bottom: ${vSpacing.xs}px;
    display: flex;
    justify-content: center;
    pointer-events: none;
  }

  @media (min-width: ${screenSizes.s}px) {
    .carousel-buttons {
      pointer-events: unset;
    }
  }

  @media (min-width: ${screenSizes.s}px) {
    .carousel-buttons {
      left: auto;
      display: block;
      width: 65px;
      height: 100%;
      top: 0;
      right: ${hSpacing.xxs}px;
    }
  }`;

export function CarouselButtons({carouselId, buttonIcons}) {
    return (
        <div id={`selector-${carouselId}`}
            className={'carousel-buttons'}>
            {buttonIcons.map((icon, index) => (<CarouselButton
                key={`${carouselId}-${index}`}
                index={index}
                buttonIcon={icon}
                carouselId={carouselId}/>))}
            <style jsx>{styles}</style>
        </div>
    );
}

CarouselButtons.propTypes = {
    carouselId:  PropTypes.string,
    buttonIcons: PropTypes.array
};
