import React from 'react';
import {createRoot} from 'react-dom/client';
import {act} from 'react-dom/test-utils';

import {useHorizontalSwipeEvent} from '../';

describe('Carousel Hooks', () => {
    let container;

    beforeEach(() => {
        container = document.createElement('div');
        document.body.appendChild(container);
    });

    afterEach(() => {
        document.body.removeChild(container);
        container = null;
    });

    const DummyDiv = ({onLeftSwipe, onRightSwipe, children}) => {
        const touchHandlers = useHorizontalSwipeEvent({onLeftSwipe, onRightSwipe});

        return (<div id={'swipe-div'} {...touchHandlers}>
            {children}
        </div>);
    };

    test('Swiping left should call left swipe handler', () => {
        const onLeftSwipe = jest.fn();

        act(() => {
            createRoot(container).render(
                <DummyDiv onLeftSwipe={onLeftSwipe}/>
            );
        });

        const swipeDiv = container.querySelector('#swipe-div');

        act(() => {
            swipeDiv.dispatchEvent(new TouchEvent('touchstart', {targetTouches: [{clientX: 151}], bubbles: true}));
        });

        act(() => {
            swipeDiv.dispatchEvent(new TouchEvent('touchmove', {targetTouches: [{clientX: 100}], bubbles: true}));
        });

        act(() => {
            swipeDiv.dispatchEvent(new TouchEvent('touchend', {bubbles: true}));
        });

        expect(onLeftSwipe).toBeCalled();
    });

    test('Swiping right should call right swipe handler', () => {
        const onRightSwipe = jest.fn();

        act(() => {
            createRoot(container).render(
                <DummyDiv onRightSwipe={onRightSwipe}/>
            );
        });

        const swipeDiv = container.querySelector('#swipe-div');

        act(() => {
            swipeDiv.dispatchEvent(new TouchEvent('touchstart', {targetTouches: [{clientX: 100}], bubbles: true}));
        });

        act(() => {
            swipeDiv.dispatchEvent(new TouchEvent('touchmove', {targetTouches: [{clientX: 151}], bubbles: true}));
        });

        act(() => {
            swipeDiv.dispatchEvent(new TouchEvent('touchend', {bubbles: true}));
        });

        expect(onRightSwipe).toBeCalled();
    });

    test('Pressing and moving mouse to left should call left swipe handler', () => {
        const onLeftSwipe = jest.fn();

        act(() => {
            createRoot(container).render(
                <DummyDiv onLeftSwipe={onLeftSwipe}/>
            );
        });

        const swipeDiv = container.querySelector('#swipe-div');

        act(() => {
            swipeDiv.dispatchEvent(new MouseEvent('mousedown', {clientX: 151, bubbles: true}));
        });

        act(() => {
            swipeDiv.dispatchEvent(new MouseEvent('mousemove', {clientX: 100, bubbles: true}));
        });

        act(() => {
            swipeDiv.dispatchEvent(new MouseEvent('mouseup', {bubbles: true}));
        });

        expect(onLeftSwipe).toBeCalled();
    });

    test('Pressing and moving mouse to right should call right swipe handler', () => {
        const onRightSwipe = jest.fn();

        act(() => {
            createRoot(container).render(
                <DummyDiv onRightSwipe={onRightSwipe}/>
            );
        });

        const swipeDiv = container.querySelector('#swipe-div');

        act(() => {
            swipeDiv.dispatchEvent(new MouseEvent('mousedown', {clientX: 100, bubbles: true}));
        });

        act(() => {
            swipeDiv.dispatchEvent(new MouseEvent('mousemove', {clientX: 151, bubbles: true}));
        });

        act(() => {
            swipeDiv.dispatchEvent(new MouseEvent('mouseup', {bubbles: true}));
        });

        expect(onRightSwipe).toBeCalled();
    });

    test('Swiping with mouse should preventDefault to avoid redirection when events happen on a link/button child', () => {
        const onRightSwipe = jest.fn();
        const preventDefault = jest.fn();

        act(() => {
            createRoot(container).render(
                <DummyDiv onRightSwipe={onRightSwipe}>
                    <a id={'link'}/>
                </DummyDiv>
            );
        });

        const link = container.querySelector('#link');

        act(() => {
            link.dispatchEvent(new MouseEvent('mousedown', {clientX: 100, bubbles: true}));
        });

        act(() => {
            link.dispatchEvent(new MouseEvent('mousemove', {clientX: 151, bubbles: true}));
        });

        act(() => {
            link.dispatchEvent(new MouseEvent('mouseup', {bubbles: true}));
        });

        act(() => {
            const clickEvent = new MouseEvent('click', {bubbles: true});
            clickEvent.preventDefault = preventDefault;
            link.dispatchEvent(clickEvent);
        });

        expect(onRightSwipe).toBeCalled();
        expect(preventDefault).toBeCalled();
    });

    test('Swiping with mouse in a child link/button element but not for long enough to trigger a swipe event should not call preventDefault', () => {
        const onRightSwipe = jest.fn();
        const preventDefault = jest.fn();

        act(() => {
            createRoot(container).render(
                <DummyDiv onRightSwipe={onRightSwipe}>
                    <a id={'link'}/>
                </DummyDiv>
            );
        });

        const link = container.querySelector('#link');

        act(() => {
            link.dispatchEvent(new MouseEvent('mousedown', {clientX: 100, bubbles: true}));
        });

        act(() => {
            link.dispatchEvent(new MouseEvent('mousemove', {clientX: 105, bubbles: true}));
        });

        act(() => {
            link.dispatchEvent(new MouseEvent('mouseup', {bubbles: true}));
        });

        act(() => {
            const clickEvent = new MouseEvent('click', {bubbles: true});
            clickEvent.preventDefault = preventDefault;
            link.dispatchEvent(clickEvent);
        });

        expect(onRightSwipe).not.toBeCalled();
        expect(preventDefault).not.toBeCalled();
    });

    test('Should call preventDefault to avoid dragging child elements, cause dragging breaks swipe', () => {
        const onRightSwipe = jest.fn();
        const preventDefault = jest.fn();

        act(() => {
            createRoot(container).render(
                <DummyDiv onRightSwipe={onRightSwipe}>
                    <a id={'link'}/>
                </DummyDiv>
            );
        });

        const link = container.querySelector('#link');

        act(() => {
            const dragEvent = new MouseEvent('dragstart', {bubbles: true});
            dragEvent.preventDefault = preventDefault;
            link.dispatchEvent(dragEvent);
        });

        expect(onRightSwipe).not.toBeCalled();
        expect(preventDefault).toBeCalled();
    });
});
