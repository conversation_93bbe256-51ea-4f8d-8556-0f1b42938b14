import React from 'react';
import {HeroCarousel} from '../heroCarousel';

describe('HeroCarousel', () => {
    const image = {
        _type: 'image',
        asset: {
            _ref:  'image-f6291580450291e8e61b85db49f1307f9e89149a-24x20-png',
            _type: 'reference'
        },
        height: 20,
        width:  24
    };

    const heroImages = [
        {
            title: 'image 1',
            image: {_key: '1', ...image},
            icon:  {_key: '2', ...image},
            alt:   'image 1'
        },
        {
            title: 'image 2',
            image: {_key: '3', ...image},
            icon:  {_key: '4', ...image},
            alt:   'image 2',
            link:  {
                _ref: 'page1'
            }
        },
        {
            title: 'image 3',
            image: {_key: '5', ...image},
            icon:  {_key: '6', ...image},
            alt:   'image 3'
        }
    ];

    test('renders without errors', () => {
        const component = render(<HeroCarousel
            id={'id'}
            heroImages={heroImages}
        />);

        expect(component).toMatchSnapshot();
    });
});
