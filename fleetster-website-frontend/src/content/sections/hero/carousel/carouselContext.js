import React, {createContext, useContext, useState} from 'react';
import PropTypes from 'prop-types';

export const CarouselContext = createContext({});

export function useCarouselContext() {
    const context = useContext(CarouselContext);
    if (!context) {
        console.error('useCarouselContext must be used within a CarouselProvider');
    }

    return context;
}

export function CarouselProvider({children, context}) {
    const [currentIndex, setCurrentIndex] = useState(context.currentIndex || 0);
    const [seen, setSeen] = useState([true]);
    const [intervalMs] = useState(context.intervalMs || 0);
    const [totalItems] = useState(context.totalItems || 0);

    const goToIndex = index => {
        let newSeen = [...seen];
        setCurrentIndex(index);
        newSeen[index] = true;
        setSeen(newSeen);
    };
    const goToPrev = () => goToIndex((currentIndex - 1 + totalItems) % totalItems);
    const goToNext = () => goToIndex((currentIndex + 1) % totalItems);

    const goToPrevNoLooping = () => {
        if (currentIndex > 0) {
            goToPrev();
        }
    };
    const goToNextNoLooping = () => {
        if (currentIndex < (totalItems - 1)) {
            goToNext();
        }
    };

    return (
        <CarouselContext.Provider value={{
            currentIndex,
            intervalMs,
            totalItems,
            seen,
            goToIndex,
            goToPrev,
            goToNext,
            goToPrevNoLooping,
            goToNextNoLooping
        }}>
            {children}
        </CarouselContext.Provider>
    );
}

CarouselProvider.propTypes = {
    children: PropTypes.oneOfType([
        PropTypes.object,
        PropTypes.array
    ]),
    context: PropTypes.object
};

