import React from 'react';
import PropTypes from 'prop-types';

import {Image} from 'rf-components';

import {useCarouselContext} from './carouselContext';
import {HERO_IMAGE_HEIGHT} from '../constants';

export function CarouselImage({heroImage, index, optimizeForLCP}) {
    const {seen} = useCarouselContext();

    if (!seen?.[index]) {
        return null;
    }

    const optmize = optimizeForLCP && index === 0;

    if (heroImage.link) {
        const href = heroImage.link.href;

        return (
            <a key={heroImage.image._key} href={href} className={'carousel-img'} tabIndex={-1}>
                <Image image={heroImage.image} imageAlt={heroImage.alt} priority={optmize} height={HERO_IMAGE_HEIGHT}/>
            </a>
        );
    } else {
        return (
            <Image key={heroImage.image._key} image={heroImage.image} imageAlt={heroImage.alt} priority={optmize} height={HERO_IMAGE_HEIGHT}/>
        );
    }
}

CarouselImage.propTypes = {
    heroImage: PropTypes.shape({
        image: PropTypes.shape({
            _key:  PropTypes.string,
            asset: PropTypes.object
        }),
        alt:  PropTypes.string,
        link: PropTypes.object
    }),
    index:          PropTypes.number,
    optimizeForLCP: PropTypes.bool
};
