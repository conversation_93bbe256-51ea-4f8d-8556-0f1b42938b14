import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {fontWeights, screenSizes, vSpacing} from 'rf-styles';
import {CallToAction, MainHeading} from 'rf-components';

const {className, styles} = css.resolve`
.hero-text {
    display: flex;
    font-family: "Open Sans", sans-serif;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    margin-bottom: ${vSpacing.m}px;
    padding: 0;
}
@media (min-width: ${screenSizes.s}px) { .hero-text {
    margin-bottom: ${vSpacing.l}px;
    width: 50%;
}}
@media (min-width: ${screenSizes.m}px) { .hero-text {
    margin-bottom: 0;
}}
  
.hero-text h1, .hero-text h2, .hero-text p {
    font-size: 26px;
    line-height: 30px;
    font-weight: ${fontWeights.semiBold};
}

@media (min-width: ${screenSizes.xxs}px) { .hero-text h1, .hero-text h2 {
    font-size: 30px;
    line-height: 36px;
}}

@media (min-width: ${screenSizes.m}px) { .hero-text h1, .hero-text h2 {
    font-size: 38px;
    line-height: 44px;
}}
  
.hero-text p {
    font-weight: ${fontWeights.semiBold};
    font-size: 14px;
    line-height: 18px;
}

@media (min-width: ${screenSizes.xxs}px) { .hero-text p {
    font-size: 16px;
    line-height: 20px;
}}

@media (min-width: ${screenSizes.m}px) { .hero-text p  {
    font-size: 20px;
    line-height: 24px;
    margin-bottom: ${vSpacing.m}px
}}`;

const lightThemeStyles = css.global`
.hero-text .light-theme {
    color: white;
}`;

export function HeroText(props) {
    const {heading, lightTheme, text, callToAction, index = 1} = props;
    const lightThemeClasses = lightTheme ? 'light-theme' : '';

    return (
        <div className={`${className} rf-cell rf-flex-6 hero-text`}>
            <MainHeading heading={heading} index={index} classNames={`h1 ${lightThemeClasses} ${className}`}/>
            {text && <p className={`${className} ${lightThemeClasses} h5`}> {text}</p>}
            {callToAction?.title && <CallToAction {...callToAction} lightTheme={lightTheme}/>}
            {styles}
            <style jsx global>{lightThemeStyles}</style>
        </div>
    );
}

HeroText.propTypes = {
    heading:      PropTypes.string,
    lightTheme:   PropTypes.bool,
    text:         PropTypes.string,
    callToAction: PropTypes.object,
    index:        PropTypes.number
};
