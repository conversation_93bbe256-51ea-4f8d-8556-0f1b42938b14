import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';
import _ from 'lodash';
import dynamic from 'next/dynamic';

import {vSpacing, screenSizes} from 'rf-styles';

import {HeroImage} from './heroImage';
import {HeroBackgroundImage} from './heroBackgroundImage';
import {HeroText} from './heroText';
import {ICON_SIZE, MAX_ICONS} from './constants';

const styles = css`
  .rf-flex-container {
    flex-wrap: nowrap;
    width: 100%;
    padding: 0;
  }

  .hero-section {
    position: relative;
    overflow: hidden;
    background-color: transparent;
    transform: translateZ(0);
    padding-bottom: 80px;
  }
`;

const HeroCarousel = dynamic(() => import('./carousel').then(module => module.HeroCarousel));

const rightColumnStyles = css`
  .right-hero-column {
    overflow: hidden;
    padding: 0;
  }

  @media (min-width: ${screenSizes.s}px) {
    .right-hero-column {
      width: 50%;
    }
  }
`;

function RightHeroColumn({isCarousel, heroImages, id, optimizeForLCP}) {
    const firstImage = heroImages?.[0];
    let content = (<HeroImage
        image={firstImage?.image}
        imageAlt={firstImage?.alt || ''}
        optimizeForLCP={optimizeForLCP}/>);

    if (isCarousel) {
        content = <HeroCarousel id={id} heroImages={heroImages} optimizeForLCP={optimizeForLCP}/>;
    }

    return (<>
        <div className={'rf-cell rf-flex-6 center-content right-hero-column'}>{content}</div>
        <style jsx>{rightColumnStyles}</style>
    </>);
}

RightHeroColumn.propTypes = {
    isCarousel:     PropTypes.bool,
    heroImages:     PropTypes.array,
    optimizeForLCP: PropTypes.bool,
    id:             PropTypes.string
};

export function Hero(props) {
    const {backgroundImageLandscape, heroImages, _key, heading} = props;
    const isCarousel = heroImages?.length > 1;
    const carouselClass = isCarousel ? 'hero-carousel' : '';
    const {index} = props;
    const optimizeForLCP = index < 2;
    const height = isCarousel ? `${MAX_ICONS * (ICON_SIZE + vSpacing.xs) + vSpacing.xs}px` : 'auto';

    return (
        <section className={`page-horizontal-padding section-vertical-padding hero-section center-content ${carouselClass}`} id={_.kebabCase(heading)}>
            <HeroBackgroundImage backgroundImageLandscape={backgroundImageLandscape} optimizeForLCP={optimizeForLCP}/>
            <div className={'rf-flex-container'}>
                <HeroText {...props}/>
                <RightHeroColumn id={_key} heroImages={heroImages} isCarousel={isCarousel} optimizeForLCP={optimizeForLCP}/>
            </div>
            <style jsx>{styles}</style>
            <style jsx>{`
              @media(min-width: ${screenSizes.s}px) { .hero-section {
                height: ${height};
              }}
            `}
            </style>
        </section>
    );
}

Hero.propTypes = {
    heading:                  PropTypes.string,
    lightTheme:               PropTypes.bool,
    _key:                     PropTypes.string,
    heroImages:               PropTypes.array,
    backgroundImageLandscape: PropTypes.object,
    tagline:                  PropTypes.array,
    callToAction:             PropTypes.object,
    index:                    PropTypes.number
};
