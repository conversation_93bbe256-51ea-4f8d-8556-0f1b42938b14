import React from 'react';
import PropTypes from 'prop-types';

import {Image} from 'rf-components';
import {HERO_IMAGE_HEIGHT} from './constants';

export function HeroImage({image, imageAlt, optimizeForLCP}) {
    return (
        <div className={'rf-cell rf-flex-6'}>
            <Image image={image} imageAlt={imageAlt} priority={optimizeForLCP} height={HERO_IMAGE_HEIGHT}/>
        </div>
    );
}

HeroImage.propTypes = {
    image:          PropTypes.object,
    imageAlt:       PropTypes.string,
    optimizeForLCP: PropTypes.bool
};
