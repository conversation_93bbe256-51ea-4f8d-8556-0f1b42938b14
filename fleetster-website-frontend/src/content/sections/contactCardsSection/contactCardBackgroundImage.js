import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {Image} from 'rf-components';

const styles = css`
  .contact-card-bg {
    position: absolute;
    height: 100%;
    width: 100%;
  }

  .contact-card-bg :global(img) {
    border-radius: 10px 10px 0 0;
  }
`;

export function ContactCardBackgroundImage({image}) {
    if (!image) {
        return null;
    }

    return (
        <div className={'contact-card-bg'} >
            <Image
                image={image}
                imageAlt={'Contact Background'}
                layout={'cover'}/>
            <style jsx>{styles}</style>
        </div>
    );
}

ContactCardBackgroundImage.propTypes = {
    image: PropTypes.object
};
