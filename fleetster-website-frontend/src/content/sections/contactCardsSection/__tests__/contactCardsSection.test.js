import React from 'react';
import {ContactCardsSection} from '../../index';

describe('ContactCardsSection', () => {
    const image = {
        _type: 'image',
        asset: {
            _ref:  'image-dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563-png',
            _type: 'reference'
        },
        height: 563,
        width:  1832
    };

    const sampleCard = {
        image,
        backgroundImage: image,
        role:            'Function',
        sanityDisplay:   'Display',
        name:            'Name',
        buttons:         [
            {
                _key:        'd54e27149cbb',
                _type:       'iconTextLink',
                description: 'CLickme',
                icon:        image,
                lightTheme:  true
            }
        ],
        itemList: [
            {
                _key:        '2d950a17264f',
                _type:       'iconText',
                description: 'ItemONe',
                icon:        image
            },
            {
                _key:        '3c6f0e3e4b68',
                _type:       'iconText',
                description: 'IOtem Two',
                icon:        image
            }
        ]
    };
    const anotherCard = {
        image,
        backgroundImage: image,
        role:            'Some Role',
        sanityDisplay:   'Another',
        name:            'Name,  ',
        buttons:         [
            {
                _key:        'd54e27149cbb',
                _type:       'iconTextLink',
                description: 'Button One',
                icon:        image,
                lightTheme:  true
            },
            {
                _key:        'd54e27149cbb',
                _type:       'iconTextLink',
                description: 'Button Two',
                icon:        image
            }
        ],
        itemList: [
            {
                _key:        '2d950a17264f',
                _type:       'iconText',
                description: 'No image'
            },
            {
                _key:  '3c6f0e3e4b68',
                _type: 'iconText',
                icon:  image
            }
        ]
    };

    test('renders without errors', async() => {
        let component;

        await act(() => {
            component = render(<ContactCardsSection/>);
        });

        expect(component).toMatchSnapshot();
    });

    test('renders with a single card', async() => {
        let component;
        const mockProps = {cards: [sampleCard]};

        await act(() => {
            component = render(<ContactCardsSection {...mockProps}/>);
        });

        expect(component).toMatchSnapshot();
    });

    test('renders with multiple cards', async() => {
        let component;
        const mockProps = {
            cards: [
                sampleCard,
                anotherCard
            ]
        };

        await act(() => {
            component = render(<ContactCardsSection {...mockProps}/>);
        });

        expect(component).toMatchSnapshot();
    });

    test('renders without background images', async() => {
        let component;
        const mockProps = {
            cards: [
                {...sampleCard, backgroundImage: null}
            ]
        };
        await act(() => {
            component = render(<ContactCardsSection {...mockProps}/>);
        });

        expect(component).toMatchSnapshot();
    });

    test('renders without name and role', async() => {
        let component;
        const mockProps = {
            cards: [
                {...sampleCard, name: null, role: null}
            ]
        };
        await act(() => {
            component = render(<ContactCardsSection {...mockProps}/>);
        });

        expect(component).toMatchSnapshot();
    });

    test('renders without buttons', async() => {
        let component;
        const mockProps = {
            cards: [
                {...sampleCard, buttons: []}
            ]
        };

        await act(() => {
            component = render(<ContactCardsSection {...mockProps}/>);
        });

        expect(component).toMatchSnapshot();
    });
});
