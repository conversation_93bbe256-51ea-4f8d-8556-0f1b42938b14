import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';
import * as _ from 'lodash';

import {vSpacing, hSpacing, screenSizes} from 'rf-styles';

import {ContactCard} from './contactCard';

const styles = css`
  .contact-cards-section {
    display: flex;
    align-items: baseline;
    flex-wrap: wrap;
    justify-content: center;
    column-gap: ${hSpacing.m}px;
    row-gap: ${vSpacing.m}px;
    padding: 50px 58px;
    max-width: 100%;
  }
  
  @media (min-width: ${screenSizes.s}px) {
    .contact-cards-section {
      padding-left: 77px;
      padding-right: 77px;
    }
  }

  @media (min-width: ${screenSizes.m}px) {
    .contact-cards-section {
      padding-left: 141px;
      padding-right: 141px;
    }
  }

  @media (min-width: ${screenSizes.l}px) {
    .contact-cards-section {
      padding-left: 162px;
      padding-right: 162px;
    }
  }

  @media (min-width: ${screenSizes.xl}px) {
    .contact-cards-section {
      padding-left: 215px;
      padding-right: 215px;
    }
  }
`;

function ContactCardsSection({cards}) {
    return (
        <section className={'contact-cards-section'}>
            {_.map(cards, card => <ContactCard card={card} key={card.name}/>)}
            <style jsx>{styles}</style>
        </section>
    );
}

ContactCardsSection.propTypes = {
    _key:  PropTypes.string,
    cards: PropTypes.array
};

export {ContactCardsSection};
