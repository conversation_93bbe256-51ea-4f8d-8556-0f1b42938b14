import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {screenSizes, colors, hSpacing, vSpacing} from 'rf-styles';
import {Image} from 'rf-components';
import {CallToActionLink} from 'rf-components/callToAction/callToActionLink';

const iconSize = 26;
const iconSizeSm = 22;

const styles = css`
  .contact-card-buttons {
    margin-top: ${vSpacing.s}px;
    padding: 0 ${hSpacing.sm}px;
  }
  
  .contact-card-button {
    display: flex;
    background-color: ${colors.blue};
    border-radius: 4px;
    height: 40px;
    position: relative;
    border: none;
    color: ${colors.white};
    justify-content: center;
  }

  .contact-card-button:nth-child(2) {
    margin-top: ${hSpacing.xs}px;
  }

  .item-icon {
    width: ${iconSizeSm}px;
    pointer-events: none;
    padding-left: 10px;
    padding-right: 5px;
  }

  @media (min-width: ${screenSizes.xs}px) {
    .contact-card-buttons {
      display: flex;
      justify-content: center;
    }
    
    .contact-card-button {
      display: inline-flex;
    }

    .contact-card-button:nth-child(2) {
      margin-top: 0;
      margin-left: ${hSpacing.xxs}px;
    }
    
    .item-icon {
      width: ${iconSize}px;
    }
  }

  .light-theme {
    box-sizing: border-box;
    background-color: ${colors.white};
    border: 1px solid ${colors.blue};
    color: ${colors.blue};
  }

  .contact-card-button :global(a) {
    padding-left: 5px;
    width: auto;
  }
`;

function ContactCardButton({iconTextLink}) {
    const {icon, iconAlt, description, link, lightTheme, route} = iconTextLink;

    return (<div className={`contact-card-button ${lightTheme ? 'light-theme' : ''}`}>
        {icon && <div className={'item-icon'}><Image image={icon} imageAlt={`${iconAlt} icon`}/></div>}
        <CallToActionLink title={description} lightTheme={lightTheme} link={link} route={route} enableHover={false}/>
        <style jsx>{styles}</style>
    </div>);
}

ContactCardButton.propTypes = {
    _key:         PropTypes.string,
    iconTextLink: PropTypes.object
};

function ContactCardButtons({buttons}) {
    if (!buttons || buttons?.length === 0) {
        return null;
    }
    const [left, right] = buttons;

    return (
        <div className={'contact-card-buttons'}>
            <ContactCardButton iconTextLink={left}/>
            {right && <ContactCardButton iconTextLink={right}/>}
            <style jsx>{styles}</style>
        </div>
    );
}

ContactCardButtons.propTypes = {
    _key:    PropTypes.string,
    buttons: PropTypes.array
};

export {ContactCardButtons};
