import React from 'react';
import moment from 'rf-moment-locale';
import Head from 'next/head';
import css from 'styled-jsx/css';
import PropTypes from 'prop-types';

import {t} from 'rf-i18n';
import {Image, MainHeading} from 'rf-components';
import {colors, vSpacing} from 'rf-styles';
import {language} from 'rf-data/siteConfig.json';

import {articleGlobalStyles} from './articleGlobalStyles';
import {articleToStructuredData} from './articleHeadingStructuredData';

const styles = css`
.image-holder {
    height: 350px;
    position: relative;
    overflow: hidden;
    margin-bottom: ${vSpacing.m}px;
}
  
.image-inner-holder {
    position: absolute;
    width: 100%;
    min-height: 350px;
    box-sizing: border-box;
}

.heading :global(.title) {
    margin-bottom: ${vSpacing.xs}px;
}
  
.published {
    color: ${colors.blue};
    font-size: 12px;
    line-height: 12px;
    letter-spacing: 1.2px;
    margin-bottom: ${vSpacing.m}px;
}
  
.description {
    margin: 0;
    padding: ${vSpacing.s}px 0;
    color: ${colors.darkGray};
    border-top: 1px solid ${colors.gray};
    border-bottom: 1px solid ${colors.gray};
}
`;

function getPublishedHeadingSubtitle(publishedOn, author) {
    const publishedDate = moment(publishedOn).locale(language).format('D. MMM YYYY');
    if (!publishedOn && !author) {
        return <p/>;
    }

    let text = `${publishedDate} | ${t('article.by')} ${author}`;
    if (!publishedOn) {
        text = `${t('article.by')} ${author}`;
    }
    if (!author) {
        text = publishedDate;
    }

    return <p className={'published'}>{text}</p>;
}

export function ArticleHeadingSection(props) {
    const {image, imageAlt, heading, publishedOn, author, description, index = 1} = props;

    const {width, height} = image;
    const ratio = height / width * 100;
    const imageHolderStyle = {
        minWidth:      width,
        minHeight:     height,
        paddingBottom: `${ratio}%`
    };

    const structureDataHtml = {
        __html: JSON.stringify(articleToStructuredData(props))
    };
    const Published = getPublishedHeadingSubtitle(publishedOn, author);

    return (
        <>
            <div className={'image-holder'}>
                <div className={'image-inner-holder'} style={imageHolderStyle}>
                    <Image image={image} layout={'fill'} imageAlt={imageAlt}/>
                </div>
            </div>

            <div className={'page-horizontal-padding heading'}>
                <MainHeading heading={heading} index={index} classNames={'h2 title'}/>
                {Published}

                <p className={'description'}> {description} </p>
            </div>

            <style jsx>{styles}</style>
            <style jsx>{articleGlobalStyles}</style>
            <Head>
                <script dangerouslySetInnerHTML={structureDataHtml} type={'application/ld+json'}/>
            </Head>
        </>
    );
}

ArticleHeadingSection.propTypes = {
    image:       PropTypes.object,
    imageAlt:    PropTypes.string,
    heading:     PropTypes.string,
    publishedOn: PropTypes.string,
    author:      PropTypes.string,
    description: PropTypes.string,
    index:       PropTypes.number
};
