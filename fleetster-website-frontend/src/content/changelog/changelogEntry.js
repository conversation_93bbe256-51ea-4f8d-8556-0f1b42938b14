import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';
import _ from 'lodash';

import moment from 'rf-moment-locale';

import {CallToAction, SimpleBlockContent} from 'rf-components';
import {colors, fontWeights, vSpacing, screenSizes} from 'rf-styles';

import {ChangelogTags} from './changelogTags';
import {ChangelogMedia} from './changelogMedia';
import Head from 'next/head';
import {generateVideoStructuredDataHtml} from 'rf-helpers';

const styles = css`
  .changelog-card {
    border-top: 1px solid ${colors.gray};
    display: flex;
    flex-wrap: wrap;
    padding: ${vSpacing.s}px 0;
  }
  .changelog-card:first-of-type {
    border-top: 1px solid ${colors.blue};
  }
  .changelog-card:last-child {
    border-bottom: 1px solid ${colors.blue};
  }
  @media (min-width: ${screenSizes.xxs}px) { .changelog-card {
    padding: ${vSpacing.m}px 0;
  }}
  @media (min-width: ${screenSizes.s}px) { .changelog-card {
    padding: ${vSpacing.l}px 0;
  }}
  @media (min-width: ${screenSizes.m}px) { .changelog-card {
    flex-wrap: nowrap;
  }}
  
  .changelog-card-title {
    color: ${colors.blue};
    font-weight: ${fontWeights.semiBold};
    display: flex;
    margin-bottom: ${vSpacing.s}px;
  }
  
  .changelog-release {
    font-weight: ${fontWeights.semiBold};
    color: ${colors.darkGray};
    margin-bottom: ${vSpacing.s}px;;
  }
  
  .changelog-description {
    margin-top: ${vSpacing.s}px;
  }
  @media (min-width: ${screenSizes.xxs}px) { .changelog-description {
    margin-bottom: 0;
  }}

  .call-to-action-wrapper {
    margin-top: ${vSpacing.s}px;
  }
  @media (min-width: ${screenSizes.m}px) { .call-to-action-wrapper {
    display: inline-block;
  }}
`;

export function ChangelogEntry({changelog}) {
    const {
        content, image, imageAlt, videoUrl, youtubeVideoData,
        title, releaseDate, releaseVersion = '-', tags, callToAction,
        language
    } = changelog;
    if (!content) {
        return null;
    }

    const formattedDate = moment(releaseDate, 'YYYY-MM-DD').locale(language).format('L');
    const structuredDataHtml = generateVideoStructuredDataHtml(youtubeVideoData);

    return (
        <>
            <section className={'changelog-card'} id={_.kebabCase(title)}>
                <div className={'rf-cell'}>
                    <h2 className={'changelog-card-title h4'}>{title}</h2>
                    <h3 className={'changelog-release link'}>{`${formattedDate}, ${releaseVersion}`}</h3>
                    <ChangelogTags tags={tags}/>
                    <ChangelogMedia image={image} imageAlt={imageAlt} videoUrl={videoUrl}/>
                    <div className={'changelog-description'}>
                        <SimpleBlockContent blocks={content}/>
                    </div>
                    {_.get(callToAction, 'title') && <div className={'call-to-action-wrapper'}>
                        <CallToAction {...callToAction}/>
                    </div>
                    }
                </div>
                <style jsx>{styles}</style>
            </section>
            {structuredDataHtml &&
            <Head>
                <script dangerouslySetInnerHTML={structuredDataHtml} type={'application/ld+json'}/>
            </Head>}
        </>
    );
}

ChangelogEntry.propTypes = {
    changelog: PropTypes.object,
    index:     PropTypes.number
};
