import React from 'react';

describe('ChangelogList', () => {
    const changelogs = [
        {
            callToAction: {
                _type: 'callToAction',
                link:  '/v4/request/price-request',
                title: 'Read more'
            },
            content: [
                {
                    _type:    'embedHTML',
                    style:    'normal',
                    children: [
                        {text: 'lorem ipsum 1'}
                    ]
                }
            ],
            releaseDate:    '2020-12-24',
            releaseVersion: '3.90',
            title:          'Testing Changelog Q 4',
            quarter:        4,
            year:           2020,
            pageSlug:       'product-updates/q4-2020',
            image:          {asset: {_ref: 'image-Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000-jpg'}, height: 3000, width: 2000},
            tags:           [{
                value: 'fleetManagement',
                label: 'Fleet Management'
            }],
            language: 'de'
        },
        {
            releaseDate:    '2020-09-08',
            releaseVersion: '3.70',
            title:          'Testing changelog Q3',
            quarter:        3,
            year:           2020,
            pageSlug:       'product-updates/q3-2020'
        },
        {
            content: [
                {
                    _type:    'embedHTML',
                    style:    'normal',
                    children: [
                        {text: 'lorem ipsum 3'}
                    ]
                }
            ],
            tags: [{
                label: 'Fleetmanagement',
                value: 'fleetManagement'
            }],
            releaseDate:    '2020-05-08',
            releaseVersion: '3.70',
            title:          'Testing changelog Q2',
            quarter:        2,
            year:           2020,
            pageSlug:       'product-updates/q2-2020',
            language:       'en'
        }
    ];

    beforeEach(() => {
        jest.doMock('rf-components/simpleBlockContent', () => ({
            __esModule: true,
            default:    ({blocks, ...rest}) => <div {...rest}>{blocks}</div> // eslint-disable-line react/display-name, react/prop-types
        }));
    });

    test('renders changelog content', () => {
        const {ChangelogList} = require('../index');

        const component = render(<ChangelogList changelogs={changelogs}/>);

        expect(component.toJSON()).toMatchSnapshot();
    });

    test('renders only the title if no changelogs are present', () => {
        const {ChangelogList} = require('../index');

        const component = render(<ChangelogList/>);

        expect(component.toJSON()).toMatchSnapshot();
    });
});
