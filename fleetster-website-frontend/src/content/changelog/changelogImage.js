import {Image} from 'rf-components';
import React from 'react';
import css from 'styled-jsx/css';
import PropTypes from 'prop-types';
import {screenSizes, vSpacing} from 'rf-styles';

const styles = css`
  .changelog-card-image {
    box-sizing: border-box;
    margin-top: ${vSpacing.s}px;
    max-height: 450px;
    width: 100%;
    display: flex;
    justify-content: flex-start;
    overflow: hidden;
  }
  
  @media (max-width: ${screenSizes.s}px) { 
    .changelog-card-image {
      justify-content: center;
    }
  }
`;

export function ChangelogImage({image, imageAlt}) {
    return (
        <div className={'changelog-card-image'}>
            <div>{image && <Image image={image} imageAlt={imageAlt}/>}</div>
            <style jsx>{styles}</style>
        </div>
    );
}
ChangelogImage.propTypes = {
    image:    PropTypes.object,
    imageAlt: PropTypes.string
};

