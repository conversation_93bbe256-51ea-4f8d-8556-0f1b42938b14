import React from 'react';
import PropTypes from 'prop-types';
import {NextSeo} from 'next-seo';

import {PaginationSection} from 'rf-components';

import {getLanguageAlternates} from './getLanguageAlternates';
import {ChangelogList} from '../content/changelog';
import {Layout} from '../layout';

function paginationTextGenerator(item) {
    return `Q${item.quarter} ${item.year}`;
}

export function ChangelogPage({route, slug}) {
    return (
        <Layout route={route}>
            <NextSeo
                title={route.title}
                canonical={route.canonical}
                description={route.description}
                languageAlternates={getLanguageAlternates(route)}
            />
            <ChangelogList changelogs={route.content}/>
            <PaginationSection pageSlug={slug} pagination={route.pagination}
                textFormatter={paginationTextGenerator}/>
        </Layout>
    );
}

ChangelogPage.propTypes = {
    slug:  PropTypes.string,
    route: PropTypes.object
};
