import React from 'react';
import PropTypes from 'prop-types';

import {getImageUrl} from 'rf-data';

export function SVGIcon({image, className, styles, alt}) {
    const imageSource = getImageUrl(image);

    return (
        <div style={{
            mask:               `url(${imageSource}) no-repeat center / contain`,
            WebkitMaskImage:    `url(${imageSource})`,
            WebkitMaskRepeat:   'no-repeat',
            WebkitMaskPosition: 'center',
            WebkitMaskSize:     'contain'
        }}
        className={className}
        aria-label={alt}
        role={'img'}
        >
            <style jsx>{styles}</style>
        </div>
    );
}

SVGIcon.propTypes = {
    className: PropTypes.string,
    styles:    PropTypes.oneOfType([
        PropTypes.object,
        PropTypes.string
    ]),
    image: PropTypes.object,
    alt:   PropTypes.string
};
