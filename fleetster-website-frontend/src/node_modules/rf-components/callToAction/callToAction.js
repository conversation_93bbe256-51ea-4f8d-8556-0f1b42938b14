import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {screenSizes, colors} from 'rf-styles';
import {CTA_HEIGHT_REGULAR, CTA_HEIGHT_SMALL} from 'rf-components/callToAction/constants';

import {CallToActionLink} from './callToActionLink';

const styles = css`
  .call-to-action {
    display: inline-flex;
    background-color: ${colors.blue};
    border-radius: 4px;
    width: 100%;
    min-height: ${CTA_HEIGHT_REGULAR}px;
    border: none;
    color: ${colors.white};
    cursor: pointer;
  }

  .small {
    min-height: ${CTA_HEIGHT_SMALL}px;
  }

@media (min-width: ${screenSizes.s}px) { .call-to-action {
    min-height: ${CTA_HEIGHT_REGULAR}px;
}}

@media (min-width: ${screenSizes.xs}px) { .call-to-action {
    width: auto;
}}

  .call-to-action:hover {
    background-color: ${colors.darkBlue};
  }

  .light-theme {
    background-color: ${colors.white};
  }

  .light-theme:hover {
    background-color: ${colors.lightBlue};
  }
`;

export function CallToAction(props) {
    return (
        <div className={`call-to-action${props.lightTheme ? ' light-theme' : ''}${props.small ? ' small' : ''}`}>
            <CallToActionLink {...props}/>
            <style jsx>{styles}</style>
        </div>

    );
}

CallToAction.propTypes = {
    link:         PropTypes.string,
    lightTheme:   PropTypes.bool,
    small:        PropTypes.bool,
    title:        PropTypes.string,
    enableHover:  PropTypes.bool,
    prefixedText: PropTypes.string,
    route:        PropTypes.shape({
        _ref: PropTypes.string
    })
};
