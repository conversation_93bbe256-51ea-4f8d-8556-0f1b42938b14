import React from 'react';
import {CallToAction} from '../callToAction';

describe('CallToAction', () => {
    it('renders without errors', () => {
        const component = render(<CallToAction title={'title'}/>);

        expect(component).toMatchSnapshot();
    });

    it('renders a link that opens in the current tab', () => {
        const component = render(<CallToAction title={'Link\'s awakening'} link={'http://localhost:3000/somewhere'}/>);

        expect(component).toMatchSnapshot();
    });

    it('renders a link that opens in new tab if link is a file', () => {
        const component = render(<CallToAction title={'Link\'s awakening'} link={'http://localhost:3000/file.pdf'}/>);

        expect(component).toMatchSnapshot();
    });

    it('renders an internal link if a route is passed in the params', () => {
        const component = render(<CallToAction title={'Route\'s awakening'} route={{_ref: 'page1', href: 'http://localhost:3000/v4/corsoroute'}}/>);

        expect(component).toMatchSnapshot();
    });

    it('renders with light theme', () => {
        const component = render(<CallToAction title={'Link\'s awakening'} link={'http://localhost:3000/somewhere'} lightTheme={true}/>);

        expect(component).toMatchSnapshot();
    });

    it('renders with light theme and hover disabled', () => {
        const component = render(<CallToAction title={'Link\'s awakening'} link={'http://localhost:3000'} lightTheme={true} enableHover={false}/>);

        expect(component).toMatchSnapshot();
    });
});
