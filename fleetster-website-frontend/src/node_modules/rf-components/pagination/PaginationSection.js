import React from 'react';
import PropTypes from 'prop-types';
import * as _ from 'lodash';

import {host} from 'rf-data/siteConfig.json';
import {Pagination} from './Pagination';

function getLargeButtons(visibleButtons, paginationIndex, pagination) {
    const sliceSize = visibleButtons.length;
    const middleIndex = Math.ceil(sliceSize / 2);

    if (paginationIndex < middleIndex) {
        return visibleButtons;
    }

    if (paginationIndex > (pagination.length - middleIndex)) {
        visibleButtons = pagination.slice(pagination.length - sliceSize);
    } else {
        visibleButtons = pagination.slice(paginationIndex - Math.floor(sliceSize / 2), paginationIndex + middleIndex);
    }

    return visibleButtons;
}

function getSmallButtons(visibleButtons, paginationIndex, pagination) {
    if (paginationIndex < 2) {
        return visibleButtons;
    }

    const iteration = Math.floor(paginationIndex / 2);

    return pagination.slice(iteration * 2, iteration * 2 + 2);
}

function getPreviousAndNextPage(paginationIndex, pagination) {
    let nextPageSlug, previousPageSlug;

    if (pagination[paginationIndex + 1]) {
        nextPageSlug = pagination[paginationIndex + 1].pageSlug;
    }

    if (pagination[paginationIndex - 1]) {
        previousPageSlug = pagination[paginationIndex - 1].pageSlug;
    }

    return {nextPageSlug, previousPageSlug};
}

function getPaginatedRoutes(pageSlug, paginationIndex, pagination) {
    let largeVisibleButtons = pagination.slice(0, 5);
    let smallVisibleButtons = pagination.slice(0, 2);

    if (paginationIndex > -1) {
        largeVisibleButtons = getLargeButtons(largeVisibleButtons, paginationIndex, pagination);
        smallVisibleButtons = getSmallButtons(smallVisibleButtons, paginationIndex, pagination);

        const {previousPageSlug, nextPageSlug} = getPreviousAndNextPage(paginationIndex, pagination);

        return {
            nextPage:     `${host.url}/${nextPageSlug}`,
            previousPage: `${host.url}/${previousPageSlug}`,
            largeVisibleButtons,
            smallVisibleButtons
        };
    }

    return {
        nextPage:     `${host.url}/${pagination[0].pageSlug}`,
        previousPage: `${host.url}/${pagination[pagination.length - 1].pageSlug}`,
        largeVisibleButtons,
        smallVisibleButtons
    };
}

function getArrowVisibility(paginationIndex, pagination) {
    let showLeftArrow = true;
    let showRightArrow = true;

    if (paginationIndex < 1) {
        showLeftArrow = false;
    }

    if (paginationIndex > pagination.length - 2) {
        showRightArrow = false;
    }

    return {showLeftArrow, showRightArrow};
}

export function PaginationSection({pageSlug, pagination, textFormatter}) {
    if (_.isEmpty(pagination) || !pageSlug) {
        return null;
    }

    const paginationIndex = _.indexOf(_.map(pagination, 'pageSlug'), pageSlug);

    const {showLeftArrow, showRightArrow} = getArrowVisibility(paginationIndex, pagination);

    const {
        nextPage,
        previousPage,
        largeVisibleButtons,
        smallVisibleButtons
    } = getPaginatedRoutes(pageSlug, paginationIndex, pagination);

    return (
        <Pagination
            pageSlug={pageSlug}
            showLeftArrow={showLeftArrow}
            showRightArrow={showRightArrow}
            nextPage={nextPage}
            previousPage={previousPage}
            largeVisibleButtons={largeVisibleButtons}
            smallVisibleButtons={smallVisibleButtons}
            textFormatter={textFormatter}
        />
    );
}

PaginationSection.propTypes = {
    pageSlug:      PropTypes.string,
    pagination:    PropTypes.array,
    textFormatter: PropTypes.func
};
