import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {colors} from 'rf-styles';
import {removeEndSlash} from 'rf-utils';

const styles = css`
  .pagination-link {
    text-decoration: none;
    background: ${colors.white};
    color: ${colors.blue};
    border-radius: 2px;
    margin: 0 5px 0 5px;
    width: 100px;
    height: 40px;
  }

  .pagination-link:hover {
    background: ${colors.gray};
    color: ${colors.white}
  }

  .selected {
    background: ${colors.blue};
    color: ${colors.white}
  }

  .selected:hover {
    background: ${colors.darkBlue};
  }
`;

export function PaginationButton({text, selected, url}) {
    return (
        <>
            <a className={`center-content pagination-link ${selected ? 'selected' : ''}`} href={removeEndSlash(url)}>{text}</a>
            <style jsx>{styles}</style>
        </>
    );
}

PaginationButton.propTypes = {
    url:      PropTypes.string,
    text:     PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    selected: PropTypes.bool
};
