import React from 'react';
import PropTypes from 'prop-types';
import Head from 'next/head';

import {getImageSource, getImageSrcSet} from './helpers';
import _ from 'lodash';

function getImageStyle({layout, width, height}) {
    if (layout === 'intrinsic') {
        return {
            width:     '100%',
            height:    'auto',
            maxWidth:  width,
            maxHeight: height
        };
    }

    if (layout === 'fill') {
        return {
            width:  '100%',
            height: '100%'
        };
    }

    if (layout === 'cover') {
        return {
            height:    '100%',
            width:     '100%',
            objectFit: 'cover'
        };
    }

    return null;
}

export function getImageDimensions(image, maxWidth, maxHeight) {
    if (maxWidth && maxHeight) {
        return {width: maxWidth, height: maxHeight};
    }

    let {width, height} = image;

    if (maxWidth) {
        height = _.round(height * (maxWidth / width));
        width = maxWidth;
    }

    if (maxHeight) {
        width = _.round(width * (maxHeight / height));
        height = maxHeight;
    }

    return {width, height};
}

export function Image({image, className = 'center-content', layout = 'intrinsic', priority, imageAlt, ...props}) {
    if (!image) { return null; }

    let {width, height} = getImageDimensions(image, props.width, props.height);

    const style = getImageStyle({layout, width, height});
    const src = getImageSource({image, width, height});
    const alt = imageAlt || new URL(src).pathname;
    const fillOrFixedSize = layout === 'fill' ? {} : {width, height};
    const {srcSet, sizes} = getImageSrcSet({image, width, height});

    return (
        <>
            {
                priority
                    ? <Head>
                        <link rel={'preload'} as={'image'} imageSizes={sizes} imageSrcSet={srcSet}/>
                    </Head> : null
            }

            <div className={className} style={{height: '100%', width: '100%'}}>
                <img
                    src={src}
                    alt={alt}
                    style={style}
                    decoding={priority ? 'sync' : 'async'}
                    loading={priority ? 'eager' : 'lazy'}
                    {...fillOrFixedSize}
                    srcSet={srcSet}
                    sizes={sizes}/>
            </div>
        </>
    );
}

Image.propTypes = {
    image:     PropTypes.object,
    imageAlt:  PropTypes.string,
    className: PropTypes.string,
    layout:    PropTypes.string,
    width:     PropTypes.number,
    height:    PropTypes.number,
    loading:   PropTypes.string,
    priority:  PropTypes.bool
};
