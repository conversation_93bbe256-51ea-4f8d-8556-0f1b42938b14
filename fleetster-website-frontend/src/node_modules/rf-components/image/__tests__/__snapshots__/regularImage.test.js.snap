// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Image should include preload link in head when priority is true 1`] = `
[
  <link
    as="image"
    imageSizes="100px"
    imageSrcSet="http://example.com/image-srcSet.jpg 100w"
    rel="preload"
  />,
  <div
    className="center-content"
    style={
      {
        "height": "100%",
        "width": "100%",
      }
    }
  >
    <img
      alt="/image-source.jpg"
      decoding="sync"
      height={500}
      loading="eager"
      sizes="100px"
      src="http://example.com/image-source.jpg"
      srcSet="http://example.com/image-srcSet.jpg 100w"
      style={
        {
          "height": "auto",
          "maxHeight": 500,
          "maxWidth": 500,
          "width": "100%",
        }
      }
      width={500}
    />
  </div>,
]
`;

exports[`Image should not include preload link in head when priority is false 1`] = `
<div
  className="center-content"
  style={
    {
      "height": "100%",
      "width": "100%",
    }
  }
>
  <img
    alt="/image-source.jpg"
    decoding="async"
    height={500}
    loading="lazy"
    sizes="100px"
    src="http://example.com/image-source.jpg"
    srcSet="http://example.com/image-srcSet.jpg 100w"
    style={
      {
        "height": "auto",
        "maxHeight": 500,
        "maxWidth": 500,
        "width": "100%",
      }
    }
    width={500}
  />
</div>
`;

exports[`Image should render Image with fill layout correctly 1`] = `
<div
  className="center-content"
  style={
    {
      "height": "100%",
      "width": "100%",
    }
  }
>
  <img
    alt="/image-source.jpg"
    decoding="async"
    loading="lazy"
    sizes="100px"
    src="http://example.com/image-source.jpg"
    srcSet="http://example.com/image-srcSet.jpg 100w"
    style={
      {
        "height": "100%",
        "width": "100%",
      }
    }
  />
</div>
`;

exports[`Image should render Image with intrinsic layout correctly 1`] = `
<div
  className="center-content"
  style={
    {
      "height": "100%",
      "width": "100%",
    }
  }
>
  <img
    alt="/image-source.jpg"
    decoding="async"
    height={500}
    loading="lazy"
    sizes="100px"
    src="http://example.com/image-source.jpg"
    srcSet="http://example.com/image-srcSet.jpg 100w"
    style={
      {
        "height": "auto",
        "maxHeight": 500,
        "maxWidth": 500,
        "width": "100%",
      }
    }
    width={500}
  />
</div>
`;
