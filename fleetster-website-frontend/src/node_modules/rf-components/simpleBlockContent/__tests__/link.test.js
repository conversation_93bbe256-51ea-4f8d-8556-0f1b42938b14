import React from 'react';

import {Link} from '../link';

describe('SimpleBlockContent - Link', () => {
    test('Should render link without pointing to new tab', () => {
        const props = {
            mark: {
                href:         'http://www.google.com',
                openInNewTab: false
            },
            children: ['huehuebr']
        };

        const link = render(<Link {...props}/>);

        expect(link.toJSON()).toMatchSnapshot();
    });

    test('Should render link pointing to new tab', () => {
        const props = {
            mark: {
                href:         'http://www.google.com',
                openInNewTab: true
            },
            children: ['huehuebr']
        };

        const link = render(<Link {...props}/>);

        expect(link.toJSON()).toMatchSnapshot();
    });

    test('Should render internal link pointing to correct route', () => {
        const props = {
            mark: {
                _type: 'internalLink',
                _ref:  'page1',
                href:  'http://localhost:3000/v4/corsoroute'
            },
            children: ['huehuebr']
        };

        const link = render(<Link {...props}/>);

        expect(link.toJSON()).toMatchSnapshot();
    });

    test('Should make external link open in current tab if it is same host even if openInNewTab is true', () => {
        const props = {
            mark: {
                href:         'http://localhost:3000',
                openInNewTab: true
            },
            children: ['huehuebr']
        };

        const link = render(<Link {...props}/>);

        expect(link.toJSON()).toMatchSnapshot();
    });
});
