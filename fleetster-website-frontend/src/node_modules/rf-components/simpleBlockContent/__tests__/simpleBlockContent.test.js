import React from 'react';

describe('SimpleBlockContent', () => {
    beforeAll(() => {
        jest.doMock('../embedHTML', () => ({EmbedHTML: () => <p>huehuebr</p>}));
        jest.doMock('../figure', () => ({Figure: () => <p>huehuebr</p>}));
    });

    test('Should render nothing if no blocks', () => {
        const {SimpleBlockContent} = require('../simpleBlockContent');

        const consoleError = jest.spyOn(console, 'error').mockImplementation(() => 'error message');

        const blockContent = render(<SimpleBlockContent blocks={null}/>);

        expect(blockContent.toJSON()).toBeFalsy();
        expect(consoleError).toBeCalled();

        consoleError.mockRestore();
    });

    test('Should render blocks correctly', () => {
        const {SimpleBlockContent} = require('../simpleBlockContent');

        const blockContent = render(<SimpleBlockContent blocks={[{_type: 'embedHTML', _key: 1}, {_type: 'figure', _key: 2}]}/>);

        expect(blockContent.root.findAllByType('p')[0].children[0]).toBe('huehuebr');
        expect(blockContent.root.findAllByType('p')[1].children[0]).toBe('huehuebr');
    });
});
