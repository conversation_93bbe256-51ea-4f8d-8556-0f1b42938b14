import React from 'react';

describe('Figure', () => {
    beforeAll(() => {
        jest.mock('rf-data', () => ({
            ...jest.requireActual('rf-data'),
            imageBuilder: {
                ref: '',
                get image() {
                    return asset => {
                        this.ref = asset._ref;

                        return this;
                    };
                },
                get auto() {
                    return () => this;
                },
                get width() {
                    return () => this;
                },
                get url() {
                    return () => this.ref;
                }
            }
        }));
    });

    test('Should render nothing if no assets', () => {
        const {Figure} = require('../figure');

        const blockContent = render(<Figure node={{}}/>);

        expect(blockContent.toJSON()).toBeFalsy();
    });

    test('Should render image correctly', () => {
        const {Figure} = require('../figure');

        const image = {
            alt:     'alt test',
            caption: 'caption test',
            asset:   {_ref: 'image-Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000-jpg'}
        };

        const figure = render(<Figure node={image}/>);

        expect(figure.root.findByType('img').props.src).toBe(image.asset._ref);
        expect(figure.root.findByType('img').props.alt).toBe(image.alt);
        expect(figure.root.findByType('p').children[0]).toBe('caption test');
    });
});
