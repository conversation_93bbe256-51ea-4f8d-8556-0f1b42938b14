import React from 'react';
import PropTypes from 'prop-types';
import BlockContent from '@sanity/block-content-to-react';

import {sanity} from 'rf-data/siteConfig.json';

import {serializers} from './serializers';

const {projectId, dataset} = sanity.client;

export function SimpleBlockContent({blocks}) {
    if (!blocks) {
        console.error('Missing blocks');

        return null;
    }

    return <BlockContent blocks={blocks} serializers={serializers} projectId={projectId} dataset={dataset} />;
}

SimpleBlockContent.propTypes = {
    blocks: PropTypes.arrayOf(PropTypes.object)
};
