import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {imageBuilder} from 'rf-data';

const styles = css`
.label {
    margin-top: 1em;
}

.label .title {
    margin-top: 0.2em; 
}

.image {
    display: block;
    width: 100%;
}

.content {
    position: relative;
    margin: 0 1.5rem;
}

.caption {
    width: 100%;
    margin: 0 auto;
}

.captionBox {
    border: 1px solid;
    padding: 1.5rem;
}`;

export function Figure({node}) {
    const {alt, caption, asset} = node;
    if (!asset) {
        return null;
    }

    return (
        <figure className={'content'}>
            <img
                src={imageBuilder
                    .image(asset)
                    .auto('format')
                    .width(2000)
                    .url()}
                className={'image'}
                alt={alt}
            />
            {caption &&
              <figcaption>
                  <div className={'caption'}>
                      <div className={'captionBox'}>
                          <p>{caption}</p>
                      </div>
                  </div>
              </figcaption>}
            <style jsx>{styles}</style>
        </figure>
    );
}

Figure.propTypes = {
    node: PropTypes.shape({
        alt:     PropTypes.string,
        caption: PropTypes.string,
        asset:   PropTypes.shape({
            _ref: PropTypes.string
        })
    })
};
