import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {colors, screenSizes, vSpacing} from 'rf-styles';

import {MainHeading} from './mainHeading';

const styles = css`
  .title-container {
    padding-top: ${vSpacing.m}px;
    padding-bottom: ${vSpacing.s}px;
  }

  @media (min-width: ${screenSizes.xxs}px) {
    .title-container {
      padding-top: ${vSpacing.l}px;
    }
  }

  @media (min-width: ${screenSizes.m}px) {
    .title-container {
      padding-bottom: ${vSpacing.m}px;
    }
  }

  @media (min-width: ${screenSizes.l}px) {
    .title-container {
      padding-top: ${vSpacing.xl}px;
    }
  }
`;

export function RFTitle({title, hexColor, index}) {
    const style = {color: hexColor ? `#${hexColor}` : colors.blue};

    return (
        <div className={'center-content title-container'}>
            <MainHeading style={style} heading={title} index={index}/>
            <style jsx>{styles}</style>
            <style jsx global>{`
              .title-container:global(.h1) {
                margin-top: 0;
                margin-bottom: 0;
                padding: 0;
              }`}
            </style>
        </div>
    );
}

RFTitle.propTypes = {
    index:    PropTypes.number,
    title:    PropTypes.string,
    hexColor: PropTypes.string
};

