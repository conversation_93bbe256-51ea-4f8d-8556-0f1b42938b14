describe('mergeClassNames', () => {
    const {mergeClassNames} = require('../index');

    it('handles default classes as string correctly', () => {
        const defaultClasses = 'one two';
        const customClasses = ['one', 'two', 'three'];

        const result = mergeClassNames(defaultClasses, customClasses);

        expect(result).toBe('one two three');
    });

    it('handles custom classes as string correctly', () => {
        const defaultClasses = ['one', 'two'];
        const customClasses = 'one two three';

        const result = mergeClassNames(defaultClasses, customClasses);

        expect(result).toBe('one two three');
    });

    it('handles default & custom classes as array correctly', () => {
        const defaultClasses = ['one', 'two'];
        const customClasses = ['one', 'two', 'three'];

        const result = mergeClassNames(defaultClasses, customClasses);

        expect(result).toBe('one two three');
    });

    it('handles default classes as string when no custom classes provided', () => {
        const defaultClasses = 'one two';
        const customClasses = undefined;

        const result = mergeClassNames(defaultClasses, customClasses);

        expect(result).toBe('one two');
    });

    it('handles default classes as array when no custom classes provided', () => {
        const defaultClasses = ['one', 'two'];
        const customClasses = undefined;

        const result = mergeClassNames(defaultClasses, customClasses);

        expect(result).toBe('one two');
    });
});
