const {createClient} = require('@sanity/client');
const _ = require('lodash');

const {config} = require('../../../config');

const sanityClient = createClient(config.services.sanity.client);

async function setTranslations(document, language = '') {
    if (!document.language) {
        return document;
    }

    let languagesProjection = 'value->';
    if (language) {
        languagesProjection = '_key == $language => {"value": @.value->}';
    }

    const translationsQuery = `*[_type == "translation.metadata" && references($docId)] {
        "i18n": translations[] {
            ${languagesProjection}
        }
    }[0]`;

    let translations = await sanityClient.fetch(translationsQuery, {docId: document._id, language});

    translations = _(translations?.i18n).map(({value}) => value)
        .keyBy('language')
        .value();

    if (_.isEmpty(translations)) {
        translations = {
            [document.language]: await sanityClient.fetch('*[_id == $docId][0]', {docId: document._id})
        };
    }

    document.i18n = translations;

    return document;
}

async function getDocument(id, language) {
    let result = await sanityClient.getDocument(id);

    await setTranslations(result, language);

    return result;
}

async function fetchDocuments(query, params = {}, language) {
    let result = await sanityClient.fetch(query, params);

    if (!_.isArray(result)) {
        await setTranslations(result, language);
    } else {
        await Promise.all(result.map(doc => setTranslations(doc, language)));
    }

    return result;
}

module.exports = {fetchDocuments, getDocument};
