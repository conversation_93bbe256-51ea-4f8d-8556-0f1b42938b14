import _ from 'lodash';
import React from 'react';
import PropTypes from 'prop-types';

import translations from './translations.json';

export function t(label) {
    if (typeof label !== 'string') {
        return label;
    }

    return _.get(translations, label, label);
}

export function T({children, ...props}) {
    return <span {...props}>{t(children)}</span>;
}

T.propTypes = {
    children: PropTypes.oneOfType([
        PropTypes.object,
        PropTypes.array
    ]),
    interpolationMap: PropTypes.object
};
