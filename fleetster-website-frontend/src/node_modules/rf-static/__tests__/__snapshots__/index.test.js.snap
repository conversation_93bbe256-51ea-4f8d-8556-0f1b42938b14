// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`rf-static getChangelogPaths should generate paths for non-empty changelogs 1`] = `
[
  {
    "params": {
      "slug": [
        "produkt-updates",
        "q4-2021",
      ],
    },
  },
  {
    "params": {
      "slug": [
        "produkt-updates",
        "q1-2021",
      ],
    },
  },
  {
    "params": {
      "slug": [
        "produkt-updates",
        "q1-2020",
      ],
    },
  },
  {
    "params": {
      "slug": [
        "produkt-updates",
        "q4-2020",
      ],
    },
  },
  {
    "params": {
      "slug": [
        "produkt-updates",
        "q3-2020",
      ],
    },
  },
  {
    "params": {
      "slug": [
        "produkt-updates",
        "q2-2020",
      ],
    },
  },
  {
    "params": {
      "slug": [
        "produkt-updates",
      ],
    },
  },
]
`;

exports[`rf-static getChangelogRoute should fetch and transform a changelog route by slug 1`] = `
{
  "_updatedAt": "2021-03-30T09:38:51.627Z",
  "content": [
    {
      "content": [
        {
          "_key": "42539dd8a013",
          "_type": "block",
          "children": [
            {
              "_key": "becac38ba3dd",
              "_type": "span",
              "marks": [],
              "text": "Let us see if this works Alex -> This is the german page",
            },
          ],
          "markDefs": [],
          "style": "normal",
        },
      ],
      "language": "de",
      "pageSlug": "produkt-updates/q4-2021",
      "quarter": 4,
      "releaseDate": "2021-12-22",
      "releaseVersion": "3.99",
      "tags": [],
      "title": "Welcome to the fleetster changelog",
      "year": 2021,
    },
    {
      "content": [
        {
          "_key": "609b5ef651e6",
          "_type": "block",
          "children": [
            {
              "_key": "150a6a147c2b",
              "_type": "span",
              "marks": [],
              "text": "asdasdaddaddada",
            },
          ],
          "markDefs": [],
          "style": "normal",
        },
      ],
      "language": "de",
      "pageSlug": "produkt-updates/q4-2021",
      "quarter": 4,
      "releaseDate": "2021-12-06",
      "releaseVersion": "999",
      "title": "Infinity",
      "year": 2021,
    },
    {
      "content": [
        {
          "_key": "16bfb4db4e2e",
          "_type": "block",
          "children": [
            {
              "_key": "c8909871a0fd",
              "_type": "span",
              "marks": [],
              "text": "adsadsadsa",
            },
          ],
          "markDefs": [],
          "style": "normal",
        },
      ],
      "language": "de",
      "pageSlug": "produkt-updates/q4-2021",
      "quarter": 4,
      "releaseDate": "2021-12-05",
      "releaseVersion": "666",
      "title": "Even more",
      "year": 2021,
    },
    {
      "content": [
        {
          "_key": "0412cfbc36d9",
          "_type": "block",
          "children": [
            {
              "_key": "21847eb29944",
              "_type": "span",
              "marks": [],
              "text": "asdasdasd",
            },
          ],
          "markDefs": [],
          "style": "normal",
        },
      ],
      "language": "de",
      "pageSlug": "produkt-updates/q4-2021",
      "quarter": 4,
      "releaseDate": "2021-12-03",
      "releaseVersion": "1213",
      "title": "Something more",
      "year": 2021,
    },
    {
      "content": [
        {
          "_key": "b546b1dd476c",
          "_type": "block",
          "children": [
            {
              "_key": "3a98bf2cb9e8",
              "_type": "span",
              "marks": [],
              "text": "asdasdasdda",
            },
          ],
          "markDefs": [],
          "style": "normal",
        },
      ],
      "language": "de",
      "pageSlug": "produkt-updates/q4-2021",
      "quarter": 4,
      "releaseDate": "2021-12-02",
      "releaseVersion": "123",
      "title": "Something else",
      "year": 2021,
    },
    {
      "content": [
        {
          "_key": "afa9e4661959",
          "_type": "block",
          "children": [
            {
              "_key": "0f2f131e7fc7",
              "_type": "span",
              "marks": [],
              "text": "asdasdasd",
            },
          ],
          "markDefs": [],
          "style": "normal",
        },
      ],
      "language": "de",
      "pageSlug": "produkt-updates/q4-2021",
      "quarter": 4,
      "releaseDate": "2021-12-01",
      "releaseVersion": "3.845845",
      "title": "Something",
      "year": 2021,
    },
    {
      "content": [
        {
          "_key": "f9acd3003b49",
          "_type": "block",
          "children": [
            {
              "_key": "063c5689d9af",
              "_type": "span",
              "marks": [],
              "text": "asdasd",
            },
          ],
          "markDefs": [],
          "style": "normal",
        },
      ],
      "language": "de",
      "pageSlug": "produkt-updates/q4-2021",
      "quarter": 4,
      "releaseDate": "2021-11-03",
      "releaseVersion": "777",
      "title": "Last one",
      "year": 2021,
    },
    {
      "content": [
        {
          "_key": "86b299795f1b",
          "_type": "block",
          "children": [
            {
              "_key": "b2ee087ec302",
              "_type": "span",
              "marks": [],
              "text": "Yeahnasfksjfadkfdsadad",
            },
          ],
          "markDefs": [],
          "style": "normal",
        },
      ],
      "language": "de",
      "pageSlug": "produkt-updates/q4-2021",
      "quarter": 4,
      "releaseDate": "2021-10-01",
      "releaseVersion": "123123",
      "title": "Really the last one",
      "year": 2021,
    },
  ],
  "host": {
    "loginUrl": "http://localhost:8000",
    "url": "http://localhost:3000",
  },
  "i18n": {
    "de": {
      "slug": {
        "current": "produkt-updates",
      },
    },
    "en": {
      "slug": {
        "current": "product-updates",
      },
    },
    "es": {
      "slug": {
        "current": "actualizaciones",
      },
    },
    "nl": {
      "slug": {
        "current": "product-updates",
      },
    },
  },
  "id": "produkt-updates",
  "isIndex": true,
  "logo": {
    "_type": "image",
    "alt": "fleetster",
    "asset": {
      "_ref": "image-f834f95f9016aa4c4419ac82f3ffe81509d7a115-1990x493-png",
      "_type": "reference",
    },
    "height": 493,
    "width": 1990,
  },
  "pagination": "[{"year":2021,"quarter":4,"pageSlug":"produkt-updates/q4-2021"},{"year":2021,"quarter":1,"pageSlug":"produkt-updates/q1-2021"},{"year":2020,"quarter":4,"pageSlug":"produkt-updates/q4-2020"},{"year":2020,"quarter":3,"pageSlug":"produkt-updates/q3-2020"},{"year":2020,"quarter":2,"pageSlug":"produkt-updates/q2-2020"},{"year":2020,"quarter":1,"pageSlug":"produkt-updates/q1-2020"}]",
  "slug": "produkt-updates",
}
`;

exports[`rf-static getNewsPaths should generate paths for non-empty news 1`] = `
[
  {
    "params": {
      "slug": [
        "neuigkeiten",
      ],
    },
  },
  {
    "params": {
      "slug": [
        "neuigkeiten",
        "2020",
      ],
    },
  },
  {
    "params": {
      "slug": [
        "neuigkeiten",
        "2021",
      ],
    },
  },
]
`;

exports[`rf-static getNewsRoute should fetch and transform a news route by slug 1`] = `
{
  "canonical": "http://localhost:3000/neuigkeiten",
  "content": [
    {
      "_key": "c3cf0f2d1092",
      "_type": "articleHeadingSection",
      "author": "Ace Ventura",
      "description": "Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.",
      "heading": "SOme heading",
      "image": {
        "_type": "image",
        "asset": {
          "_ref": "image-0c4541069612f52d2a47f9c5d16e90e52e8d195a-2500x1667-jpg",
          "_type": "reference",
        },
      },
      "imageAlt": "My car",
      "includeInNews": true,
      "isIndex": true,
      "pageSlug": "v4/request/30-day-trial",
      "publishedOn": "2021-12-05",
      "year": 2021,
    },
    {
      "_key": "5b4dabcbae3c",
      "_type": "articleHeadingSection",
      "author": "Papichulo",
      "description": "IM SO METAAAAAAAAAAAAAAAAAAA",
      "heading": "This is some news for you",
      "image": {
        "_type": "image",
        "asset": {
          "_ref": "image-20759ace134bbf45b13c660b55f3dcbb988d8b4a-1280x720-jpg",
          "_type": "reference",
        },
      },
      "imageAlt": "Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.",
      "includeInNews": true,
      "isIndex": true,
      "pageSlug": "v4/request/30-day-trial",
      "publishedOn": "2021-07-01",
      "year": 2021,
    },
    {
      "_key": "7eb8e59dc8e9",
      "_type": "articleHeadingSection",
      "author": "Ahjhafhdhfa",
      "description": "fesdafdsafds",
      "heading": "asdasd",
      "image": {
        "_ref": "image-066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852-jpg",
        "_type": "image",
        "asset": {
          "_ref": "image-066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852-jpg",
          "_type": "reference",
        },
      },
      "imageAlt": "asdasdd",
      "includeInNews": true,
      "isIndex": true,
      "pageSlug": "elektronische-fuehrerschein-kontrolle",
      "publishedOn": "2020-07-09",
      "year": 2020,
    },
  ],
  "description": "News Meta description",
  "host": {
    "loginUrl": "http://localhost:8000",
    "url": "http://localhost:3000",
  },
  "i18n": {
    "de": {
      "slug": {
        "current": "neuigkeiten",
      },
    },
    "en": {
      "slug": {
        "current": "news",
      },
    },
    "es": {
      "slug": {
        "current": "noticias",
      },
    },
    "nl": {
      "slug": {
        "current": "nieuws",
      },
    },
  },
  "id": "neuigkeiten",
  "logo": {
    "_type": "image",
    "alt": "fleetster",
    "asset": {
      "_ref": "image-f834f95f9016aa4c4419ac82f3ffe81509d7a115-1990x493-png",
      "_type": "reference",
    },
    "height": 493,
    "width": 1990,
  },
  "pagination": [
    {
      "pageSlug": "neuigkeiten/2021",
      "year": "2021",
    },
    {
      "pageSlug": "neuigkeiten/2020",
      "year": "2020",
    },
  ],
  "slug": "neuigkeiten",
}
`;

exports[`rf-static getRegularPageStaticPaths should return the correct static paths 1`] = `
[
  {
    "params": {
      "slug": [
        "v4",
        "corsoroute",
      ],
    },
  },
  {
    "params": {
      "slug": [
        "v4",
        "request",
        "online-demo",
      ],
    },
  },
  {
    "params": {
      "slug": [
        "elektronische-fuehrerschein-kontrolle",
      ],
    },
  },
  {
    "params": {
      "slug": [
        "v4",
        "request",
        "30-day-trial",
      ],
    },
  },
  {
    "params": {
      "slug": [
        "v4",
        "request",
        "online-demo",
        "desired-features",
      ],
    },
  },
  {
    "params": {
      "slug": [
        "v4",
        "request",
        "document-request",
      ],
    },
  },
]
`;
