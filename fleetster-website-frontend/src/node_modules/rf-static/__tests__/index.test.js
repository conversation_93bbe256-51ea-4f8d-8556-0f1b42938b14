import {
    buildOpenGraphImage,
    getOpenGraphImages,
    getRegularPageStaticProps,
    getRegularPageStaticPaths,
    getNewsRoute,
    getNewsPaths,
    getChangelogRoute,
    getChangelogPaths
} from '../index';

jest.mock('rf-data', () => ({
    imageBuilder: {
        image: jest.fn().mockImplementation(() => ({
            width:  jest.fn().mockReturnThis(),
            height: jest.fn().mockReturnThis(),
            url:    jest.fn().mockReturnValue('mocked_url')
        }))
    }
}));

describe('rf-static', () => {
    describe('buildOpenGraphImage', () => {
        test('should correctly construct open graph image', () => {
            const params = {
                title:          'Test Title',
                width:          1200,
                height:         630,
                openGraphImage: 'testImage'
            };

            const result = buildOpenGraphImage(params);

            expect(result).toEqual({
                url:    'mocked_url',
                alt:    'Test Title',
                width:  1200,
                height: 630
            });
        });
    });

    describe('getOpenGraphImages', () => {
        test('should return an array of open graph images if openGraphImage is provided', () => {
            const title = 'Test Title';
            const openGraphImage = 'testImage';

            const result = getOpenGraphImages(title, openGraphImage);

            expect(result).toHaveLength(3);
            expect(result[0]).toEqual({
                url:    'mocked_url',
                alt:    'Test Title',
                width:  1200,
                height: 630
            });
        });

        test('should return an empty array if openGraphImage is not provided', () => {
            const title = 'Test Title';

            const result = getOpenGraphImages(title, null);

            expect(result).toEqual([]);
        });
    });

    describe('getRegularPageStaticProps', () => {
        test('should return empty props if page data is missing', () => {
            const slug = 'missingPageSlug';
            const result = getRegularPageStaticProps(slug);
            expect(result).toEqual({});
        });

        test('should return the correct props for a given slug', () => {
            const slug = 'v4/corsoroute';
            const result = getRegularPageStaticProps(slug);

            expect(result).toHaveProperty('props.route');
            expect(result.props.route).toEqual(expect.objectContaining({
                _id:  'route1',
                slug: 'v4/corsoroute'
            }));
        });
    });

    describe('getRegularPageStaticPaths', () => {
        test('should return the correct static paths', () => {
            const result = getRegularPageStaticPaths();

            expect(result).toMatchSnapshot();
        });
    });

    describe('getNewsRoute', () => {
        test('should fetch and transform a news route by slug', () => {
            const slug = 'neuigkeiten';
            const result = getNewsRoute(slug);

            expect(result).toMatchSnapshot();
        });
    });

    describe('getNewsPaths', () => {
        test('should generate paths for non-empty news', () => {
            const result = getNewsPaths();

            expect(result).toMatchSnapshot();
        });
    });

    describe('getChangelogRoute', () => {
        test('should fetch and transform a changelog route by slug', () => {
            const slug = 'produkt-updates';
            const result = getChangelogRoute(slug);

            expect(result).toMatchSnapshot();
        });
    });

    describe('getChangelogPaths', () => {
        test('should generate paths for non-empty changelogs', () => {
            const result = getChangelogPaths();

            expect(result).toMatchSnapshot();
        });
    });
});
