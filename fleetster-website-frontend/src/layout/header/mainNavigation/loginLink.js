import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';
import _ from 'lodash';

import {i18n, language} from 'rf-data/siteConfig.json';
import {removeEndSlash} from 'rf-utils';
import {colors, vSpacing, hSpacing} from 'rf-styles';
import {FleetsterIcon} from 'rf-icon';
import {headerButtonsHeight, headerButtonsWidth} from './constants';

const borderSize = 2;
const padding = vSpacing.xs;
const height = headerButtonsHeight - (borderSize * 2) - (padding * 2);
const width = headerButtonsWidth - (borderSize * 2) - (padding * 2);

const styles = css`
  a {
    color: ${colors.darkGray};
    white-space: nowrap;
    text-decoration: none;
    text-align: center;
    border: ${borderSize}px solid ${colors.darkGray};
    border-radius: 4px;
    margin-bottom: 0;
    padding: ${padding}px;
    height: ${height}px;
    width: ${width}px;
    box-sizing: initial;
  }

  a:hover, a:focus-within {
    color: ${colors.gray};
    border-color: ${colors.gray};
  }
`;

export function LoginLink() {
    const {text, route, url} = _.get(i18n, [language, 'mainNavigation', 'loginLink'], {});

    if (!text) {
        return null;
    }

    const href = _.get(route, 'href', removeEndSlash(url));

    return (
        <>
            <a href={href} className={'center-content'}>
                <FleetsterIcon icon={'loginRound'} customStyles={{paddingRight: hSpacing.xxs}}/>
                {text}
            </a>
            <style jsx>{styles}</style>
        </>
    );
}

LoginLink.propTypes = {
    text:  PropTypes.string,
    route: PropTypes.object,
    url:   PropTypes.string
};

