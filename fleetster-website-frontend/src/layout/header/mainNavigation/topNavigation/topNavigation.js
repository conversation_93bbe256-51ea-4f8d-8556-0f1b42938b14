import React from 'react';
import css from 'styled-jsx/css';
import _ from 'lodash';

import {i18n, language} from 'rf-data/siteConfig.json';
import {screenSizes} from 'rf-styles';

import {DropdownMenu} from './dropdownMenu';
import {TopNavigationLink} from './topNavigationLink';

const styles = css`
.top-navigation {
    height: 100%;
    display: none;
}
@media (min-width: ${screenSizes.s}px) { .top-navigation {
    display: flex;
    justify-content: space-between;
}}`;

export function TopNavigation() {
    const {dropdown, links = [], dropdownResources} = _.get(i18n, [language, 'mainNavigation'], {});

    return (
        <div className={'center-content top-navigation'}>
            <DropdownMenu key={'products'} iconFallback={'vehicle'} {...dropdown}/>
            {links.map(link => <TopNavigationLink key={link._key} {...link}/>)}
            <DropdownMenu key={'resources'} iconFallback={'resourcesBox'} {...dropdownResources}/>
            <style jsx>{styles}</style>
        </div>
    );
}
