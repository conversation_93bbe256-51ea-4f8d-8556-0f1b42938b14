import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {Image, RFLink} from 'rf-components';
import {vSpacing, hSpacing, colors} from 'rf-styles';

import {dropdownIconSize} from '../constants';

const styles = css`
.nav-link {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    width: 435px;
    padding: 8px ${hSpacing.sm}px;
}
.nav-link:hover, .nav-link:focus {
    background-color: ${colors.lightGray};
}

.icon {
    margin-top: ${vSpacing.xxs * 2}px;
    margin-right: ${hSpacing.s}px;
    min-width: ${dropdownIconSize}px;
    min-height: ${dropdownIconSize}px;
    width: ${dropdownIconSize}px;
    height: ${dropdownIconSize}px;
}

.h5 {
    font-size: 14px;
    line-height: 18px;
    color: ${colors.blue};
}

a {
    margin-bottom: 0;
    text-decoration: none;
    outline: none;
}

p {
    font-size: 12px;
    line-height: 16px;
    margin-bottom: 0;
}`;

export function DropdownMenuItem({text, description, icon, route, url}) {
    if (!text) {
        return null;
    }

    return (
        <RFLink className={'nav-link'} route={route} url={url} styles={styles}>
            <div className={'icon'}>
                <Image image={icon} imageAlt={`${description} icon`} width={icon.width} height={icon.height}/>
            </div>
            <div>
                <span className={'h5'}>{text}</span>
                <p>{description}</p>
            </div>
            <style jsx>{styles}</style>
        </RFLink>
    );
}

DropdownMenuItem.propTypes = {
    text:        PropTypes.string,
    description: PropTypes.string,
    icon:        PropTypes.object,
    route:       PropTypes.object,
    url:         PropTypes.string
};
