import React from 'react';

describe('Top navigation link', () => {
    const icon = {
        _type: 'image',
        asset: {
            _ref:  'image-b56718596084267a4f79129c51096493d1bdce72-225x225-png',
            _type: 'reference'
        }
    };

    test('renders as expected with a route', () => {
        const {TopNavigationLink} = require('../topNavigationLink');

        const component = render(<TopNavigationLink link={{route: {href: 'http://localhost:3000/v4/corsoroute'}, text: 'testing'}} icon={icon}/>);

        expect(component.toJSON()).toMatchSnapshot();
    });

    test('renders as expected with a url', () => {
        const {TopNavigationLink} = require('../topNavigationLink');

        const component = render(<TopNavigationLink text={'testing'} link={{url: 'https://www.google.com', text: 'testing'}} icon={icon}/>);

        expect(component.toJSON()).toMatchSnapshot();
    });
});

