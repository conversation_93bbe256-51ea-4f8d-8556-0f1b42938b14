import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {Image, RFLink} from 'rf-components';
import {hSpacing, colors, fontWeights} from 'rf-styles';

import {siderIconSize} from '../constants';

const styles = css`
    .nav-link {
        display: flex;
        align-items: center;
        flex-direction: row;
        flex-wrap: nowrap;
        flex-grow: 0;
        padding-left: 5px;
        user-select: none;
    }
  
    .nav-link:hover, .nav-link:focus {
        background-color: ${colors.lightGray};
    }
    
    .icon {
        min-width: ${siderIconSize}px;
        min-height: ${siderIconSize}px;
        width: ${siderIconSize}px;
        height: ${siderIconSize}px;
    }
    
    .h5 {
      padding: ${hSpacing.xs}px;
      margin-bottom: 0;
      white-space: nowrap;
      font-size: 12px;
      line-height: 18px;
      font-weight: ${fontWeights.semiBold};
    }

    a {
        margin-bottom: 0;
        text-decoration: none;
        outline: none;
    }
`;

export function SideNavigationLink({text, icon, route, link, showIcon}) {
    if (!text) {
        return null;
    }

    return (
        <RFLink className={'nav-link'} route={route} url={link} styles={styles}>
            {showIcon && <div className={'icon'}>
                <Image image={icon} imageAlt={`${text} icon`}/>
            </div>}
            <span className={'h5'}>{text}</span>
            <style jsx>{styles}</style>
        </RFLink>
    );
}

SideNavigationLink.propTypes = {
    text:     PropTypes.string,
    showIcon: PropTypes.bool,
    icon:     PropTypes.object,
    route:    PropTypes.object,
    link:     PropTypes.string
};

