import React from 'react';
import css from 'styled-jsx/css';

import {FleetsterIcon} from 'rf-icon';
import {screenSizes} from 'rf-styles';
import {Sider} from './sider';

const styles = css`
  .side-navigation {
    display: flex;
    flex: 3;
    width: auto;
    height: auto;
    overflow: unset;
  }

  @media (min-width: ${screenSizes.s}px) {
    .side-navigation {
      flex: 0;
      width: 0;
      height: 0;
      overflow: hidden;
    }
  }

  label {
    cursor: pointer;
  }

  #sider-toggle {
    display: none
  }

  #sider-toggle:checked + .sider-container {
    width: 100vw;
    height: 100%;
    overflow: unset;
  }

  @media (min-width: ${screenSizes.s}px) {
    #sider-toggle:checked + .sider-container {
      width: 0;
      height: 0;
      overflow: hidden;
    }
  }

  .sider-container {
    position: fixed;
    z-index: 11;
    top: 0;
    left: 0;
    width: 0;
    height: 0;
    overflow: hidden;
  }

  .sider-container label {
    position: absolute;
    z-index: -1;
    cursor: default;
    display: block;
    width: 100%;
    height: 100%;
  }`;

export function SideNavigation() {
    return (
        <div className={'side-navigation'}>
            <div className={'sider-button'}>
                <label htmlFor={'sider-toggle'}>
                    <FleetsterIcon icon={'hamburguerList'}/>
                </label>
                <input type={'checkbox'} id={'sider-toggle'}/>
                <div className={'sider-container'}>
                    <label htmlFor={'sider-toggle'}/>
                    <Sider/>
                </div>
            </div>
            <style jsx>{styles}</style>
        </div>
    );
}
