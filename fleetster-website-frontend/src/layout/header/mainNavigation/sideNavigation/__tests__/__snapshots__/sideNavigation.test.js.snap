// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Side navigation Still renders fine if no links 1`] = `
<div
  className="side-navigation"
>
  <div
    className="sider-button"
  >
    <label
      htmlFor="sider-toggle"
    >
      <span
        className="bp3-icon"
      >
        <div
          height={20}
          src="/v4/svgs/hamburguerList.svg"
          width="20px"
        />
      </span>
    </label>
    <input
      id="sider-toggle"
      type="checkbox"
    />
    <div
      className="sider-container"
    >
      <label
        htmlFor="sider-toggle"
      />
      <div
        className="sider"
      >
        <label
          className="toggle-container"
          htmlFor="sider-toggle"
        >
          <span
            className="bp3-icon"
          >
            <div
              height={20}
              src="/v4/svgs/hamburguerList.svg"
              width="20px"
            />
          </span>
        </label>
        <div>
          <div
            className="dropdown-trigger"
            onClick={[Function]}
          >
            <span
              className="bp3-icon"
            >
              <div
                height={18}
                src="/v4/svgs/vehicle.svg"
                width="18px"
              />
            </span>
            <p
              className="h5"
            >
              Products
            </p>
            <span
              className="bp3-icon"
            >
              <div
                height={14}
                src="/v4/svgs/caretDown.svg"
                width="14px"
              />
            </span>
          </div>
          <style />
        </div>
        <a
          aria-label="Contact us"
          className="nav-link"
        >
          <span
            className="bp3-icon"
          >
            <div
              height={18}
              src="/v4/svgs/mail.svg"
              width="18px"
            />
          </span>
          <span
            className="h5"
          >
            Contact us
          </span>
        </a>
        <style />
        <div
          style={
            {
              "flex": 100,
            }
          }
        />
        <div
          className="bottom-buttons"
        >
          <a
            className="center-content"
          >
            <span
              className="bp3-icon"
            >
              <div
                height={22}
                src="/v4/svgs/whiteLabel.svg"
                width="22px"
              />
            </span>
            Demo
          </a>
          <style />
          <a
            className="center-content"
            href="https://my.fleetster.net"
          >
            <span
              className="bp3-icon"
            >
              <div
                height={20}
                src="/v4/svgs/loginRound.svg"
                width="20px"
              />
            </span>
            Login
          </a>
          <style />
        </div>
        <style />
      </div>
    </div>
  </div>
  <style />
</div>
`;

exports[`Side navigation renders as expected 1`] = `
<div
  className="side-navigation"
>
  <div
    className="sider-button"
  >
    <label
      htmlFor="sider-toggle"
    >
      <span
        className="bp3-icon"
      >
        <div
          height={20}
          src="/v4/svgs/hamburguerList.svg"
          width="20px"
        />
      </span>
    </label>
    <input
      id="sider-toggle"
      type="checkbox"
    />
    <div
      className="sider-container"
    >
      <label
        htmlFor="sider-toggle"
      />
      <div
        className="sider"
      >
        <label
          className="toggle-container"
          htmlFor="sider-toggle"
        >
          <span
            className="bp3-icon"
          >
            <div
              height={20}
              src="/v4/svgs/hamburguerList.svg"
              width="20px"
            />
          </span>
        </label>
        <div>
          <div
            className="dropdown-trigger"
            onClick={[Function]}
          >
            <span
              className="bp3-icon"
            >
              <div
                height={18}
                src="/v4/svgs/vehicle.svg"
                width="18px"
              />
            </span>
            <p
              className="h5"
            >
              Products
            </p>
            <span
              className="bp3-icon"
            >
              <div
                height={14}
                src="/v4/svgs/caretDown.svg"
                width="14px"
              />
            </span>
          </div>
          <style />
        </div>
        <a
          className="nav-link"
          rel="noreferrer noopener"
          target="_blank"
        >
          <div
            className="icon"
          >
            <div
              className="center-content"
              style={
                {
                  "height": "100%",
                  "width": "100%",
                }
              }
            >
              <img
                alt="Lejla test icon"
                decoding="async"
                height={449}
                loading="lazy"
                sizes="(max-width: 362px) 361px, 465px"
                src="https://cdn.sanity.io/images/dp11egz7/development/d58fe928015b2c9d27c175e3163e952d81f4530c-465x449.png?w=465&h=449&auto=format"
                srcSet="https://cdn.sanity.io/images/dp11egz7/development/d58fe928015b2c9d27c175e3163e952d81f4530c-465x449.png?rect=1,0,464,449&w=361&h=349&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/d58fe928015b2c9d27c175e3163e952d81f4530c-465x449.png?w=465&h=449&auto=format 465w"
                style={
                  {
                    "height": "auto",
                    "maxHeight": 449,
                    "maxWidth": 465,
                    "width": "100%",
                  }
                }
                width={465}
              />
            </div>
          </div>
          <span
            className="h5"
          >
            Lejla test
          </span>
          <style />
        </a>
        <style />
        <a
          className="nav-link"
          href="https://www.google.com"
          rel="noreferrer noopener"
          target="_blank"
        >
          <div
            className="icon"
          >
            <div
              className="center-content"
              style={
                {
                  "height": "100%",
                  "width": "100%",
                }
              }
            >
              <img
                alt="Google icon"
                decoding="async"
                height={224}
                loading="lazy"
                sizes="228px"
                src="https://cdn.sanity.io/images/dp11egz7/development/e18ed6815845cfa79edc623d7e7f6ef6c31dc3bb-228x224.png?w=228&h=224&auto=format"
                srcSet="https://cdn.sanity.io/images/dp11egz7/development/e18ed6815845cfa79edc623d7e7f6ef6c31dc3bb-228x224.png?w=228&h=224&auto=format 228w"
                style={
                  {
                    "height": "auto",
                    "maxHeight": 224,
                    "maxWidth": 228,
                    "width": "100%",
                  }
                }
                width={228}
              />
            </div>
          </div>
          <span
            className="h5"
          >
            Google
          </span>
          <style />
        </a>
        <style />
        <a
          aria-label="Contact us"
          className="nav-link"
        >
          <span
            className="bp3-icon"
          >
            <div
              height={18}
              src="/v4/svgs/mail.svg"
              width="18px"
            />
          </span>
          <span
            className="h5"
          >
            Contact us
          </span>
        </a>
        <style />
        <div
          style={
            {
              "flex": 100,
            }
          }
        />
        <div
          className="bottom-buttons"
        >
          <a
            className="center-content"
          >
            <span
              className="bp3-icon"
            >
              <div
                height={22}
                src="/v4/svgs/whiteLabel.svg"
                width="22px"
              />
            </span>
            Demo
          </a>
          <style />
          <a
            className="center-content"
            href="https://my.fleetster.net"
          >
            <span
              className="bp3-icon"
            >
              <div
                height={20}
                src="/v4/svgs/loginRound.svg"
                width="20px"
              />
            </span>
            Login
          </a>
          <style />
        </div>
        <style />
      </div>
    </div>
  </div>
  <style />
</div>
`;
