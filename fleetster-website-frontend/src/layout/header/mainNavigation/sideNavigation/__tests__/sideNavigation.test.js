import React from 'react';
import _ from 'lodash';

import mockSiteConfig from '../../../../../../__mocks__/siteConfig.json';

describe('Side navigation', () => {
    test('renders as expected', () => {
        const {SideNavigation} = require('../index');
        const component = render(<SideNavigation/>);

        expect(component.toJSON()).toMatchSnapshot();
    });

    test('Do not throw when dropdown is empty', () => {
        let siteConfigCopy = _.cloneDeep(mockSiteConfig);
        _.unset(siteConfigCopy, 'i18n.de.mainNavigation.dropdown');

        jest.setMock('rf-data/siteConfig.json', siteConfigCopy);

        const {SideNavigation} = require('../index');

        expect(() => render(<SideNavigation/>)).not.toThrow();
    });

    test('Still renders fine if no links', () => {
        let siteConfigCopy = _.cloneDeep(mockSiteConfig);
        _.unset(siteConfigCopy, 'i18n.de.mainNavigation.links');

        jest.setMock('rf-data/siteConfig.json', siteConfigCopy);

        const {SideNavigation} = require('../index');

        const component = render(<SideNavigation/>);

        expect(component.toJSON()).toMatchSnapshot();
    });
});

