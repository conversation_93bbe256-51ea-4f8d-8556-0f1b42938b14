import React from 'react';
import css from 'styled-jsx/css';
import _ from 'lodash';

import {i18n, language} from 'rf-data/siteConfig.json';
import {hSpacing, colors, fontWeights} from 'rf-styles';
import {FleetsterIcon} from 'rf-icon';

import {siderIconSize, topIconSize} from '../constants';

const styles = css`
  .nav-link {
    display: flex;
    align-items: center;
    flex-direction: row;
    flex-wrap: nowrap;
    flex-grow: 0;
    padding-left: 5px;
    user-select: none;
  }

  .nav-link:hover, .nav-link:focus {
    background-color: ${colors.lightGray};
  }

  .icon {
    min-width: ${siderIconSize}px;
    min-height: ${siderIconSize}px;
    width: ${siderIconSize}px;
    height: ${siderIconSize}px;
    color: gray;
  }

  .h5 {
    padding: ${hSpacing.xs}px;
    margin-bottom: 0;
    white-space: nowrap;
    font-size: 12px;
    line-height: 18px;
    font-weight: ${fontWeights.semiBold};
  }

  a {
    margin-bottom: 0;
    text-decoration: none;
    outline: none;
    color: ${colors.darkGray};
  }

`;

export function SideNavigationContactLink() {
    const contactLink = _.get(i18n, [language, 'mainNavigation', 'contactLink']);

    if (!contactLink) {
        return null;
    }

    return (
        <>
            <a className={'nav-link'} href={contactLink.url} aria-label={contactLink.text}>
                <FleetsterIcon iconSize={topIconSize} icon={'mail'}/>
                <span className={'h5'}>{contactLink.text}</span>
            </a>
            <style jsx>{styles}</style>
        </>
    );
}
