import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';
import _ from 'lodash';

import {i18n, language} from 'rf-data/siteConfig.json';
import {removeEndSlash} from 'rf-utils';
import {colors, vSpacing, hSpacing} from 'rf-styles';
import {FleetsterIcon} from 'rf-icon';
import {headerButtonsHeight, headerButtonsWidth} from './constants';

const padding = vSpacing.xs;
const height = headerButtonsHeight - (padding * 2);
const width = headerButtonsWidth - (padding * 2);

const styles = css`
  a {
    text-align: center;
    color: ${colors.white};
    text-decoration: none;
    background-color: ${colors.darkGray};
    border-radius: 4px;
    border: none;
    margin-bottom: 0;
    padding: ${padding}px;
    height: ${height}px;
    width: ${width}px;
    box-sizing: initial;
  }

  a:hover, a:focus-within {
    background-color: ${colors.gray};
  }`;

export function DemoLink() {
    const {text, route, url} = _.get(i18n, [language, 'mainNavigation', 'demoLink'], {});

    if (!text) {
        return null;
    }
    const href = _.get(route, 'href', removeEndSlash(url));

    return (
        <>
            <a href={href} className={'center-content'}>
                <FleetsterIcon iconSize={22} icon={'whiteLabel'} customStyles={{paddingRight: hSpacing.xxs + 2}}/>
                {text}
            </a>
            <style jsx>{styles}</style>
        </>
    );
}

DemoLink.propTypes = {
    text:  PropTypes.string,
    route: PropTypes.object,
    url:   PropTypes.string
};

