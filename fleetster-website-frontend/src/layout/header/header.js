import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {colors, hSpacing, screenSizes, vSpacing} from 'rf-styles';

import {LanguagePicker} from './languagePicker';
import {Logo} from './logo';
import {TopNavigation, SideNavigation} from './mainNavigation';
import {ContactLink} from './mainNavigation/contactLink';
import {DemoLink} from './mainNavigation/demoLink';
import {LoginLink} from './mainNavigation/loginLink';

const styles = css`
  .header-component {
    z-index: 10;
    position: fixed;
    color: ${colors.darkGray};
    background-color: ${colors.white};
    width: 100%;
    height: ${vSpacing.xl}px;
  }

  .header-container {
    width: 100%;
    height: 100%;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.3);
  }

  .right-buttons { display: none }
  
  @media (min-width: ${screenSizes.s}px) { .right-buttons {
    column-gap: ${hSpacing.xs}px;
    display: flex
  }}
`;

export function Header({route}) {
    return (
        <header className={'header-component'}>
            <div className={'page-horizontal-padding page-horizontal-padding center-content header-container'}>
                <SideNavigation/>
                <Logo/>
                <TopNavigation/>
                <div className={'rf-flex-2'}/>
                <div className={'center-content right-menu'}>
                    <ContactLink/>
                    <LanguagePicker route={route}/>
                    <div className={'rf-cell right-buttons center-content'}>
                        <DemoLink/>
                        <LoginLink/>
                    </div>
                </div>
            </div>
            <style jsx>{styles}</style>
        </header>
    );
}

Header.propTypes = {
    route: PropTypes.object
};
