import _ from 'lodash';
import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {colors, fontWeights, hSpacing, vSpacing} from 'rf-styles';
import {FleetsterIcon} from 'rf-icon';
import siteConfig from 'rf-data/siteConfig.json';

import {getAltLanguageLinks} from './getAltLanguageLinks';

const styles = css`
.language-picker {
    height: 100%;
    padding-top: ${vSpacing.xs * 2}px;
    padding-bottom: ${vSpacing.xs * 2}px;
    position: relative;
    font-size: 18px;
    font-weight: ${fontWeights.semiBold};
    cursor: pointer;
}
.language-picker:hover, .language-picker:focus-within {
    color: ${colors.gray};
}

.language-picker:hover .language-dropdown,
.language-picker:focus-within .language-dropdown {
    top: ${vSpacing.m}px;
    opacity: 1;
    pointer-events: auto;
}

.language-dropdown {
    position: absolute;
    pointer-events: none;
    right: 0;
    top: 0;
    opacity: 0;
    background-color: ${colors.white};
    padding: ${vSpacing.xs * 2}px ${hSpacing.s}px;
    border-radius: 4px;
    transition: all 200ms ease-in-out;
    flex-direction: column;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .5);
}`;

// ToDo fix language link for changelog and news -> closes https://gitlab.fleetster.de/fleetster/fleetster-website-frontend/-/issues/632

export function LanguagePicker({route}) {
    const altLanguageLinks = getAltLanguageLinks({
        route,
        languages:  siteConfig.languages,
        configLang: siteConfig.language
    });

    if (_.isEmpty(altLanguageLinks)) { return null; }

    return (
        <div className={'icon-padding center-content language-picker'}>
            <div className={'center-content'} style={{flex: 0, marginLeft: 'auto'}}>
                <FleetsterIcon icon={'world'}/>
            </div>
            <div className={'language-dropdown'}>
                {altLanguageLinks}
            </div>
            <style jsx>{styles}</style>
        </div>
    );
}

LanguagePicker.propTypes = {
    pageId:   PropTypes.string,
    pageSlug: PropTypes.string
};
