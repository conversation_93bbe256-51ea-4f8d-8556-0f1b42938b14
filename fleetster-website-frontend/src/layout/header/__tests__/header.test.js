import React from 'react';

describe('Header', () => {
    const route = {
        i18n: {
            de: {slug: {current: 'germansales'}},
            en: {slug: {current: 'sale'}},
            es: {slug: {current: 'spanishsales'}},
            nl: {slug: {current: 'dutchsales'}}
        }
    };

    beforeAll(() => {
        jest.doMock('next/head', () => props => <div {...props} />);
    });

    beforeEach(() => {
        jest.resetAllMocks();
    });

    test('renders as expected', () => {
        const {Header} = require('../index');
        const component = render(<Header route={route}/>);

        expect(component.toJSON()).toMatchSnapshot();
    });
});

