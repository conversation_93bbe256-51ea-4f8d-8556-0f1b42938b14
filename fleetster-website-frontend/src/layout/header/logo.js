import React from 'react';
import css from 'styled-jsx/css';

import {Image} from 'rf-components';
import {host, logo} from 'rf-data/siteConfig.json';
import {hSpacing, screenSizes} from 'rf-styles';
import {removeEndSlash} from 'rf-utils';

const [logoWidth, logoHeight] = [170, 30];

const styles = css`
.logo {
    flex: 0 1 auto;
    margin-top: 0px;
    margin-bottom: 0px;
    width: ${logoWidth}px;
    height: ${logoHeight}px;
    outline : none;
}
@media (min-width: ${screenSizes.m}px) { .right-buttons {
    margin-right: ${hSpacing.s}px;
}}
`;

export function Logo() {
    return (
        <a href={removeEndSlash(host.url)} className={'logo'}>
            <Image image={logo} imageAlt={logo.alt} height={logoHeight} width={logoWidth}/>
            <style jsx>{styles}</style>
        </a>
    );
}
