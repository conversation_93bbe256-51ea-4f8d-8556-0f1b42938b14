import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {colors, fontWeights} from 'rf-styles';

const styles = css`
.language-link {  
  display: block;
  cursor: pointer;
  color: ${colors.gray};
  text-decoration: none;
  transition: color 200ms ease-in-out;
  font-size: 18px;
  font-weight: ${fontWeights.semiBold};
  margin-bottom: 0;
}

.language-link:hover, .language-link:focus-within {
  color: ${colors.darkGray};
}

.language-link:not(:last-child) {
  padding-bottom: 10px;
}
`;

export function LanguageLink({href, lang}) {
    const languageLabel = lang.label.toLowerCase();

    return (
        <a className={'language-link'} hrefLang={languageLabel} href={href} >
            {lang.label}
            <style jsx>{styles}</style>
        </a>
    );
}

LanguageLink.propTypes = {
    href: PropTypes.string.isRequired,
    lang: PropTypes.object.isRequired
};
