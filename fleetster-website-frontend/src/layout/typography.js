/* eslint-disable max-lines */
import {global} from 'styled-jsx/css';

import {screenSizes, fontWeights, colors, vSpacing} from 'rf-styles';

export const typography = global`
body {
    font-family: "Open Sans", sans-serif;
}

.h1 {
    color: ${colors.darkGray};
    font-weight: ${fontWeights.semiBold};
    font-size: 26px; 
    line-height: 30px;
    margin-top: 0;
    margin-bottom: ${vSpacing.s}px;
}
@media (min-width: ${screenSizes.xxs}px) { .h1 {
    font-size: 30px; 
    line-height: 36px;
}}
@media (min-width: ${screenSizes.m}px) { .h1 {
    font-size: 38px; 
    line-height: 44px;
}}

.h2 {
    color: ${colors.blue};
    font-weight: ${fontWeights.light};
    font-size: 24px; 
    line-height: 28px;
    margin-top: 0;
    margin-bottom: ${vSpacing.s}px;
}
@media (min-width: ${screenSizes.xxs}px) { .h2 {
    font-size: 28px; 
    line-height: 36px;
}}
@media (min-width: ${screenSizes.m}px) { .h2 {
    font-size: 34px; 
    line-height: 42px;
}}

.h3 {
    color: ${colors.blue};
    font-weight: ${fontWeights.light};
    font-size: 20px;
    line-height: 24px;
    margin-top: 0;
}
@media (min-width: ${screenSizes.xxs}px) { .h3 {
    font-size: 24px;
    line-height: 28px;
    margin-bottom: ${vSpacing.s}px;
}}
@media (min-width: ${screenSizes.m}px) { .h3 {
    font-size: 28px;
    line-height: 32px;
}}

.h4 {
    color: ${colors.blue};
    font-weight: ${fontWeights.semiBold};
    font-size: 18px;
    line-height: 22px;
    margin-top: 0px;
    margin-bottom: ${vSpacing.m}px;
}
@media (min-width: ${screenSizes.xxs}px) { .h4 {
    font-size: 20px; 
    line-height: 24px;
}}
@media (min-width: ${screenSizes.m}px) { .h4 {
    font-size: 24px; 
    line-height: 28px;
}}

.h5 {
    color: ${colors.darkGray};
    font-size: 14px;
    line-height: 28px;
    margin-top: 0;
    margin-bottom: ${vSpacing.s}px;
} 
@media (min-width: ${screenSizes.xxs}px) { .h5 {
    font-size: 16px; 
    line-height: 20px;
}}
@media (min-width: ${screenSizes.m}px) { .h5 {
    font-size: 18px; 
    line-height: 22px;
}}

.h6 {
    color: ${colors.blue};
    font-weight: ${fontWeights.semiBold};
    font-size: 12px;
    line-height: 16px;
}

@media (min-width: ${screenSizes.xxs}px) { .h6 {
    font-size: 14px; 
    line-height: 18px;
}}

@media (min-width: ${screenSizes.m}px) { .h6 {
    font-size: 16px; 
    line-height: 20px;
}}

.p-small p, .p-small a {
    color: ${colors.darkGray};
    font-weight: ${fontWeights.regular};
    font-size: 12px;
    line-height: 14px;
    margin-bottom: 0;
    margin-top: ${vSpacing.xs}px;
}

@media (min-width: ${screenSizes.l}px) { .p-small p {
    font-size: 12px; 
    line-height: 16px;
}}

.p-small a {
    color: ${colors.blue};
    font-weight: ${fontWeights.semiBold};
}

p, ol, ul, .p {
    color: ${colors.darkGray};
}

p, ol, ul, .p, p a, p .link{
    font-size: 12px; 
    margin-top: 0px;
    line-height: 16px;
    margin-bottom: ${vSpacing.s}px;
}

@media (min-width: ${screenSizes.xxs}px) { p, ol, ul, .p, p a, p .link{
    font-size: 14px; 
    margin-bottom: ${vSpacing.s}px;
    line-height: 24px;
}}

@media (min-width: ${screenSizes.m}px) { p, ol, ul, .p, p a, p .link{
    font-size: 16px; 
    line-height: 26px;
}}

p a, p .link{
    text-decoration: none;
}

a, .link {
    color: ${colors.blue};
    font-weight: ${fontWeights.semiBold};
    font-size: 12px; 
    margin-top: 0;
    margin-bottom: ${vSpacing.s}px;
    line-height: 18px;
}
@media (min-width: ${screenSizes.xs}px) { a, .link {
    font-size: 14px; 
    line-height: 20px;
}}
@media (min-width: ${screenSizes.s}px) { a, .link {
    font-size: 16px; 
    line-height: 22px;
}}

.info {
    color: ${colors.black};
    font-weight: ${fontWeights.semiBold};
    font-size: 12px; 
    margin-top: 0;
    margin-bottom: ${vSpacing.m}px;
    line-height: 12px;
    letter-spacing: 1.2px;
}`;
