import React from 'react';

describe('Layout', () => {
    beforeAll(() => {
        jest.doMock('next/head', () => props => <div {...props} />);
        jest.spyOn(global.Date.prototype, 'getFullYear').mockImplementation(() => 2023);
    });

    test('Should render correctly', () => {
        const {Layout} = require('../index');

        const component = render(
            <Layout route={{id: 'test', slug: 'test', host: {url: 'http://localhost:3000'}, image: null, breadcrumbsList: []}}>
                <div>test</div>
            </Layout>
        );

        expect(component.toJSON()).toMatchSnapshot();
    });
});
