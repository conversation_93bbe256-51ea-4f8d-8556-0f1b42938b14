import React from 'react';

import {OrganizationInformation} from '../organizationInformation';

jest.mock('next/head', () => {
    return {__esModule: true, default: ({children}) => <head title={'test'}>{children}</head>};
});

jest.mock('rf-data', () => ({
    getImageUrl: image => image.asset._ref
}));

describe('OrganizationInformation', () => {
    let component;

    const host = {url: 'http://example.com'};
    const logo = {asset: {_ref: 'image-Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000-jpg'}};

    it('renders without crashing', () => {
        component = render(<OrganizationInformation host={host} logo={logo}/>);

        expect(component).toMatchSnapshot();
    });

    it('renders the organization structured data correctly', () => {
        component = render(<OrganizationInformation host={host} logo={logo}/>);

        const scriptElement = component.root.findByType('script');
        const expectedData = {
            '@context':   'https://schema.org',
            '@type':      'Organization',
            name:         'fleetster | Next Generation Mobility GmbH',
            url:          host.url,
            logo:         logo.asset._ref,
            contactPoint: [{
                '@type':           'ContactPoint',
                telephone:         '+49-89125010212',
                availableLanguage: ['English', 'German', 'Spanish'],
                contactType:       'customer service'
            }],
            sameAs: [
                'https://www.facebook.com/corporatecarsharing/',
                'https://twitter.com/fleetster_cc',
                'https://vimeo.com/fleetster',
                'https://www.youtube.com/user/NGMmuenchen/videos',
                'https://www.linkedin.com/company/next-generation-mobility-gmbh-&-co--kg',
                'https://www.xing.com/pages/fleetster-nextgenerationmobilitygmbh-co-kgmunich'
            ]
        };

        expect(JSON.parse(scriptElement.props.dangerouslySetInnerHTML.__html)).toEqual(expectedData);
    });

    it('does not render when host or logo is not provided', () => {
        component = render(<OrganizationInformation/>);

        expect(component.root.findAllByType('script').length).toBe(0);
    });
});
