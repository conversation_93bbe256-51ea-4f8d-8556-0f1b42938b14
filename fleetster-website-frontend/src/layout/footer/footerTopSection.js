import React from 'react';
import css from 'styled-jsx/css';
import _ from 'lodash';

import {i18n, language} from 'rf-data/siteConfig.json';
import {vSpacing, screenSizes} from 'rf-styles';
import {FooterSegment} from './footerSegment';
import {FooterTopSectionText} from './footerTopSectionText';

const styles = css`
  .footer-top {
    display: flex;
    flex-direction: column;
    padding-top: ${vSpacing.l}px;
  }

  .footer-segments {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    width: 100%;
    margin-top: ${vSpacing.m}px;
    margin-bottom: ${vSpacing.m - vSpacing.s}px;
  }
  
  @media (min-width: ${screenSizes.s}px) {
    .footer-top {
      flex-direction: row;
      padding-top: ${vSpacing.l}px;
      padding-bottom: ${vSpacing.l - vSpacing.s}px;
    }
    
    .footer-segments {
      flex-wrap: wrap;
      flex-direction: row;
      margin: 0;
      row-gap: ${vSpacing.m - vSpacing.s}px;
    }
  }

  @media (min-width: ${screenSizes.m}px) {
    .footer-segments {
      flex-wrap: nowrap;
      flex-direction: row;
    }
  }
`;

export function FooterTopSection() {
    const {text, segments = []} = _.get(i18n, [language, 'footer'], {});

    return (
        <div className={'footer-top'}>
            <FooterTopSectionText text={text}/>
            <div className={'rf-flex-1 footer-segments'}>
                {segments.map(segment => <FooterSegment key={segment._key} {...segment}/>)}
            </div>
            <style jsx>{styles}</style>
        </div>
    );
}
