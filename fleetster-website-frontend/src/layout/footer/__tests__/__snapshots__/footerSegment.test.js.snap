// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Footer segment Still renders if links list is non-existent 1`] = `
<div
  className="footer-segment"
>
  <input
    className="section-toggle"
    id="segment-toggle-my_key"
    type="checkbox"
  />
  <div
    className="segment-title"
  >
    <h3
      className="h4"
    >
      HueHueBr
    </h3>
    <label
      className="caret-down"
      htmlFor="segment-toggle-my_key"
    >
      <span
        className="bp3-icon"
      >
        <div
          height={14}
          src="/v4/svgs/caretDown.svg"
          width="14px"
        />
      </span>
    </label>
  </div>
  <div
    className="segment-links"
  >
    <div
      className="segment-link view-all"
    >
      <a
        href="http://localhost:3000/v4/request/online-demo"
      >
        products.viewAll
      </a>
      <style />
    </div>
  </div>
  <style />
</div>
`;

exports[`Footer segment renders as expected 1`] = `
<div
  className="footer-segment"
>
  <input
    className="section-toggle"
    id="segment-toggle-my_key"
    type="checkbox"
  />
  <div
    className="segment-title"
  >
    <h3
      className="h4"
    >
      HueHueBr
    </h3>
    <label
      className="caret-down"
      htmlFor="segment-toggle-my_key"
    >
      <span
        className="bp3-icon"
      >
        <div
          height={14}
          src="/v4/svgs/caretDown.svg"
          width="14px"
        />
      </span>
    </label>
  </div>
  <div
    className="segment-links"
  >
    <div
      className="segment-link "
    >
      <a
        href="http://localhost:3000/v4/corsoroute"
      >
        Link 1
      </a>
      <style />
    </div>
    <div
      className="segment-link view-all"
    >
      <a
        href="http://localhost:3000/v4/request/online-demo"
      >
        products.viewAll
      </a>
      <style />
    </div>
  </div>
  <style />
</div>
`;
