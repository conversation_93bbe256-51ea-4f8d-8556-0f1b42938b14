// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Footer renders as expected in de 1`] = `
<footer
  className="page-horizontal-padding footer"
>
  <div
    className="footer-top"
  >
    <div
      className="footer-text"
    >
      <h2
        className="footer-logo"
      >
        <div
          className="center-content"
          style={
            {
              "height": "100%",
              "width": "100%",
            }
          }
        >
          <img
            alt="fleetster"
            decoding="async"
            height={50}
            loading="lazy"
            sizes="200px"
            src="https://cdn.sanity.io/images/dp11egz7/development/63c3f345cee6b1704cb6f946f77b4e3117b20d65-200x50.png?w=200&h=50&auto=format"
            srcSet="https://cdn.sanity.io/images/dp11egz7/development/63c3f345cee6b1704cb6f946f77b4e3117b20d65-200x50.png?w=200&h=50&auto=format 200w"
            style={
              {
                "height": "auto",
                "maxHeight": 50,
                "maxWidth": 200,
                "width": "100%",
              }
            }
            width={200}
          />
        </div>
      </h2>
      <p>
        We are a mobility technology company based exclusively in Munich, Germany. We develop software for fleets and mobility providers world wide.
      </p>
      <style />
    </div>
    <div
      className="rf-flex-1 footer-segments"
    >
      <div
        className="footer-segment"
      >
        <input
          className="section-toggle"
          id="segment-toggle-6b6d440dd2f3"
          type="checkbox"
        />
        <div
          className="segment-title"
        >
          <h3
            className="h4"
          >
            For Fleet Managers
          </h3>
          <label
            className="caret-down"
            htmlFor="segment-toggle-6b6d440dd2f3"
          >
            <span
              className="bp3-icon"
            >
              <div
                height={14}
                src="/v4/svgs/caretDown.svg"
                width="14px"
              />
            </span>
          </label>
        </div>
        <div
          className="segment-links"
        >
          <div
            className="segment-link "
          >
            <a>
              Link 1
            </a>
            <style />
          </div>
          <div
            className="segment-link "
          >
            <a>
              Link 2
            </a>
            <style />
          </div>
          <div
            className="segment-link "
          >
            <a>
              Another link
            </a>
            <style />
          </div>
          <div
            className="segment-link "
          >
            <a>
              Yet another link
            </a>
            <style />
          </div>
          <div
            className="segment-link "
          >
            <a>
              Yet another link (more text)
            </a>
            <style />
          </div>
          <div
            className="segment-link view-all"
          >
            <a>
              products.viewAll
            </a>
            <style />
          </div>
        </div>
        <style />
      </div>
      <div
        className="footer-segment"
      >
        <input
          className="section-toggle"
          id="segment-toggle-fb5c02f8e72d"
          type="checkbox"
        />
        <div
          className="segment-title"
        >
          <h3
            className="h4"
          >
            For Mobility Providers
          </h3>
          <label
            className="caret-down"
            htmlFor="segment-toggle-fb5c02f8e72d"
          >
            <span
              className="bp3-icon"
            >
              <div
                height={14}
                src="/v4/svgs/caretDown.svg"
                width="14px"
              />
            </span>
          </label>
        </div>
        <div
          className="segment-links"
        >
          <div
            className="segment-link "
          >
            <a>
              Link 1
            </a>
            <style />
          </div>
          <div
            className="segment-link "
          >
            <a>
              Link 2
            </a>
            <style />
          </div>
          <div
            className="segment-link view-all"
          >
            <a>
              products.viewAll
            </a>
            <style />
          </div>
        </div>
        <style />
      </div>
      <div
        className="footer-segment"
      >
        <input
          className="section-toggle"
          id="segment-toggle-ea63d8588c9b"
          type="checkbox"
        />
        <div
          className="segment-title"
        >
          <h3
            className="h4"
          >
            Hardware & Technology
          </h3>
          <label
            className="caret-down"
            htmlFor="segment-toggle-ea63d8588c9b"
          >
            <span
              className="bp3-icon"
            >
              <div
                height={14}
                src="/v4/svgs/caretDown.svg"
                width="14px"
              />
            </span>
          </label>
        </div>
        <div
          className="segment-links"
        >
          <div
            className="segment-link "
          >
            <a>
              Link 1
            </a>
            <style />
          </div>
          <div
            className="segment-link view-all"
          >
            <a>
              products.viewAll
            </a>
            <style />
          </div>
        </div>
        <style />
      </div>
      <div
        className="footer-segment"
      >
        <input
          className="section-toggle"
          id="segment-toggle-6c6d2f42e158"
          type="checkbox"
        />
        <div
          className="segment-title"
        >
          <h3
            className="h4"
          >
            Company Information
          </h3>
          <label
            className="caret-down"
            htmlFor="segment-toggle-6c6d2f42e158"
          >
            <span
              className="bp3-icon"
            >
              <div
                height={14}
                src="/v4/svgs/caretDown.svg"
                width="14px"
              />
            </span>
          </label>
        </div>
        <div
          className="segment-links"
        >
          <div
            className="segment-link "
          >
            <a>
              Newz
            </a>
            <style />
          </div>
        </div>
        <style />
      </div>
    </div>
    <style />
  </div>
  <hr />
  <div
    className="footer-bottom"
  >
    <div
      className="footer-bottom-element with-top-margin"
    >
      <span
        className="info"
      >
        © 
        2023
         fleetster. 
        All rights reserved
      </span>
    </div>
    <div
      style={
        {
          "display": "flex",
          "flex": 1,
          "marginBottom": 0,
        }
      }
    />
    <div
      className="footer-bottom-element"
    >
      <div
        className="social-media-links with-top-margin"
      >
        <a
          href="https://www.twitter.com"
          rel="noreferrer"
          target="_blank"
          title="Twitter"
        >
          <div
            className="center-content"
            style={
              {
                "height": "100%",
                "width": "100%",
              }
            }
          >
            <img
              alt="Twitter"
              decoding="async"
              height={54}
              loading="lazy"
              sizes="54px"
              src="https://cdn.sanity.io/images/dp11egz7/development/524f8126e1c5510074164b42eeede663ea85243e-54x54.png?w=54&h=54&auto=format"
              srcSet="https://cdn.sanity.io/images/dp11egz7/development/524f8126e1c5510074164b42eeede663ea85243e-54x54.png?w=54&h=54&auto=format 54w"
              style={
                {
                  "height": "auto",
                  "maxHeight": 54,
                  "maxWidth": 54,
                  "width": "100%",
                }
              }
              width={54}
            />
          </div>
        </a>
        <a
          href="https://www.facebook.com"
          rel="noreferrer"
          target="_blank"
          title="Facebook"
        >
          <div
            className="center-content"
            style={
              {
                "height": "100%",
                "width": "100%",
              }
            }
          >
            <img
              alt="Facebook"
              decoding="async"
              height={32}
              loading="lazy"
              sizes="20px"
              src="https://cdn.sanity.io/images/dp11egz7/development/585f44e5d1dc950fefde5c00692d77441604fe7f-20x32.png?w=20&h=32&auto=format"
              srcSet="https://cdn.sanity.io/images/dp11egz7/development/585f44e5d1dc950fefde5c00692d77441604fe7f-20x32.png?w=20&h=32&auto=format 20w"
              style={
                {
                  "height": "auto",
                  "maxHeight": 32,
                  "maxWidth": 20,
                  "width": "100%",
                }
              }
              width={20}
            />
          </div>
        </a>
      </div>
      <div
        className="center-content knowledge-button"
      >
        <div
          className="call-to-action light-theme small"
        >
          <a
            className="center-content light-theme hover-enabled"
            rel="noreferrer"
            target="_blank"
          >
            Fleet Knowledge
            <style />
          </a>
          <style />
        </div>
        <style />
      </div>
    </div>
    <style />
  </div>
  <style />
</footer>
`;

exports[`Footer renders as expected in en 1`] = `
<footer
  className="page-horizontal-padding footer"
>
  <div
    className="footer-top"
  >
    <div
      className="footer-text"
    >
      <h2
        className="footer-logo"
      >
        <div
          className="center-content"
          style={
            {
              "height": "100%",
              "width": "100%",
            }
          }
        >
          <img
            alt="fleetster"
            decoding="async"
            height={50}
            loading="lazy"
            sizes="200px"
            src="https://cdn.sanity.io/images/dp11egz7/development/63c3f345cee6b1704cb6f946f77b4e3117b20d65-200x50.png?w=200&h=50&auto=format"
            srcSet="https://cdn.sanity.io/images/dp11egz7/development/63c3f345cee6b1704cb6f946f77b4e3117b20d65-200x50.png?w=200&h=50&auto=format 200w"
            style={
              {
                "height": "auto",
                "maxHeight": 50,
                "maxWidth": 200,
                "width": "100%",
              }
            }
            width={200}
          />
        </div>
      </h2>
      <p>
        We are a mobility technology company based exclusively in Munich, Germany. We develop software for fleets and mobility providers world wide.
      </p>
      <style />
    </div>
    <div
      className="rf-flex-1 footer-segments"
    >
      <div
        className="footer-segment"
      >
        <input
          className="section-toggle"
          id="segment-toggle-6b6d440dd2f3"
          type="checkbox"
        />
        <div
          className="segment-title"
        >
          <h3
            className="h4"
          >
            For Fleet Managers
          </h3>
          <label
            className="caret-down"
            htmlFor="segment-toggle-6b6d440dd2f3"
          >
            <span
              className="bp3-icon"
            >
              <div
                height={14}
                src="/v4/svgs/caretDown.svg"
                width="14px"
              />
            </span>
          </label>
        </div>
        <div
          className="segment-links"
        >
          <div
            className="segment-link "
          >
            <a>
              Link 1
            </a>
            <style />
          </div>
          <div
            className="segment-link "
          >
            <a>
              Link 2
            </a>
            <style />
          </div>
          <div
            className="segment-link "
          >
            <a>
              Another link
            </a>
            <style />
          </div>
          <div
            className="segment-link "
          >
            <a>
              Yet another link
            </a>
            <style />
          </div>
          <div
            className="segment-link "
          >
            <a>
              Yet another link (more text)
            </a>
            <style />
          </div>
          <div
            className="segment-link view-all"
          >
            <a>
              products.viewAll
            </a>
            <style />
          </div>
        </div>
        <style />
      </div>
      <div
        className="footer-segment"
      >
        <input
          className="section-toggle"
          id="segment-toggle-fb5c02f8e72d"
          type="checkbox"
        />
        <div
          className="segment-title"
        >
          <h3
            className="h4"
          >
            For Mobility Providers
          </h3>
          <label
            className="caret-down"
            htmlFor="segment-toggle-fb5c02f8e72d"
          >
            <span
              className="bp3-icon"
            >
              <div
                height={14}
                src="/v4/svgs/caretDown.svg"
                width="14px"
              />
            </span>
          </label>
        </div>
        <div
          className="segment-links"
        >
          <div
            className="segment-link "
          >
            <a>
              Link 1
            </a>
            <style />
          </div>
          <div
            className="segment-link "
          >
            <a>
              Link 2
            </a>
            <style />
          </div>
          <div
            className="segment-link view-all"
          >
            <a>
              products.viewAll
            </a>
            <style />
          </div>
        </div>
        <style />
      </div>
      <div
        className="footer-segment"
      >
        <input
          className="section-toggle"
          id="segment-toggle-ea63d8588c9b"
          type="checkbox"
        />
        <div
          className="segment-title"
        >
          <h3
            className="h4"
          >
            Hardware & Technology
          </h3>
          <label
            className="caret-down"
            htmlFor="segment-toggle-ea63d8588c9b"
          >
            <span
              className="bp3-icon"
            >
              <div
                height={14}
                src="/v4/svgs/caretDown.svg"
                width="14px"
              />
            </span>
          </label>
        </div>
        <div
          className="segment-links"
        >
          <div
            className="segment-link "
          >
            <a>
              Link 1
            </a>
            <style />
          </div>
          <div
            className="segment-link view-all"
          >
            <a>
              products.viewAll
            </a>
            <style />
          </div>
        </div>
        <style />
      </div>
      <div
        className="footer-segment"
      >
        <input
          className="section-toggle"
          id="segment-toggle-6c6d2f42e158"
          type="checkbox"
        />
        <div
          className="segment-title"
        >
          <h3
            className="h4"
          >
            Company Information
          </h3>
          <label
            className="caret-down"
            htmlFor="segment-toggle-6c6d2f42e158"
          >
            <span
              className="bp3-icon"
            >
              <div
                height={14}
                src="/v4/svgs/caretDown.svg"
                width="14px"
              />
            </span>
          </label>
        </div>
        <div
          className="segment-links"
        >
          <div
            className="segment-link "
          >
            <a>
              Newz
            </a>
            <style />
          </div>
        </div>
        <style />
      </div>
    </div>
    <style />
  </div>
  <hr />
  <div
    className="footer-bottom"
  >
    <div
      className="footer-bottom-element with-top-margin"
    >
      <span
        className="info"
      >
        © 
        2023
         fleetster. 
        All rights reserved
      </span>
    </div>
    <div
      style={
        {
          "display": "flex",
          "flex": 1,
          "marginBottom": 0,
        }
      }
    />
    <div
      className="footer-bottom-element"
    >
      <div
        className="social-media-links with-top-margin"
      >
        <a
          href="https://www.twitter.com"
          rel="noreferrer"
          target="_blank"
          title="Twitter"
        >
          <div
            className="center-content"
            style={
              {
                "height": "100%",
                "width": "100%",
              }
            }
          >
            <img
              alt="Twitter"
              decoding="async"
              height={54}
              loading="lazy"
              sizes="54px"
              src="https://cdn.sanity.io/images/dp11egz7/development/524f8126e1c5510074164b42eeede663ea85243e-54x54.png?w=54&h=54&auto=format"
              srcSet="https://cdn.sanity.io/images/dp11egz7/development/524f8126e1c5510074164b42eeede663ea85243e-54x54.png?w=54&h=54&auto=format 54w"
              style={
                {
                  "height": "auto",
                  "maxHeight": 54,
                  "maxWidth": 54,
                  "width": "100%",
                }
              }
              width={54}
            />
          </div>
        </a>
        <a
          href="https://www.facebook.com"
          rel="noreferrer"
          target="_blank"
          title="Facebook"
        >
          <div
            className="center-content"
            style={
              {
                "height": "100%",
                "width": "100%",
              }
            }
          >
            <img
              alt="Facebook"
              decoding="async"
              height={32}
              loading="lazy"
              sizes="20px"
              src="https://cdn.sanity.io/images/dp11egz7/development/585f44e5d1dc950fefde5c00692d77441604fe7f-20x32.png?w=20&h=32&auto=format"
              srcSet="https://cdn.sanity.io/images/dp11egz7/development/585f44e5d1dc950fefde5c00692d77441604fe7f-20x32.png?w=20&h=32&auto=format 20w"
              style={
                {
                  "height": "auto",
                  "maxHeight": 32,
                  "maxWidth": 20,
                  "width": "100%",
                }
              }
              width={20}
            />
          </div>
        </a>
      </div>
      <div
        className="center-content knowledge-button"
      >
        <div
          className="call-to-action light-theme small"
        >
          <a
            className="center-content light-theme hover-enabled"
            rel="noreferrer"
            target="_blank"
          >
            Fleet Knowledge
            <style />
          </a>
          <style />
        </div>
        <style />
      </div>
    </div>
    <style />
  </div>
  <style />
</footer>
`;

exports[`Footer renders as expected in es 1`] = `
<footer
  className="page-horizontal-padding footer"
>
  <div
    className="footer-top"
  >
    <div
      className="footer-text"
    >
      <h2
        className="footer-logo"
      >
        <div
          className="center-content"
          style={
            {
              "height": "100%",
              "width": "100%",
            }
          }
        >
          <img
            alt="fleetster"
            decoding="async"
            height={50}
            loading="lazy"
            sizes="200px"
            src="https://cdn.sanity.io/images/dp11egz7/development/63c3f345cee6b1704cb6f946f77b4e3117b20d65-200x50.png?w=200&h=50&auto=format"
            srcSet="https://cdn.sanity.io/images/dp11egz7/development/63c3f345cee6b1704cb6f946f77b4e3117b20d65-200x50.png?w=200&h=50&auto=format 200w"
            style={
              {
                "height": "auto",
                "maxHeight": 50,
                "maxWidth": 200,
                "width": "100%",
              }
            }
            width={200}
          />
        </div>
      </h2>
      <p>
        We are a mobility technology company based exclusively in Munich, Germany. We develop software for fleets and mobility providers world wide.
      </p>
      <style />
    </div>
    <div
      className="rf-flex-1 footer-segments"
    >
      <div
        className="footer-segment"
      >
        <input
          className="section-toggle"
          id="segment-toggle-6b6d440dd2f3"
          type="checkbox"
        />
        <div
          className="segment-title"
        >
          <h3
            className="h4"
          >
            For Fleet Managers
          </h3>
          <label
            className="caret-down"
            htmlFor="segment-toggle-6b6d440dd2f3"
          >
            <span
              className="bp3-icon"
            >
              <div
                height={14}
                src="/v4/svgs/caretDown.svg"
                width="14px"
              />
            </span>
          </label>
        </div>
        <div
          className="segment-links"
        >
          <div
            className="segment-link "
          >
            <a>
              Link 1
            </a>
            <style />
          </div>
          <div
            className="segment-link "
          >
            <a>
              Link 2
            </a>
            <style />
          </div>
          <div
            className="segment-link "
          >
            <a>
              Another link
            </a>
            <style />
          </div>
          <div
            className="segment-link "
          >
            <a>
              Yet another link
            </a>
            <style />
          </div>
          <div
            className="segment-link "
          >
            <a>
              Yet another link (more text)
            </a>
            <style />
          </div>
          <div
            className="segment-link view-all"
          >
            <a>
              products.viewAll
            </a>
            <style />
          </div>
        </div>
        <style />
      </div>
      <div
        className="footer-segment"
      >
        <input
          className="section-toggle"
          id="segment-toggle-fb5c02f8e72d"
          type="checkbox"
        />
        <div
          className="segment-title"
        >
          <h3
            className="h4"
          >
            For Mobility Providers
          </h3>
          <label
            className="caret-down"
            htmlFor="segment-toggle-fb5c02f8e72d"
          >
            <span
              className="bp3-icon"
            >
              <div
                height={14}
                src="/v4/svgs/caretDown.svg"
                width="14px"
              />
            </span>
          </label>
        </div>
        <div
          className="segment-links"
        >
          <div
            className="segment-link "
          >
            <a>
              Link 1
            </a>
            <style />
          </div>
          <div
            className="segment-link "
          >
            <a>
              Link 2
            </a>
            <style />
          </div>
          <div
            className="segment-link view-all"
          >
            <a>
              products.viewAll
            </a>
            <style />
          </div>
        </div>
        <style />
      </div>
      <div
        className="footer-segment"
      >
        <input
          className="section-toggle"
          id="segment-toggle-ea63d8588c9b"
          type="checkbox"
        />
        <div
          className="segment-title"
        >
          <h3
            className="h4"
          >
            Hardware & Technology
          </h3>
          <label
            className="caret-down"
            htmlFor="segment-toggle-ea63d8588c9b"
          >
            <span
              className="bp3-icon"
            >
              <div
                height={14}
                src="/v4/svgs/caretDown.svg"
                width="14px"
              />
            </span>
          </label>
        </div>
        <div
          className="segment-links"
        >
          <div
            className="segment-link "
          >
            <a>
              Link 1
            </a>
            <style />
          </div>
          <div
            className="segment-link view-all"
          >
            <a>
              products.viewAll
            </a>
            <style />
          </div>
        </div>
        <style />
      </div>
      <div
        className="footer-segment"
      >
        <input
          className="section-toggle"
          id="segment-toggle-6c6d2f42e158"
          type="checkbox"
        />
        <div
          className="segment-title"
        >
          <h3
            className="h4"
          >
            Company Information
          </h3>
          <label
            className="caret-down"
            htmlFor="segment-toggle-6c6d2f42e158"
          >
            <span
              className="bp3-icon"
            >
              <div
                height={14}
                src="/v4/svgs/caretDown.svg"
                width="14px"
              />
            </span>
          </label>
        </div>
        <div
          className="segment-links"
        >
          <div
            className="segment-link "
          >
            <a>
              Newz
            </a>
            <style />
          </div>
        </div>
        <style />
      </div>
    </div>
    <style />
  </div>
  <hr />
  <div
    className="footer-bottom"
  >
    <div
      className="footer-bottom-element with-top-margin"
    >
      <span
        className="info"
      >
        © 
        2023
         fleetster. 
        All rights reserved
      </span>
    </div>
    <div
      style={
        {
          "display": "flex",
          "flex": 1,
          "marginBottom": 0,
        }
      }
    />
    <div
      className="footer-bottom-element"
    >
      <div
        className="social-media-links with-top-margin"
      >
        <a
          href="https://www.twitter.com"
          rel="noreferrer"
          target="_blank"
          title="Twitter"
        >
          <div
            className="center-content"
            style={
              {
                "height": "100%",
                "width": "100%",
              }
            }
          >
            <img
              alt="Twitter"
              decoding="async"
              height={54}
              loading="lazy"
              sizes="54px"
              src="https://cdn.sanity.io/images/dp11egz7/development/524f8126e1c5510074164b42eeede663ea85243e-54x54.png?w=54&h=54&auto=format"
              srcSet="https://cdn.sanity.io/images/dp11egz7/development/524f8126e1c5510074164b42eeede663ea85243e-54x54.png?w=54&h=54&auto=format 54w"
              style={
                {
                  "height": "auto",
                  "maxHeight": 54,
                  "maxWidth": 54,
                  "width": "100%",
                }
              }
              width={54}
            />
          </div>
        </a>
        <a
          href="https://www.facebook.com"
          rel="noreferrer"
          target="_blank"
          title="Facebook"
        >
          <div
            className="center-content"
            style={
              {
                "height": "100%",
                "width": "100%",
              }
            }
          >
            <img
              alt="Facebook"
              decoding="async"
              height={32}
              loading="lazy"
              sizes="20px"
              src="https://cdn.sanity.io/images/dp11egz7/development/585f44e5d1dc950fefde5c00692d77441604fe7f-20x32.png?w=20&h=32&auto=format"
              srcSet="https://cdn.sanity.io/images/dp11egz7/development/585f44e5d1dc950fefde5c00692d77441604fe7f-20x32.png?w=20&h=32&auto=format 20w"
              style={
                {
                  "height": "auto",
                  "maxHeight": 32,
                  "maxWidth": 20,
                  "width": "100%",
                }
              }
              width={20}
            />
          </div>
        </a>
      </div>
      <div
        className="center-content knowledge-button"
      >
        <div
          className="call-to-action light-theme small"
        >
          <a
            className="center-content light-theme hover-enabled"
            rel="noreferrer"
            target="_blank"
          >
            Fleet Knowledge
            <style />
          </a>
          <style />
        </div>
        <style />
      </div>
    </div>
    <style />
  </div>
  <style />
</footer>
`;

exports[`Footer renders as expected in nl 1`] = `
<footer
  className="page-horizontal-padding footer"
>
  <div
    className="footer-top"
  >
    <div
      className="footer-text"
    >
      <h2
        className="footer-logo"
      >
        <div
          className="center-content"
          style={
            {
              "height": "100%",
              "width": "100%",
            }
          }
        >
          <img
            alt="fleetster"
            decoding="async"
            height={50}
            loading="lazy"
            sizes="200px"
            src="https://cdn.sanity.io/images/dp11egz7/development/63c3f345cee6b1704cb6f946f77b4e3117b20d65-200x50.png?w=200&h=50&auto=format"
            srcSet="https://cdn.sanity.io/images/dp11egz7/development/63c3f345cee6b1704cb6f946f77b4e3117b20d65-200x50.png?w=200&h=50&auto=format 200w"
            style={
              {
                "height": "auto",
                "maxHeight": 50,
                "maxWidth": 200,
                "width": "100%",
              }
            }
            width={200}
          />
        </div>
      </h2>
      <p>
        We are a mobility technology company based exclusively in Munich, Germany. We develop software for fleets and mobility providers world wide.
      </p>
      <style />
    </div>
    <div
      className="rf-flex-1 footer-segments"
    >
      <div
        className="footer-segment"
      >
        <input
          className="section-toggle"
          id="segment-toggle-6b6d440dd2f3"
          type="checkbox"
        />
        <div
          className="segment-title"
        >
          <h3
            className="h4"
          >
            For Fleet Managers
          </h3>
          <label
            className="caret-down"
            htmlFor="segment-toggle-6b6d440dd2f3"
          >
            <span
              className="bp3-icon"
            >
              <div
                height={14}
                src="/v4/svgs/caretDown.svg"
                width="14px"
              />
            </span>
          </label>
        </div>
        <div
          className="segment-links"
        >
          <div
            className="segment-link "
          >
            <a>
              Link 1
            </a>
            <style />
          </div>
          <div
            className="segment-link "
          >
            <a>
              Link 2
            </a>
            <style />
          </div>
          <div
            className="segment-link "
          >
            <a>
              Another link
            </a>
            <style />
          </div>
          <div
            className="segment-link "
          >
            <a>
              Yet another link
            </a>
            <style />
          </div>
          <div
            className="segment-link "
          >
            <a>
              Yet another link (more text)
            </a>
            <style />
          </div>
          <div
            className="segment-link view-all"
          >
            <a>
              products.viewAll
            </a>
            <style />
          </div>
        </div>
        <style />
      </div>
      <div
        className="footer-segment"
      >
        <input
          className="section-toggle"
          id="segment-toggle-fb5c02f8e72d"
          type="checkbox"
        />
        <div
          className="segment-title"
        >
          <h3
            className="h4"
          >
            For Mobility Providers
          </h3>
          <label
            className="caret-down"
            htmlFor="segment-toggle-fb5c02f8e72d"
          >
            <span
              className="bp3-icon"
            >
              <div
                height={14}
                src="/v4/svgs/caretDown.svg"
                width="14px"
              />
            </span>
          </label>
        </div>
        <div
          className="segment-links"
        >
          <div
            className="segment-link "
          >
            <a>
              Link 1
            </a>
            <style />
          </div>
          <div
            className="segment-link "
          >
            <a>
              Link 2
            </a>
            <style />
          </div>
          <div
            className="segment-link view-all"
          >
            <a>
              products.viewAll
            </a>
            <style />
          </div>
        </div>
        <style />
      </div>
      <div
        className="footer-segment"
      >
        <input
          className="section-toggle"
          id="segment-toggle-ea63d8588c9b"
          type="checkbox"
        />
        <div
          className="segment-title"
        >
          <h3
            className="h4"
          >
            Hardware & Technology
          </h3>
          <label
            className="caret-down"
            htmlFor="segment-toggle-ea63d8588c9b"
          >
            <span
              className="bp3-icon"
            >
              <div
                height={14}
                src="/v4/svgs/caretDown.svg"
                width="14px"
              />
            </span>
          </label>
        </div>
        <div
          className="segment-links"
        >
          <div
            className="segment-link "
          >
            <a>
              Link 1
            </a>
            <style />
          </div>
          <div
            className="segment-link view-all"
          >
            <a>
              products.viewAll
            </a>
            <style />
          </div>
        </div>
        <style />
      </div>
      <div
        className="footer-segment"
      >
        <input
          className="section-toggle"
          id="segment-toggle-6c6d2f42e158"
          type="checkbox"
        />
        <div
          className="segment-title"
        >
          <h3
            className="h4"
          >
            Company Information
          </h3>
          <label
            className="caret-down"
            htmlFor="segment-toggle-6c6d2f42e158"
          >
            <span
              className="bp3-icon"
            >
              <div
                height={14}
                src="/v4/svgs/caretDown.svg"
                width="14px"
              />
            </span>
          </label>
        </div>
        <div
          className="segment-links"
        >
          <div
            className="segment-link "
          >
            <a>
              Newz
            </a>
            <style />
          </div>
        </div>
        <style />
      </div>
    </div>
    <style />
  </div>
  <hr />
  <div
    className="footer-bottom"
  >
    <div
      className="footer-bottom-element with-top-margin"
    >
      <span
        className="info"
      >
        © 
        2023
         fleetster. 
        All rights reserved
      </span>
    </div>
    <div
      style={
        {
          "display": "flex",
          "flex": 1,
          "marginBottom": 0,
        }
      }
    />
    <div
      className="footer-bottom-element"
    >
      <div
        className="social-media-links with-top-margin"
      >
        <a
          href="https://www.twitter.com"
          rel="noreferrer"
          target="_blank"
          title="Twitter"
        >
          <div
            className="center-content"
            style={
              {
                "height": "100%",
                "width": "100%",
              }
            }
          >
            <img
              alt="Twitter"
              decoding="async"
              height={54}
              loading="lazy"
              sizes="54px"
              src="https://cdn.sanity.io/images/dp11egz7/development/524f8126e1c5510074164b42eeede663ea85243e-54x54.png?w=54&h=54&auto=format"
              srcSet="https://cdn.sanity.io/images/dp11egz7/development/524f8126e1c5510074164b42eeede663ea85243e-54x54.png?w=54&h=54&auto=format 54w"
              style={
                {
                  "height": "auto",
                  "maxHeight": 54,
                  "maxWidth": 54,
                  "width": "100%",
                }
              }
              width={54}
            />
          </div>
        </a>
        <a
          href="https://www.facebook.com"
          rel="noreferrer"
          target="_blank"
          title="Facebook"
        >
          <div
            className="center-content"
            style={
              {
                "height": "100%",
                "width": "100%",
              }
            }
          >
            <img
              alt="Facebook"
              decoding="async"
              height={32}
              loading="lazy"
              sizes="20px"
              src="https://cdn.sanity.io/images/dp11egz7/development/585f44e5d1dc950fefde5c00692d77441604fe7f-20x32.png?w=20&h=32&auto=format"
              srcSet="https://cdn.sanity.io/images/dp11egz7/development/585f44e5d1dc950fefde5c00692d77441604fe7f-20x32.png?w=20&h=32&auto=format 20w"
              style={
                {
                  "height": "auto",
                  "maxHeight": 32,
                  "maxWidth": 20,
                  "width": "100%",
                }
              }
              width={20}
            />
          </div>
        </a>
      </div>
      <div
        className="center-content knowledge-button"
      >
        <div
          className="call-to-action light-theme small"
        >
          <a
            className="center-content light-theme hover-enabled"
            rel="noreferrer"
            target="_blank"
          >
            Fleet Knowledge
            <style />
          </a>
          <style />
        </div>
        <style />
      </div>
    </div>
    <style />
  </div>
  <style />
</footer>
`;

exports[`Footer renders as expected in notExisting 1`] = `
<footer
  className="page-horizontal-padding footer"
>
  <div
    className="footer-top"
  >
    <div
      className="footer-text"
    >
      <h2
        className="footer-logo"
      >
        <div
          className="center-content"
          style={
            {
              "height": "100%",
              "width": "100%",
            }
          }
        >
          <img
            alt="fleetster"
            decoding="async"
            height={50}
            loading="lazy"
            sizes="200px"
            src="https://cdn.sanity.io/images/dp11egz7/development/63c3f345cee6b1704cb6f946f77b4e3117b20d65-200x50.png?w=200&h=50&auto=format"
            srcSet="https://cdn.sanity.io/images/dp11egz7/development/63c3f345cee6b1704cb6f946f77b4e3117b20d65-200x50.png?w=200&h=50&auto=format 200w"
            style={
              {
                "height": "auto",
                "maxHeight": 50,
                "maxWidth": 200,
                "width": "100%",
              }
            }
            width={200}
          />
        </div>
      </h2>
      <p />
      <style />
    </div>
    <div
      className="rf-flex-1 footer-segments"
    />
    <style />
  </div>
  <hr />
  <div
    className="footer-bottom"
  >
    <div
      className="footer-bottom-element with-top-margin"
    >
      <span
        className="info"
      >
        © 
        2023
         fleetster. 
      </span>
    </div>
    <div
      style={
        {
          "display": "flex",
          "flex": 1,
          "marginBottom": 0,
        }
      }
    />
    <div
      className="footer-bottom-element"
    >
      <div
        className="social-media-links with-top-margin"
      >
        <a
          href="https://www.twitter.com"
          rel="noreferrer"
          target="_blank"
          title="Twitter"
        >
          <div
            className="center-content"
            style={
              {
                "height": "100%",
                "width": "100%",
              }
            }
          >
            <img
              alt="Twitter"
              decoding="async"
              height={54}
              loading="lazy"
              sizes="54px"
              src="https://cdn.sanity.io/images/dp11egz7/development/524f8126e1c5510074164b42eeede663ea85243e-54x54.png?w=54&h=54&auto=format"
              srcSet="https://cdn.sanity.io/images/dp11egz7/development/524f8126e1c5510074164b42eeede663ea85243e-54x54.png?w=54&h=54&auto=format 54w"
              style={
                {
                  "height": "auto",
                  "maxHeight": 54,
                  "maxWidth": 54,
                  "width": "100%",
                }
              }
              width={54}
            />
          </div>
        </a>
        <a
          href="https://www.facebook.com"
          rel="noreferrer"
          target="_blank"
          title="Facebook"
        >
          <div
            className="center-content"
            style={
              {
                "height": "100%",
                "width": "100%",
              }
            }
          >
            <img
              alt="Facebook"
              decoding="async"
              height={32}
              loading="lazy"
              sizes="20px"
              src="https://cdn.sanity.io/images/dp11egz7/development/585f44e5d1dc950fefde5c00692d77441604fe7f-20x32.png?w=20&h=32&auto=format"
              srcSet="https://cdn.sanity.io/images/dp11egz7/development/585f44e5d1dc950fefde5c00692d77441604fe7f-20x32.png?w=20&h=32&auto=format 20w"
              style={
                {
                  "height": "auto",
                  "maxHeight": 32,
                  "maxWidth": 20,
                  "width": "100%",
                }
              }
              width={20}
            />
          </div>
        </a>
      </div>
    </div>
    <style />
  </div>
  <style />
</footer>
`;
