import React from 'react';
import css from 'styled-jsx/css';
import _ from 'lodash';

import {i18n, language} from 'rf-data/siteConfig.json';
import {vSpacing} from 'rf-styles';
import {CallToAction} from 'rf-components';
import {removeEndSlash} from 'rf-utils';

const styles = css`
  .knowledge-button {
    margin-top: ${vSpacing.s}px;
  }
}`;

export function KnowledgeButton() {
    const {text, route, url} = _.get(i18n, [language, 'footer', 'knowledgeLink'], {});

    if (!text) {
        return null;
    }

    const href = _.get(route, 'href', removeEndSlash(url));

    return (
        <div className={'center-content knowledge-button'}>
            <CallToAction small lightTheme title={text} route={{href}} enableHover/>
            <style jsx>{styles}</style>
        </div>
    );
}

