import _ from 'lodash';

import {injectTagManagerScripts} from '../tagManagerScripts';

describe('tagManagerScripts', () => {
    beforeEach(() => {
        document.body.innerHTML = '';
        localStorage.clear();
        delete window.dataLayer;
    });

    test('injects tag manager scripts', () => {
        injectTagManagerScripts();

        expect(document.body.innerHTML).toMatchSnapshot();
    });

    test('injects different script in production', () => {
        process.env.USE_PROD_GTM = 'true';

        injectTagManagerScripts();

        expect(document.body.innerHTML).toMatchSnapshot();

        process.env.USE_PROD_GTM = 'false';
    });

    test('if window is undefined, scripts are not injected', () => {
        const oldWindow = global.window;
        _.unset(global, 'window');

        injectTagManagerScripts();

        expect(document.body.innerHTML).toBe('');

        _.set(global, 'window', oldWindow);
    });
});
