const gtmConfigUrl = 'https://www.googletagmanager.com/ns.html?id=GTM-5KKL79V';
let gtmScript = `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl+ '&gtm_auth=brolm_wR4mKxAYy642Uv2A&gtm_preview=env-355&gtm_cookies_win=x';f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-5KKL79V');`;

if (process.env.USE_PROD_GTM !== 'true') {
    gtmScript = `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-5KKL79V');`;
}

const tagManagerScripts = [
    {
        type:  'script',
        props: {
            innerHTML: gtmScript,
            key:       'gtm'
        }
    },
    {
        type:  'noscript',
        props: {
            innerHTML: `<iframe src="${gtmConfigUrl}" height="0" width="0" style="display: none; visibility: hidden;"/>`
        }
    }
];

export function injectTagManagerScripts() {
    if (typeof window === 'undefined') {
        return;
    }

    tagManagerScripts.forEach(script => {
        const scriptElement = document.createElement(script.type);

        Object.keys(script.props).forEach(key => {
            scriptElement[key] = script.props[key];
        });

        document.body.appendChild(scriptElement);
    });
}
