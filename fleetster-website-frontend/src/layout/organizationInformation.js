import React from 'react';
import Head from 'next/head';
import PropTypes from 'prop-types';

import {getImageUrl} from 'rf-data';

export function OrganizationInformation({host, logo}) {
    if (!host || !logo) { return null; }

    const logoUrl = getImageUrl(logo);

    const organizationStructuredData = {
        '@context':   'https://schema.org',
        '@type':      'Organization',
        name:         'fleetster | Next Generation Mobility GmbH',
        url:          host?.url,
        logo:         logoUrl,
        contactPoint: [{
            '@type':           'ContactPoint',
            telephone:         '+49-89125010212',
            availableLanguage: ['English', 'German', 'Spanish'],
            contactType:       'customer service'
        }],
        sameAs: [
            'https://www.facebook.com/corporatecarsharing/',
            'https://twitter.com/fleetster_cc',
            'https://vimeo.com/fleetster',
            'https://www.youtube.com/user/NGMmuenchen/videos',
            'https://www.linkedin.com/company/next-generation-mobility-gmbh-&-co--kg',
            'https://www.xing.com/pages/fleetster-nextgenerationmobilitygmbh-co-kgmunich'
        ]
    };

    const structureDataHtml = {
        __html: JSON.stringify(organizationStructuredData)
    };

    return (
        <Head>
            <script dangerouslySetInnerHTML={structureDataHtml} type={'application/ld+json'}/>
        </Head>
    );
}

OrganizationInformation.propTypes = {
    host: PropTypes.object,
    logo: PropTypes.object
};
