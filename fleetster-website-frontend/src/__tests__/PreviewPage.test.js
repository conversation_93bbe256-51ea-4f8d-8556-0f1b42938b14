import React from 'react';
import {fetchDocuments, getDocument} from 'sanity-client-adapter';

import mockRoutes from '../../__mocks__/routes.json';
import mockImages from '../../__mocks__/images.json';

import PreviewPage, {getStaticProps, getStaticPaths} from '../pages/preview/[id]';

describe('PreviewPage', () => {
    beforeAll(() => {
        jest.spyOn(global.Date.prototype, 'getFullYear').mockImplementation(() => 2023);
    });

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('getStaticProps', () => {
        it('returns route null when no pageId is provided', async() => {
            const {getStaticProps} = require('../pages/preview/[id]');
            const {props, revalidate} = await getStaticProps({params: {}});

            expect(props.route).toBeNull();
            expect(revalidate).toBe(undefined);
        });

        it('returns route null when no data is found for the provide pageId', async() => {
            getDocument.mockImplementation(() => null);

            const {props, revalidate} = await getStaticProps({params: {id: 'somePageId'}});

            expect(props.route).toBeNull();
            expect(revalidate).toBe(1);
            expect(getDocument).toHaveBeenCalledWith('somePageId');
        });

        it('render route data correctly when it is found for the provided pageId', async() => {
            getDocument.mockImplementation(() => ({
                ...Object.values(mockRoutes)[0].i18n.de.page,
                content: Object.values(mockRoutes)[0].i18n.de.page.i18n.de.content
            }));
            fetchDocuments.mockImplementation(() => mockImages);

            const {props, revalidate} = await getStaticProps({params: {id: 'somePageId'}});

            const component = render(<PreviewPage route={props.route}/>);

            expect(revalidate).toBe(1);
            expect(component).toMatchSnapshot();
        });

        it('renders correctly when no route data is provided', () => {
            const component = render(<PreviewPage route={null}/>);

            expect(component).toMatchSnapshot();
        });

        it('returns empty paths and blocking fallback', () => {
            const {paths, fallback} = getStaticPaths();

            expect(paths).toEqual([]);
            expect(fallback).toEqual('blocking');
        });
    });
});
