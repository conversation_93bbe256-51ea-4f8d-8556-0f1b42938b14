import React from 'react';
import Page, {getStaticPaths, getStaticProps} from '../pages/[[...slug]]';

describe('Page', () => {
    beforeAll(() => {
        jest.spyOn(global.Date.prototype, 'getFullYear').mockImplementation(() => 2023);
    });

    test('renders index page correctly', () => {
        const {props} = getStaticProps({params: {}});
        const component = render(<Page {...props} />);

        expect(component).toMatchSnapshot();
    });

    test('renders regular page correctly', () => {
        const {props} = getStaticProps({params: {slug: ['v4', 'corsoroute']}});
        const component = render(<Page {...props} />);

        expect(component).toMatchSnapshot();
    });

    test('getStaticPaths combines all paths correctly', () => {
        const {paths} = getStaticPaths();

        expect(paths).toMatchSnapshot();
    });

    test('getStaticProps returns news page props for news slug', () => {
        const {props} = getStaticProps({params: {slug: ['neuigkeiten', '2021']}});

        expect(props).toHaveProperty('isNewsPage', true);
    });

    test('getStaticProps returns changelog page props for changelog slug', () => {
        const {props} = getStaticProps({params: {slug: ['produkt-updates', 'q4-2021']}});

        expect(props).toHaveProperty('isChangelogPage', true);
    });

    test('renders news page correctly', () => {
        const {props} = getStaticProps({params: {slug: ['neuigkeiten', '2020']}});
        const component = render(<Page {...props} />);

        expect(component).toMatchSnapshot();
    });

    test('renders changelog page correctly', () => {
        const {props} = getStaticProps({params: {slug: ['produkt-updates', 'q4-2021']}});
        const component = render(<Page {...props} />);

        expect(component).toMatchSnapshot();
    });
});
