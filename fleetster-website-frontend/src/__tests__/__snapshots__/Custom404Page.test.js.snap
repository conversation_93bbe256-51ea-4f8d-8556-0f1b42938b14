// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Custom404Page renders correctly 1`] = `
[
  <div
    className="page-container"
  >
    <header
      className="header-component"
    >
      <div
        className="page-horizontal-padding page-horizontal-padding center-content header-container"
      >
        <div
          className="side-navigation"
        >
          <div
            className="sider-button"
          >
            <label
              htmlFor="sider-toggle"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={20}
                  src="/v4/svgs/hamburguerList.svg"
                  width="20px"
                />
              </span>
            </label>
            <input
              id="sider-toggle"
              type="checkbox"
            />
            <div
              className="sider-container"
            >
              <label
                htmlFor="sider-toggle"
              />
              <div
                className="sider"
              >
                <label
                  className="toggle-container"
                  htmlFor="sider-toggle"
                >
                  <span
                    className="bp3-icon"
                  >
                    <div
                      height={20}
                      src="/v4/svgs/hamburguerList.svg"
                      width="20px"
                    />
                  </span>
                </label>
                <div>
                  <div
                    className="dropdown-trigger"
                    onClick={[Function]}
                  >
                    <span
                      className="bp3-icon"
                    >
                      <div
                        height={18}
                        src="/v4/svgs/vehicle.svg"
                        width="18px"
                      />
                    </span>
                    <p
                      className="h5"
                    >
                      Products
                    </p>
                    <span
                      className="bp3-icon"
                    >
                      <div
                        height={14}
                        src="/v4/svgs/caretDown.svg"
                        width="14px"
                      />
                    </span>
                  </div>
                  <style />
                </div>
                <a
                  className="nav-link"
                  rel="noreferrer noopener"
                  target="_blank"
                >
                  <div
                    className="icon"
                  >
                    <div
                      className="center-content"
                      style={
                        {
                          "height": "100%",
                          "width": "100%",
                        }
                      }
                    >
                      <img
                        alt="Lejla test icon"
                        decoding="async"
                        height={449}
                        loading="lazy"
                        sizes="(max-width: 362px) 361px, 465px"
                        src="https://cdn.sanity.io/images/dp11egz7/development/d58fe928015b2c9d27c175e3163e952d81f4530c-465x449.png?w=465&h=449&auto=format"
                        srcSet="https://cdn.sanity.io/images/dp11egz7/development/d58fe928015b2c9d27c175e3163e952d81f4530c-465x449.png?rect=1,0,464,449&w=361&h=349&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/d58fe928015b2c9d27c175e3163e952d81f4530c-465x449.png?w=465&h=449&auto=format 465w"
                        style={
                          {
                            "height": "auto",
                            "maxHeight": 449,
                            "maxWidth": 465,
                            "width": "100%",
                          }
                        }
                        width={465}
                      />
                    </div>
                  </div>
                  <span
                    className="h5"
                  >
                    Lejla test
                  </span>
                  <style />
                </a>
                <style />
                <a
                  className="nav-link"
                  href="https://www.google.com"
                  rel="noreferrer noopener"
                  target="_blank"
                >
                  <div
                    className="icon"
                  >
                    <div
                      className="center-content"
                      style={
                        {
                          "height": "100%",
                          "width": "100%",
                        }
                      }
                    >
                      <img
                        alt="Google icon"
                        decoding="async"
                        height={224}
                        loading="lazy"
                        sizes="228px"
                        src="https://cdn.sanity.io/images/dp11egz7/development/e18ed6815845cfa79edc623d7e7f6ef6c31dc3bb-228x224.png?w=228&h=224&auto=format"
                        srcSet="https://cdn.sanity.io/images/dp11egz7/development/e18ed6815845cfa79edc623d7e7f6ef6c31dc3bb-228x224.png?w=228&h=224&auto=format 228w"
                        style={
                          {
                            "height": "auto",
                            "maxHeight": 224,
                            "maxWidth": 228,
                            "width": "100%",
                          }
                        }
                        width={228}
                      />
                    </div>
                  </div>
                  <span
                    className="h5"
                  >
                    Google
                  </span>
                  <style />
                </a>
                <style />
                <a
                  aria-label="Contact us"
                  className="nav-link"
                >
                  <span
                    className="bp3-icon"
                  >
                    <div
                      height={18}
                      src="/v4/svgs/mail.svg"
                      width="18px"
                    />
                  </span>
                  <span
                    className="h5"
                  >
                    Contact us
                  </span>
                </a>
                <style />
                <div
                  style={
                    {
                      "flex": 100,
                    }
                  }
                />
                <div
                  className="bottom-buttons"
                >
                  <a
                    className="center-content"
                  >
                    <span
                      className="bp3-icon"
                    >
                      <div
                        height={22}
                        src="/v4/svgs/whiteLabel.svg"
                        width="22px"
                      />
                    </span>
                    Demo
                  </a>
                  <style />
                  <a
                    className="center-content"
                    href="https://my.fleetster.net"
                  >
                    <span
                      className="bp3-icon"
                    >
                      <div
                        height={20}
                        src="/v4/svgs/loginRound.svg"
                        width="20px"
                      />
                    </span>
                    Login
                  </a>
                  <style />
                </div>
                <style />
              </div>
            </div>
          </div>
          <style />
        </div>
        <a
          className="logo"
          href="http://localhost:3000"
        >
          <div
            className="center-content"
            style={
              {
                "height": "100%",
                "width": "100%",
              }
            }
          >
            <img
              alt="fleetster"
              decoding="async"
              height={30}
              loading="lazy"
              sizes="170px"
              src="https://cdn.sanity.io/images/dp11egz7/development/f834f95f9016aa4c4419ac82f3ffe81509d7a115-1990x493.png?rect=0,72,1990,351&w=170&h=30&auto=format"
              srcSet="https://cdn.sanity.io/images/dp11egz7/development/f834f95f9016aa4c4419ac82f3ffe81509d7a115-1990x493.png?rect=0,72,1990,351&w=170&h=30&auto=format 170w"
              style={
                {
                  "height": "auto",
                  "maxHeight": 30,
                  "maxWidth": 170,
                  "width": "100%",
                }
              }
              width={170}
            />
          </div>
          <style />
        </a>
        <div
          className="center-content top-navigation"
        >
          <div
            className="rf-cell center-content dropdown"
            tabIndex={0}
          >
            <span
              className="bp3-icon"
            >
              <div
                height={18}
                src="/v4/svgs/vehicle.svg"
                width="18px"
              />
            </span>
            <p
              className="h5"
            >
              Products
            </p>
            <span
              className="bp3-icon"
            >
              <div
                height={14}
                src="/v4/svgs/caretDown.svg"
                width="14px"
              />
            </span>
            <div
              className="dropdown-content"
              tabIndex={0}
            >
              <a
                className="nav-link"
                rel="noreferrer noopener"
                target="_blank"
              >
                <div
                  className="icon"
                >
                  <div
                    className="center-content"
                    style={
                      {
                        "height": "100%",
                        "width": "100%",
                      }
                    }
                  >
                    <img
                      alt="undefined icon"
                      decoding="async"
                      height={450}
                      loading="lazy"
                      sizes="(max-width: 362px) 361px, 450px"
                      src="https://cdn.sanity.io/images/dp11egz7/development/4119c893040916f82e6d1304b78dca28eb231aee-450x450.png?w=450&h=450&auto=format"
                      srcSet="https://cdn.sanity.io/images/dp11egz7/development/4119c893040916f82e6d1304b78dca28eb231aee-450x450.png?w=361&h=361&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/4119c893040916f82e6d1304b78dca28eb231aee-450x450.png?w=450&h=450&auto=format 450w"
                      style={
                        {
                          "height": "auto",
                          "maxHeight": 450,
                          "maxWidth": 450,
                          "width": "100%",
                        }
                      }
                      width={450}
                    />
                  </div>
                </div>
                <div>
                  <span
                    className="h5"
                  >
                    Testing
                  </span>
                  <p />
                </div>
                <style />
              </a>
              <style />
              <a
                className="nav-link"
                rel="noreferrer noopener"
                target="_blank"
              >
                <div
                  className="icon"
                >
                  <div
                    className="center-content"
                    style={
                      {
                        "height": "100%",
                        "width": "100%",
                      }
                    }
                  >
                    <img
                      alt="undefined icon"
                      decoding="async"
                      height={669}
                      loading="lazy"
                      sizes="(max-width: 362px) 361px, 604px"
                      src="https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?w=604&h=669&auto=format"
                      srcSet="https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?w=361&h=400&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?w=604&h=669&auto=format 604w"
                      style={
                        {
                          "height": "auto",
                          "maxHeight": 669,
                          "maxWidth": 604,
                          "width": "100%",
                        }
                      }
                      width={604}
                    />
                  </div>
                </div>
                <div>
                  <span
                    className="h5"
                  >
                    This is an even bigger text, let's see how this one behaves. Hope it works.
                  </span>
                  <p />
                </div>
                <style />
              </a>
              <style />
              <a
                className="nav-link"
                href="/ho-ho-ho"
                rel="noreferrer noopener"
                target="_blank"
              >
                <div
                  className="icon"
                >
                  <div
                    className="center-content"
                    style={
                      {
                        "height": "100%",
                        "width": "100%",
                      }
                    }
                  >
                    <img
                      alt="undefined icon"
                      decoding="async"
                      height={669}
                      loading="lazy"
                      sizes="(max-width: 362px) 361px, 604px"
                      src="https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?w=604&h=669&auto=format"
                      srcSet="https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?w=361&h=400&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?w=604&h=669&auto=format 604w"
                      style={
                        {
                          "height": "auto",
                          "maxHeight": 669,
                          "maxWidth": 604,
                          "width": "100%",
                        }
                      }
                      width={604}
                    />
                  </div>
                </div>
                <div>
                  <span
                    className="h5"
                  >
                    meh
                  </span>
                  <p />
                </div>
                <style />
              </a>
              <style />
              <a
                className="h5 see-all"
              >
                menu.seeAll
              </a>
            </div>
            <style />
          </div>
          <a
            className="rf-cell center-content nav-link"
            rel="noreferrer noopener"
            target="_blank"
          >
            <div
              style={
                {
                  "minHeight": 18,
                  "minWidth": 18,
                }
              }
            >
              <div
                className="center-content"
                style={
                  {
                    "height": "100%",
                    "width": "100%",
                  }
                }
              >
                <img
                  alt="Lejla test icon"
                  decoding="async"
                  height={18}
                  loading="lazy"
                  sizes="18px"
                  src="https://cdn.sanity.io/images/dp11egz7/development/d58fe928015b2c9d27c175e3163e952d81f4530c-465x449.png?rect=9,0,449,449&w=18&h=18&auto=format"
                  srcSet="https://cdn.sanity.io/images/dp11egz7/development/d58fe928015b2c9d27c175e3163e952d81f4530c-465x449.png?rect=9,0,449,449&w=18&h=18&auto=format 18w"
                  style={
                    {
                      "height": "auto",
                      "maxHeight": 18,
                      "maxWidth": 18,
                      "width": "100%",
                    }
                  }
                  width={18}
                />
              </div>
            </div>
            <span
              className="h5"
            >
              Lejla test
            </span>
            <style />
          </a>
          <style />
          <a
            className="rf-cell center-content nav-link"
            href="https://www.google.com"
            rel="noreferrer noopener"
            target="_blank"
          >
            <div
              style={
                {
                  "minHeight": 18,
                  "minWidth": 18,
                }
              }
            >
              <div
                className="center-content"
                style={
                  {
                    "height": "100%",
                    "width": "100%",
                  }
                }
              >
                <img
                  alt="Google icon"
                  decoding="async"
                  height={18}
                  loading="lazy"
                  sizes="18px"
                  src="https://cdn.sanity.io/images/dp11egz7/development/e18ed6815845cfa79edc623d7e7f6ef6c31dc3bb-228x224.png?rect=2,0,224,224&w=18&h=18&auto=format"
                  srcSet="https://cdn.sanity.io/images/dp11egz7/development/e18ed6815845cfa79edc623d7e7f6ef6c31dc3bb-228x224.png?rect=2,0,224,224&w=18&h=18&auto=format 18w"
                  style={
                    {
                      "height": "auto",
                      "maxHeight": 18,
                      "maxWidth": 18,
                      "width": "100%",
                    }
                  }
                  width={18}
                />
              </div>
            </div>
            <span
              className="h5"
            >
              Google
            </span>
            <style />
          </a>
          <style />
          <style />
        </div>
        <div
          className="rf-flex-2"
        />
        <div
          className="center-content right-menu"
        >
          <div
            className="icon-padding center-content"
            title="Contact us"
          >
            <a
              aria-label="Contact us"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={22}
                  src="/v4/svgs/mail.svg"
                  width="22px"
                />
              </span>
            </a>
            <style />
          </div>
          <div
            className="rf-cell right-buttons center-content"
          >
            <a
              className="center-content"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={22}
                  src="/v4/svgs/whiteLabel.svg"
                  width="22px"
                />
              </span>
              Demo
            </a>
            <style />
            <a
              className="center-content"
              href="https://my.fleetster.net"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={20}
                  src="/v4/svgs/loginRound.svg"
                  width="20px"
                />
              </span>
              Login
            </a>
            <style />
          </div>
        </div>
      </div>
      <style />
    </header>
    <main
      className="main-content"
    >
      <div
        className="rf-flex-container"
        style={
          {
            "height": "400px",
          }
        }
      >
        <div
          style={
            {
              "flex": 1,
            }
          }
        />
        <div
          className="rf-cell center-content"
        >
          <h1>
            seo.title.notFound
          </h1>
        </div>
        <div
          style={
            {
              "flex": 1,
            }
          }
        />
      </div>
    </main>
    <footer
      className="page-horizontal-padding footer"
    >
      <div
        className="footer-top"
      >
        <div
          className="footer-text"
        >
          <h2
            className="footer-logo"
          >
            <div
              className="center-content"
              style={
                {
                  "height": "100%",
                  "width": "100%",
                }
              }
            >
              <img
                alt="fleetster"
                decoding="async"
                height={50}
                loading="lazy"
                sizes="200px"
                src="https://cdn.sanity.io/images/dp11egz7/development/63c3f345cee6b1704cb6f946f77b4e3117b20d65-200x50.png?w=200&h=50&auto=format"
                srcSet="https://cdn.sanity.io/images/dp11egz7/development/63c3f345cee6b1704cb6f946f77b4e3117b20d65-200x50.png?w=200&h=50&auto=format 200w"
                style={
                  {
                    "height": "auto",
                    "maxHeight": 50,
                    "maxWidth": 200,
                    "width": "100%",
                  }
                }
                width={200}
              />
            </div>
          </h2>
          <p>
            We are a mobility technology company based exclusively in Munich, Germany. We develop software for fleets and mobility providers world wide.
          </p>
          <style />
        </div>
        <div
          className="rf-flex-1 footer-segments"
        >
          <div
            className="footer-segment"
          >
            <input
              className="section-toggle"
              id="segment-toggle-6b6d440dd2f3"
              type="checkbox"
            />
            <div
              className="segment-title"
            >
              <h3
                className="h4"
              >
                For Fleet Managers
              </h3>
              <label
                className="caret-down"
                htmlFor="segment-toggle-6b6d440dd2f3"
              >
                <span
                  className="bp3-icon"
                >
                  <div
                    height={14}
                    src="/v4/svgs/caretDown.svg"
                    width="14px"
                  />
                </span>
              </label>
            </div>
            <div
              className="segment-links"
            >
              <div
                className="segment-link "
              >
                <a>
                  Link 1
                </a>
                <style />
              </div>
              <div
                className="segment-link "
              >
                <a>
                  Link 2
                </a>
                <style />
              </div>
              <div
                className="segment-link "
              >
                <a>
                  Another link
                </a>
                <style />
              </div>
              <div
                className="segment-link "
              >
                <a>
                  Yet another link
                </a>
                <style />
              </div>
              <div
                className="segment-link "
              >
                <a>
                  Yet another link (more text)
                </a>
                <style />
              </div>
              <div
                className="segment-link view-all"
              >
                <a>
                  products.viewAll
                </a>
                <style />
              </div>
            </div>
            <style />
          </div>
          <div
            className="footer-segment"
          >
            <input
              className="section-toggle"
              id="segment-toggle-fb5c02f8e72d"
              type="checkbox"
            />
            <div
              className="segment-title"
            >
              <h3
                className="h4"
              >
                For Mobility Providers
              </h3>
              <label
                className="caret-down"
                htmlFor="segment-toggle-fb5c02f8e72d"
              >
                <span
                  className="bp3-icon"
                >
                  <div
                    height={14}
                    src="/v4/svgs/caretDown.svg"
                    width="14px"
                  />
                </span>
              </label>
            </div>
            <div
              className="segment-links"
            >
              <div
                className="segment-link "
              >
                <a>
                  Link 1
                </a>
                <style />
              </div>
              <div
                className="segment-link "
              >
                <a>
                  Link 2
                </a>
                <style />
              </div>
              <div
                className="segment-link view-all"
              >
                <a>
                  products.viewAll
                </a>
                <style />
              </div>
            </div>
            <style />
          </div>
          <div
            className="footer-segment"
          >
            <input
              className="section-toggle"
              id="segment-toggle-ea63d8588c9b"
              type="checkbox"
            />
            <div
              className="segment-title"
            >
              <h3
                className="h4"
              >
                Hardware & Technology
              </h3>
              <label
                className="caret-down"
                htmlFor="segment-toggle-ea63d8588c9b"
              >
                <span
                  className="bp3-icon"
                >
                  <div
                    height={14}
                    src="/v4/svgs/caretDown.svg"
                    width="14px"
                  />
                </span>
              </label>
            </div>
            <div
              className="segment-links"
            >
              <div
                className="segment-link "
              >
                <a>
                  Link 1
                </a>
                <style />
              </div>
              <div
                className="segment-link view-all"
              >
                <a>
                  products.viewAll
                </a>
                <style />
              </div>
            </div>
            <style />
          </div>
          <div
            className="footer-segment"
          >
            <input
              className="section-toggle"
              id="segment-toggle-6c6d2f42e158"
              type="checkbox"
            />
            <div
              className="segment-title"
            >
              <h3
                className="h4"
              >
                Company Information
              </h3>
              <label
                className="caret-down"
                htmlFor="segment-toggle-6c6d2f42e158"
              >
                <span
                  className="bp3-icon"
                >
                  <div
                    height={14}
                    src="/v4/svgs/caretDown.svg"
                    width="14px"
                  />
                </span>
              </label>
            </div>
            <div
              className="segment-links"
            >
              <div
                className="segment-link "
              >
                <a>
                  Newz
                </a>
                <style />
              </div>
            </div>
            <style />
          </div>
        </div>
        <style />
      </div>
      <hr />
      <div
        className="footer-bottom"
      >
        <div
          className="footer-bottom-element with-top-margin"
        >
          <span
            className="info"
          >
            © 
            2023
             fleetster. 
            All rights reserved
          </span>
        </div>
        <div
          style={
            {
              "display": "flex",
              "flex": 1,
              "marginBottom": 0,
            }
          }
        />
        <div
          className="footer-bottom-element"
        >
          <div
            className="social-media-links with-top-margin"
          >
            <a
              href="https://www.twitter.com"
              rel="noreferrer"
              target="_blank"
              title="Twitter"
            >
              <div
                className="center-content"
                style={
                  {
                    "height": "100%",
                    "width": "100%",
                  }
                }
              >
                <img
                  alt="Twitter"
                  decoding="async"
                  height={54}
                  loading="lazy"
                  sizes="54px"
                  src="https://cdn.sanity.io/images/dp11egz7/development/524f8126e1c5510074164b42eeede663ea85243e-54x54.png?w=54&h=54&auto=format"
                  srcSet="https://cdn.sanity.io/images/dp11egz7/development/524f8126e1c5510074164b42eeede663ea85243e-54x54.png?w=54&h=54&auto=format 54w"
                  style={
                    {
                      "height": "auto",
                      "maxHeight": 54,
                      "maxWidth": 54,
                      "width": "100%",
                    }
                  }
                  width={54}
                />
              </div>
            </a>
            <a
              href="https://www.facebook.com"
              rel="noreferrer"
              target="_blank"
              title="Facebook"
            >
              <div
                className="center-content"
                style={
                  {
                    "height": "100%",
                    "width": "100%",
                  }
                }
              >
                <img
                  alt="Facebook"
                  decoding="async"
                  height={32}
                  loading="lazy"
                  sizes="20px"
                  src="https://cdn.sanity.io/images/dp11egz7/development/585f44e5d1dc950fefde5c00692d77441604fe7f-20x32.png?w=20&h=32&auto=format"
                  srcSet="https://cdn.sanity.io/images/dp11egz7/development/585f44e5d1dc950fefde5c00692d77441604fe7f-20x32.png?w=20&h=32&auto=format 20w"
                  style={
                    {
                      "height": "auto",
                      "maxHeight": 32,
                      "maxWidth": 20,
                      "width": "100%",
                    }
                  }
                  width={20}
                />
              </div>
            </a>
          </div>
          <div
            className="center-content knowledge-button"
          >
            <div
              className="call-to-action light-theme small"
            >
              <a
                className="center-content light-theme hover-enabled"
                rel="noreferrer"
                target="_blank"
              >
                Fleet Knowledge
                <style />
              </a>
              <style />
            </div>
            <style />
          </div>
        </div>
        <style />
      </div>
      <style />
    </footer>
  </div>,
  <style />,
  <style />,
  <style />,
]
`;
