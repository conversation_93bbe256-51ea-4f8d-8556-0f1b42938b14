#!/usr/bin/env bash

DOCKER_IMAGE_TAG="139347899477.dkr.ecr.eu-central-1.amazonaws.com/fleetster-website-frontend:$CI_COMMIT_REF_SLUG"
CI_HTTPS_PORT=$((10000+($CI_PIPELINE_ID%43000)))

set -e

eval $(aws ecr get-login --no-include-email --region eu-central-1)

docker pull "$DOCKER_IMAGE_TAG"

printf '\e[1;34m%-6s\e[m\n' "@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ http://fleetster.fleetster-review.de:$CI_HTTPS_PORT @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@"

docker run \
  -p $CI_HTTPS_PORT:80 \
  -d --restart unless-stopped \
  --name "fleetster-website-frontend-$CI_HTTPS_PORT" "$DOCKER_IMAGE_TAG"
