import {createClient} from '@sanity/client';
import {S3} from 'aws-sdk';

import {uploadS3Redirects} from '../uploadS3Redirects';
import {mockLanguage} from '../../config';

jest.mock('../../config', () => {
    const originalConfig = jest.requireActual('../../config');
    const mockLanguage = jest.fn().mockImplementation(() => 'DE');

    return {
        config: {
            ...originalConfig.config,
            get language() {
                return mockLanguage();
            }
        },
        mockLanguage
    };
});

jest.mock('aws-sdk', () => {
    const mockS3Upload = jest.fn().mockReturnThis();
    const mockS3Promise = jest.fn().mockReturnThis();

    return {
        S3: jest.fn(() => ({
            upload:  mockS3Upload,
            promise: mockS3Promise
        }))
    };
});
describe('S3 redirect upload tests', () => {
    const mockRedirects = [{
        i18n: {
            de: {
                originalUrl: {
                    current: '/test'
                },
                redirectUrl: {
                    current: '/redirect'
                }
            }
        }
    }];

    const mockSanityFetch = createClient().fetch.mockResolvedValue(mockRedirects);
    const mockS3Upload = new S3().upload;

    test('requests redirects to from sanity and uploads to s3 for default DE', async() => {
        uploadS3Redirects();
        await sleep();

        expect(S3).toHaveBeenCalledWith({
            endpoint: 'https://s3.eu-central-1.amazonaws.com',
            params:   {
                Bucket: 'website.fleetster.de'
            },
            region:           'eu-central-1',
            signatureVersion: 'v4'
        });
        expect(mockSanityFetch).toHaveBeenCalled();
        expect(mockS3Upload).toHaveBeenLastCalledWith({
            Body:                    '',
            ContentType:             'text/html',
            Key:                     '/test/index.html',
            WebsiteRedirectLocation: '/redirect'
        });
    });

    test('requests redirects to from sanity and uploads to s3 for EN', async() => {
        mockLanguage.mockImplementation(() => 'EN');

        uploadS3Redirects();
        await sleep();

        expect(S3).toHaveBeenCalledWith({
            endpoint: 'https://s3.eu-central-1.amazonaws.com',
            params:   {
                Bucket: 'website.fleetster.net'
            },
            region:           'eu-central-1',
            signatureVersion: 'v4'
        });
    });
});
