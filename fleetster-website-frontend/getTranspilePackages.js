/* eslint-disable no-sync */
const path = require('path');
const fs = require('fs');
const _ = require('lodash');

function getNodeModulesFolder(dir) {
    let dirs = [];
    const nodeModulesPath = path.join(dir, 'node_modules');

    if (fs.existsSync(nodeModulesPath)) {
        const nodeModulesSubDirs = fs.readdirSync(nodeModulesPath).map(name => path.join(nodeModulesPath, name)).filter(f => fs.statSync(f).isDirectory());
        dirs.push(nodeModulesSubDirs.map(dir => path.relative(__dirname, dir)));
    }

    return dirs;
}

function buildTranspilePackages(dir) {
    let transpilePackages = getNodeModulesFolder(dir);
    const subDirs = fs.readdirSync(dir).map(name => path.join(dir, name)).filter(f => fs.statSync(f).isDirectory());

    for (const subDir of subDirs) {
        if (subDir.includes('node_modules') || subDir.includes('__tests__')) {
            continue;
        }

        transpilePackages.push(getNodeModulesFolder(subDir));
    }

    if (subDirs.length) {
        transpilePackages.push(subDirs.map(dir => buildTranspilePackages(dir)));
    }

    return _.flattenDeep(transpilePackages);
}

function getTranspilePackages() {
    const transpilePackages = buildTranspilePackages(path.resolve(__dirname, 'src'));

    for (const packagePath of transpilePackages) {
        const packageJsonPath = path.join(packagePath, 'package.json');
        if (!fs.existsSync(packageJsonPath)) {
            fs.writeFileSync(packageJsonPath, JSON.stringify({version: '1.0.0'}));
        } else {
            const packageJson = JSON.parse(fs.readFileSync(packageJsonPath));
            const versionArray = (packageJson.version || '1.0.0').split('.');
            const patchVersion = versionArray[2];
            const bumpedVersion = `${versionArray[0]}.${versionArray[1]}.${parseInt(patchVersion) + 1}`;
            packageJson.version = bumpedVersion;
            fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson));
        }
    }

    return transpilePackages.map(packagePath => `../${packagePath}`);
}

module.exports = getTranspilePackages;
