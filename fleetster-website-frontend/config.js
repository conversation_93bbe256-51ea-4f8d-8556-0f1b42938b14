/* eslint-disable max-lines */
const _ = require('lodash');
const rc = require('rc');
const path = require('path');
const dotenv = require('dotenv');

if (process.stdout) {
    dotenv.config();
}

const configs = {};

configs.development = {
    env:       'development',
    language:  process.env.WEBSITE_LANGUAGE || 'de',
    languages: [
        {label: 'DE', href: 'http://localhost:3000'},
        {label: 'EN', href: 'http://localhost:3000'},
        {label: 'NL', href: 'http://localhost:3000'},
        {label: 'ES', href: 'http://localhost:3000'}
    ],
    development: {
        fetchDataOnHotReload: false
    },
    host: {
        url:      process.env.WEBSITE_URL || 'http://localhost:3000',
        loginUrl: 'http://localhost:8000'
    },
    webpack: {
        useBundleAnalyzer: false
    },
    services: {
        sanity: {
            client: {
                projectId:                 process.env.SANITY_PROJECT_ID,
                dataset:                   process.env.SANITY_CLIENT_DATASET || 'development',
                token:                     process.env.SANITY_CLIENT_TOKEN,
                useCdn:                    false,
                ignoreBrowserTokenWarning: true,
                apiVersion:                '2025-05-01'
            },
            studioDataOutputPath:    path.resolve(__dirname, 'src/node_modules/rf-data'),
            newStudioDataOutputPath: path.resolve(__dirname, 'sanityStudioData')
        },
        languages: {
            server:                 'https://control-tower-next.fleetster.de/api',
            adminGroup:             'fleetster',
            theme:                  'fleetster',
            project:                'fleetster-website-frontend',
            translationsOutputPath: path.resolve(__dirname, 'src/node_modules/rf-i18n/translations.json')
        }
    }
};

configs.preview = _.merge({}, configs.development, {
    env:       'preview',
    languages: [
        {label: 'DE', href: 'https://nextjs-website-preview.fleetster.de'},
        {label: 'EN', href: 'https://nextjs-website-preview.fleetster.de'},
        {label: 'NL', href: 'https://nextjs-website-preview.fleetster.de'},
        {label: 'ES', href: 'https://nextjs-website-preview.fleetster.de'}
    ],
    services: {
        sanity: {
            client: {
                dataset: process.env.SANITY_CLIENT_DATASET || 'production'
            }
        }
    }
});

configs.testing = _.merge({}, configs.development, {
    env:       'testing',
    languages: [
        {label: 'DE', href: process.env.WEBSITE_URL},
        {label: 'EN', href: process.env.WEBSITE_URL},
        {label: 'NL', href: process.env.WEBSITE_URL},
        {label: 'ES', href: process.env.WEBSITE_URL}
    ],
    host: {
        url:      process.env.WEBSITE_URL,
        loginUrl: 'https://my.testing.fleetster.de'
    }
});

configs.production = _.merge({}, configs.development, {
    env:      'production',
    language: process.env.WEBSITE_LANGUAGE || 'de',
    host:     {
        url:      process.env.WEBSITE_URL || 'https://www.fleetster.de',
        loginUrl: 'https://my.fleetster.de'
    },
    webpack: {
        useBundleAnalyzer: false
    },
    languages: [
        {label: 'DE', href: 'https://www.fleetster.de'},
        {label: 'EN', href: 'https://www.fleetster.net'},
        {label: 'NL', href: 'https://www.fleetster.nl'},
        {label: 'ES', href: 'https://www.fleetster.es'}
    ],
    services: {
        sanity: {
            client: {
                dataset: process.env.SANITY_CLIENT_DATASET || 'production',
                useCdn:  true
            }
        }
    }
});

const config = rc('fleetsterWebsiteFrontend', configs[process.env.NODE_ENV] || configs.development);

module.exports.config = config;

