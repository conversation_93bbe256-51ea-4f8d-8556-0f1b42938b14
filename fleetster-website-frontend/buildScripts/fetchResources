#!/usr/bin/env bash

# makes it so that any program that is executed from this script returns != 0, the script will exit
# with an error
set -e

echo
echo 'TRANSLATIONS'
echo

./buildScripts/downloadTranslations.js

if [ "$NODE_ENV" != "test" ]; then
  echo
  echo 'SANITY STUDIO DATA'
  echo

  ./buildScripts/downloadSanityStudioData/index.js

  echo
  echo 'GENERATE SITEMAP AND ROBOTS.TXT'
  echo

  ./buildScripts/exportSiteMapAndRobots/index.js
fi
