#!/usr/bin/env node

const qs = require('qs');
const fs = require('fs-extra');
const _ = require('lodash');
const fetch = require('node-fetch');

const {config} = require('../config');

const {services: {languages}} = config;

const {server, adminGroup, theme, project} = languages;

const targetLanguage = config.language;

const languagesFilePath = languages.translationsOutputPath;

const fullNames = {
    en: 'English',
    de: 'Deutsch',
    nl: 'Dutch',
    fr: 'Francais',
    cn: 'Chinese',
    it: 'Italiano',
    pt: 'Português',
    es: 'Español',
    ro: 'Română',
    pl: 'Polish',
    sv: 'Svenska',
    no: 'Norsk'
};

function createLanguagesArray(labels, languagesCode) {
    console.log('LABELS: ', labels.length);

    const languages = [];

    _.each(labels, label => {
        _.each(languagesCode, code => {
            if (!label.label) {
                return;
            }

            let selectedLanguage = _.find(languages, {name: code});

            if (!selectedLanguage) {
                selectedLanguage = {
                    name:        code,
                    fullName:    fullNames[code] || 'fullNameIsMissingInLanguageExporter',
                    translation: {}
                };

                languages.push(selectedLanguage);
            }

            _.set(selectedLanguage.translation, label.label, label.translations[code] || '');
        });
    });

    return languages;
}

async function writeLanguage(language) {
    const languageFileContent = `${JSON.stringify(language.translation, null, 4)}`;

    try {
        await fs.writeFile(languagesFilePath, languageFileContent);

        console.log(`Language - ${ language.fullName } (${ language.name }) - Saved`);
    } catch (err) {
        console.log(err);
    }
}

async function writeLanguages(languages) {
    for (const language of languages) {
        await writeLanguage(language);
    }
}

async function exportLanguageFromProject(projectName) {
    let request = await fetch(`${server}/label/language-codes`);
    let languagesCode = await request.json();

    languagesCode = languagesCode.filter(c => c === targetLanguage);

    const query = qs.stringify({
        project:         projectName,
        adminGroup,
        theme,
        draft:           true,
        superTranslator: true
    });

    request = await fetch(`${server}/label/export/${projectName}?${query}`);
    const labels = await request.json();

    const languages = createLanguagesArray(labels, languagesCode);

    await writeLanguages(languages);
}

(async function languageExporter() {
    try {
        await fs.remove(languagesFilePath);

        await exportLanguageFromProject(project);
    } catch (e) {
        console.error(e);
        // eslint-disable-next-line no-process-exit
        process.exit(1);
    }
}());

