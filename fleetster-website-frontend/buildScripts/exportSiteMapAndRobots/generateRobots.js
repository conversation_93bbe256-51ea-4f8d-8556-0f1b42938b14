const _ = require('lodash');
const {createWriteStream} = require('fs');

const {fetchDocuments} = require('../../src/node_modules/sanity-client-adapter');
const {config} = require('../../config');

const language = config.language;
const sanityConfig = config.services.sanity;

const routes = require(`${sanityConfig.newStudioDataOutputPath}/routes.json`);

function getAllDisallowedURIs() {
    const disallowedUrisQuery = '*[_type == "disallow-robots" && !(_id match "drafts*") && language == $language] { ... }';

    return fetchDocuments(disallowedUrisQuery, {language}, language);
}

function registerRouteRobots({route, pageSlug, robotsWriteStream}) {
    const disallowRobots = _.get(route, `i18n.${language}.disallowRobots`);

    if (disallowRobots) {
        robotsWriteStream.write(`Disallow: /${pageSlug}$\n`);
    }
}

const robotsTxtProd = `User-agent: *
Allow: /
Sitemap: ${config.host.url}/sitemap.xml

`;

const robotsTxtDev = `User-agent: *
Disallow: /
`;

async function generateRobots() {
    const robotsWriteStream = createWriteStream('./public/robots.txt');

    if (process.env.NODE_ENV !== 'production' || process.env.WEBSITE_URL.includes('staging')) {
        robotsWriteStream.write(robotsTxtDev);
        robotsWriteStream.close();

        return;
    }

    robotsWriteStream.write(robotsTxtProd);

    _.forEach(routes, (route, pageSlug) => registerRouteRobots({route, pageSlug, robotsWriteStream}));

    let disallowedURIs = await getAllDisallowedURIs();
    disallowedURIs = _(disallowedURIs).map(`i18n.${language}.uri`).compact().value();

    _.forEach(disallowedURIs, uri => robotsWriteStream.write(`Disallow: ${uri}\n`));

    robotsWriteStream.close();
}

module.exports = {generateRobots};

