const _ = require('lodash');
const {createWriteStream} = require('fs');
const {execSync} = require('child_process');
const moment = require('moment');

const {config} = require('../../config');
const sanityConfig = config.services.sanity;
const {changelogUrlMap, newsUrlMap} = require('../downloadSanityStudioData/constants');

const language = config.language;
const languages = config.languages;

let lastCommitDate = execSync('git log -1 --format=%cd').toString().trim();
lastCommitDate = new Date(lastCommitDate);

function generateNonPageLocalizedLinks(pageSlug, urlMap, paginatedRoutes) {
    const links = [];
    for (const lang of languages) {
        const parsedSlug = pageSlug.split('/');
        let localizedPageSlug;

        if (parsedSlug[1]) {
            localizedPageSlug = `${urlMap[lang.label.toLowerCase()]}/${parsedSlug[1]}`;
        } else {
            localizedPageSlug = `${urlMap[lang.label.toLowerCase()]}`;
        }

        if (!_.get(paginatedRoutes, `i18n.${lang.label.toLowerCase()}.routes.${localizedPageSlug}`)) {
            continue;
        }

        const url = `${lang.href}/${localizedPageSlug}`;
        links.push({lang: lang.label.toLowerCase(), url});
    }

    return links;
}

function registerChangelog(pageSlug, sitemap, paginatedRoutes) {
    sitemap.write({
        url:        pageSlug,
        changefreq: 'weekly',
        lastmod:    new Date().toISOString(),
        links:      generateNonPageLocalizedLinks(pageSlug, changelogUrlMap, paginatedRoutes)
    });
}

function registerNews(pageSlug, sitemap, paginatedRoutes) {
    sitemap.write({
        url:        pageSlug,
        changefreq: 'monthly',
        lastmod:    new Date().toISOString(),
        links:      generateNonPageLocalizedLinks(pageSlug, newsUrlMap, paginatedRoutes)
    });
}

function generatePageLocalizedLinks(route) {
    const routeI18n = route.i18n || {};
    let links = [];

    for (const lang in routeI18n) {
        const localizedRoute = routeI18n[lang];

        const path = _.get(localizedRoute, 'slug.current');
        if (_.isNil(path)) { continue; }

        const langCfg = languages.find(l => l.label.toLowerCase() === lang);

        const url = `${langCfg.href}/${path}`;

        if (localizedRoute.includeInSitemap && !localizedRoute.disallowRobots) {
            links.push({lang, url});
        }
    }

    return links;
}

function registerPage({route, pageSlug, sitemap}) {
    const includeInSitemap = _.get(route, `i18n.${language}.includeInSitemap`);
    const disallowRobots = _.get(route, `i18n.${language}.disallowRobots`);
    const page = _.get(route, `i18n.${language}.page`);

    if (_.isEmpty(page?.i18n?.[language]?.content)) {
        return;
    }

    const updatedAt = _.get(page, '_updatedAt', route._updatedAt);
    const lastModified = moment(lastCommitDate).isAfter(moment(updatedAt)) ? lastCommitDate : new Date(updatedAt);

    if (includeInSitemap && !disallowRobots) {
        sitemap.write({
            url:        `/${pageSlug}`,
            changefreq: 'weekly',
            lastmod:    lastModified.toISOString(),
            links:      generatePageLocalizedLinks(route)
        });
    }
}

function generateSitemap(sitemap) {
    const news = require(`${sanityConfig.newStudioDataOutputPath}/news.json`);
    const changelogs = require(`${sanityConfig.newStudioDataOutputPath}/changelog.json`);
    const routes = require(`${sanityConfig.newStudioDataOutputPath}/routes.json`);

    const paginatedRoutes = _.merge({}, changelogs, news);

    const sitemapWriteStream = createWriteStream('./public/sitemap.xml');
    sitemap.pipe(sitemapWriteStream);

    _.forEach(routes, (route, pageSlug) => registerPage({route, pageSlug, sitemap}));
    _.forEach(changelogs.i18n[language].routes, (changelog, pageSlug) => registerChangelog(pageSlug, sitemap, paginatedRoutes));
    _.forEach(news.i18n[language].routes, (article, pageSlug) => registerNews(pageSlug, sitemap, paginatedRoutes));

    sitemap.end();
}

module.exports = {generateSitemap, generateNonPageLocalizedLinks, generatePageLocalizedLinks};

