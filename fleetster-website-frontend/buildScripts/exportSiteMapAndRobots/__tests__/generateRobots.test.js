describe('generateRobots', () => {
    const routesMock = _.chain({})
        .set('regularRoute.i18n.de', {page: {_ref: 'page'}, includeInSitemap: true})
        .set('disallowRobotsRoute.i18n.de', {page: {_ref: 'page2'}, includeInSitemap: true, disallowRobots: true})
        .set('notIncludedInSitemapRoute.i18n.de', {page: {_ref: 'page2'}, includeInSitemap: false})
        .value();

    let robotsWriteStreamMock;
    let sanityClientMock;

    const mockWrite = () => {
        let writeString = '';

        return string => {
            writeString += string;

            return writeString;
        };
    };

    const fsDeps = {close: jest.fn(), on: jest.fn(), once: jest.fn(), emit: jest.fn(), end: jest.fn()};

    beforeAll(() => {
        jest.doMock('fs', () => ({
            ...jest.requireActual('fs'),
            createWriteStream: () => robotsWriteStreamMock}));
    });

    beforeEach(async() => {
        process.env.NODE_ENV = 'production';

        jest.doMock('../../../sanityStudioData/routes.json', () => routesMock, {virtual: true});

        robotsWriteStreamMock = {write: mockWrite(), ...fsDeps};

        sanityClientMock = (await import('@sanity/client')).createClient().fetch;

        jest.clearAllMocks();
    });

    it('should generate robots.txt correctly', async() => {
        const {generateRobots} = await import('../generateRobots');

        sanityClientMock.mockResolvedValue([{i18n: {de: {uri: '/disallowed-uri'}}}]);

        await generateRobots();

        const writtenRobots = robotsWriteStreamMock.write('');

        expect(writtenRobots).toMatchSnapshot();
    });

    it('should not include empty values in robots.txt', async() => {
        const {generateRobots} = await import('../generateRobots');

        sanityClientMock.mockResolvedValueOnce([
            {i18n: {de: {uri: '/disallowed-uri'}}},
            {i18n: {de: {uri: ''}}},
            {i18n: {de: {}}}
        ]);

        await generateRobots();

        const writtenRobots = robotsWriteStreamMock.write('');

        expect(writtenRobots).not.toContain('Disallow: \n');
        expect(writtenRobots).not.toContain('Disallow: undefined');
    });

    it('should disallow all routes if NODE_ENV is not production', async() => {
        process.env.NODE_ENV = 'development';

        const {generateRobots} = await import('../generateRobots');

        await generateRobots();

        const writtenRobots = robotsWriteStreamMock.write('');

        expect(writtenRobots).toMatchSnapshot();
    });

    it('should disallow all routes if WEBSITE_URL contains staging string', async() => {
        const previousWebsiteUrl = process.env.WEBSITE_URL;
        process.env.WEBSITE_URL = 'https://nextjs-website-staging.fleetster.de';

        const {generateRobots} = await import('../generateRobots');

        await generateRobots();

        process.env.WEBSITE_URL = previousWebsiteUrl;

        const writtenRobots = robotsWriteStreamMock.write('');

        expect(writtenRobots).toMatchSnapshot();
    });
});
