// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`generateRobots should disallow all routes if NODE_ENV is not production 1`] = `
"User-agent: *
Disallow: /
"
`;

exports[`generateRobots should disallow all routes if WEBSITE_URL contains staging string 1`] = `
"User-agent: *
Disallow: /
"
`;

exports[`generateRobots should generate robots.txt correctly 1`] = `
"User-agent: *
Allow: /
Sitemap: http://www.testing-env.net/sitemap.xml

Disallow: /disallowRobotsRoute$
Disallow: /disallowed-uri
"
`;
