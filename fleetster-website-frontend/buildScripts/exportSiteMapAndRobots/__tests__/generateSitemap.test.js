const childProcess = require('child_process');
const {SitemapStream} = require('sitemap');

const {changelogUrlMap, newsUrlMap} = require('../../downloadSanityStudioData/constants');

const {config} = require('../../../config');

const sanityConfig = config.services.sanity;
const hostname = config.host.url;

describe('generateSitemap', () => {
    const newsMock = _.chain({})
        .set(`i18n.de.routes.${newsUrlMap.de}`, {})
        .set(`i18n.en.routes.${newsUrlMap.en}`, {})
        .value();
    const changelogMock = _.set({}, `i18n.de.routes.${changelogUrlMap.de}`, {});
    const UPDATED_AT = '2022-10-25T12:00:00.000Z';
    const routesMock = _.chain({})
        .set('regularRoute.i18n.de', {page: {_ref: 'page', _updatedAt: UPDATED_AT, i18n: {de: {content: {something: 'good'}}}}, includeInSitemap: true})
        .set('changelog/q1.i18n.de', {page: {_ref: 'page', _updatedAt: UPDATED_AT, i18n: {de: {content: {something: 'good'}}}}, includeInSitemap: true})
        .set('pageWithNoContent.i18n.de', {page: {_ref: 'pageWithNoContent', _updatedAt: UPDATED_AT}, includeInSitemap: true})
        .set('disallowRobotsRoute.i18n.de', {page: {_ref: 'page2'}, includeInSitemap: true, disallowRobots: true})
        .set('notIncludedInSitemapRoute.i18n.de', {page: {_ref: 'page2'}, includeInSitemap: false})
        .value();

    let sitemapWriteStreamMock;

    const mockWrite = () => {
        let writeString = '';

        return string => {
            writeString += string;

            return writeString;
        };
    };

    const fsDeps = {close: jest.fn(), on: jest.fn(), once: jest.fn(), emit: jest.fn(), end: jest.fn()};

    let _Date;

    beforeAll(() => {
        _Date = Date;

        jest.doMock('fs', () => ({
            ...jest.requireActual('fs'),
            createWriteStream: () => sitemapWriteStreamMock}));
    });

    beforeEach(() => {
        const dateMock = new _Date('2022-10-26T12:00:00.000Z');
        global.Date = jest.fn(() => dateMock);
        Date.now = () => 1666785600000;
        _.set(Date, 'UTC.apply', jest.fn());

        jest.spyOn(childProcess, 'execSync').mockImplementation(() => '2022-10-23T12:00:00.000Z');

        jest.doMock(`${sanityConfig.newStudioDataOutputPath}/news.json`, () => newsMock, {virtual: true});
        jest.doMock(`${sanityConfig.newStudioDataOutputPath}/changelog.json`, () => changelogMock, {virtual: true});
        jest.doMock(`${sanityConfig.newStudioDataOutputPath}/routes.json`, () => routesMock, {virtual: true});

        sitemapWriteStreamMock = {write: mockWrite(), ...fsDeps};
    });

    it('should generate sitemap', () => {
        const {generateSitemap} = require('../generateSitemap');

        const sitemap = new SitemapStream({hostname, cacheTime: 600000});

        generateSitemap(sitemap);

        const writtenSitemap = sitemapWriteStreamMock.write('');

        expect(writtenSitemap).toMatchSnapshot();
    });

    it('should use last commit date if its more recent', () => {
        global.Date = _Date;
        let {generateSitemap} = require('../generateSitemap');

        let gitCommitDate = '2022-10-23T12:00:00.000Z';
        jest.spyOn(childProcess, 'execSync').mockImplementation(() => gitCommitDate);

        let sitemap = new SitemapStream({hostname, cacheTime: 600000});
        generateSitemap(sitemap);
        let writtenSitemap = sitemapWriteStreamMock.write('');

        expect(writtenSitemap).toContain(UPDATED_AT);
        expect(writtenSitemap).not.toContain(gitCommitDate);

        jest.resetModules();
        gitCommitDate = '2022-10-27T12:00:00.000Z';
        jest.spyOn(childProcess, 'execSync').mockImplementation(() => gitCommitDate);
        ({generateSitemap} = require('../generateSitemap'));

        sitemap = new SitemapStream({hostname, cacheTime: 600000});
        sitemapWriteStreamMock = {write: mockWrite(), ...fsDeps};
        generateSitemap(sitemap);
        writtenSitemap = sitemapWriteStreamMock.write('');

        expect(writtenSitemap).not.toContain(UPDATED_AT);
        expect(writtenSitemap).toContain(gitCommitDate);
    });

    describe('generateNonPageLocalizedLinks', () => {
        test('generates non page localized links', () => {
            const {generateNonPageLocalizedLinks} = require('../generateSitemap');

            const pageSlug = 'changelog/q1';

            const result = generateNonPageLocalizedLinks(pageSlug, changelogUrlMap, {
                i18n: {
                    de: {
                        routes: {
                            'produkt-updates/q1': {}
                        }
                    }
                }
            });

            expect(result).toEqual([{
                lang: 'de',
                url:  'http://localhost:3000/produkt-updates/q1'
            }]);
        });
    });

    describe('generatePageLocalizedLinks', () => {
        const {generatePageLocalizedLinks} = require('../generateSitemap');

        test('should generate correct links with languages and paths', () => {
            const route = {
                i18n: {
                    en: {
                        slug:             {current: 'hello'},
                        includeInSitemap: true,
                        disallowRobots:   false
                    },
                    de: {
                        slug:             {current: 'hallo'},
                        includeInSitemap: true,
                        disallowRobots:   false
                    }
                }
            };

            const result = generatePageLocalizedLinks(route);

            expect(result).toEqual([
                {
                    lang: 'en',
                    url:  'http://localhost:3000/hello'
                },
                {
                    lang: 'de',
                    url:  'http://localhost:3000/hallo'
                }
            ]);
        });

        test('should skip links with no path or disallowed by robots', () => {
            const route = {
                i18n: {
                    en: {
                        slug:             {current: 'hello'},
                        includeInSitemap: true,
                        disallowRobots:   false
                    },
                    de: {
                        slug:             {current: 'hallo'},
                        includeInSitemap: true,
                        disallowRobots:   true
                    }
                }
            };

            const result = generatePageLocalizedLinks(route);

            expect(result).toEqual([
                {
                    lang: 'en',
                    url:  'http://localhost:3000/hello'
                }
            ]);
        });
    });
});
