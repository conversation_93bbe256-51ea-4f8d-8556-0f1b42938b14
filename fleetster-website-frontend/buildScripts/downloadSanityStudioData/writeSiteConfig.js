#!/usr/bin/env node
const fs = require('fs-extra');
const _ = require('lodash');

const {fetchDocuments} = require('../../src/node_modules/sanity-client-adapter');
const {config} = require('../../config');
const defaultConfig = require('./defaultConfig.json');

const sanityConfig = config.services.sanity;

function getSiteConfig() {
    const appConfigQuery = `*[_id == "global-config"] {
        _id,
        logo,
        footerLogo,
        socialMediaLinks,
        salesEndpoints,
        language
    }[0]`;

    return fetchDocuments(appConfigQuery);
}

async function writeSiteConfig() {
    let siteConfig = await getSiteConfig();

    siteConfig = {
        ...siteConfig,
        language:  config.language,
        languages: config.languages,
        host:      config.host,
        sanity:    config.services.sanity,
        env:       config.env
    };

    // Ensure i18n object exists
    if (!siteConfig.i18n) {
        siteConfig.i18n = {};
    }

    siteConfig.i18n[config.language] = _.merge(defaultConfig.i18n[config.language], siteConfig.i18n[config.language] || {});

    if (config.env === 'production') {
        delete siteConfig.sanity.client.token;
    }

    await fs.writeFile(`${sanityConfig.studioDataOutputPath}/siteConfig.json`, JSON.stringify(siteConfig));
}

module.exports = {writeSiteConfig};

