const _ = require('lodash');
const fs = require('fs-extra');
const moment = require('moment');

const {newsUrlMap} = require('./constants');

const {config} = require('../../config');
const {getNewsMetaTitleAndDescription, removeEndSlashFromUrl} = require('./utils');

const languages = config.languages;
const sanityConfig = config.services.sanity;

function getPagesWithNews(routes, language) {
    return _.transform(routes, (pagesWithNews, route) => {
        const includeInNews = _.get(route, `i18n.${language}.includeInNews`);

        const page = _.get(route, `i18n.${language}.page.i18n.${language}`);

        if (page && includeInNews) {
            _.forEach(page.content, content => {
                content.pageSlug = route.i18n[language].slug.current;
                content.year = moment(content.publishedOn).year();
            });

            pagesWithNews.push(page);
        }
    }, []);
}

function getNewsArticles(pagesWithNews) {
    let articles = [];
    _.forEach(pagesWithNews, page => {
        _.forEach(page.content, content => {
            if (content._type === 'articleHeadingSection') {
                content.includeInNews = true;
                articles.push(content);
            }
        });
    });

    return articles;
}

function getSortedArticles(articles) {
    return _.orderBy(articles, content => { return moment(content.publishedOn, 'YYYY-MM-DD'); }, ['desc']);
}

function getPaginationData(formattedArticles) {
    const pageSlugs = _.keys(formattedArticles);

    const mappedPagination = _.map(pageSlugs, slug => {
        return {
            year:     parseInt(slug.split('/')[1]),
            pageSlug: slug
        };
    });

    const orderedPagination = _.orderBy(mappedPagination, ['year'], ['desc']);

    return _.map(orderedPagination, slug => { return {year: slug.year.toString(), pageSlug: slug.pageSlug}; });
}

const MINIMAL_INITIAL_CONTENT = 5;

// eslint-disable-next-line max-params
function reduceByYear(result, item, index, allItems) {
    let belowMinimumContentLength = result.length < MINIMAL_INITIAL_CONTENT;
    let changeLogInQuarter = item.year === allItems[0].year;
    if (belowMinimumContentLength || changeLogInQuarter) {
        return result.concat(_.assign(item, {isIndex: true}));
    } else {
        return result;
    }
}

function getIndexContent(items) {
    return _(items)
        .values()
        .map('content')
        .flatten()
        .orderBy(['year'], ['desc'])
        .reduce(reduceByYear, []);
}

function getFormattedArticles(indexSlug, articles) {
    return _.reduce(articles, (result, article) => {
        const key = `${indexSlug}/${article.year}`;
        if (result[key]) {
            result[key].content.push(article);
        } else {
            result[key] = {content: [article]};
        }

        return result;
    }, {});
}

function generateNewsJson(lang) {
    const {host} = require(`${sanityConfig.studioDataOutputPath}/siteConfig.json`);
    const routes = require(`${sanityConfig.newStudioDataOutputPath}/routes.json`);

    const indexNewsPageSlug = newsUrlMap[lang];

    const articleFlow = _.flow([getPagesWithNews, getNewsArticles, getSortedArticles, articles => getFormattedArticles(indexNewsPageSlug, articles)]);

    const formattedArticles = articleFlow(routes, lang);
    const pagination = getPaginationData(formattedArticles);

    _.forEach(formattedArticles, (article, slug) => {
        article.pagination = pagination;
        article.title = getNewsMetaTitleAndDescription(article.content, slug).title;
        article.description = getNewsMetaTitleAndDescription(article.content, slug).description;
        article.canonical = removeEndSlashFromUrl(`${host.url}/${slug}`);
    });

    const indexContent = getIndexContent(formattedArticles);
    const {title, description} = getNewsMetaTitleAndDescription(indexContent, indexNewsPageSlug);

    formattedArticles[indexNewsPageSlug] = {
        title,
        content:   indexContent,
        description,
        canonical: removeEndSlashFromUrl(`${host.url}/${indexNewsPageSlug}`),
        pagination
    };

    return {routes: formattedArticles};
}

async function writeNews() {
    const news = {i18n: {}};

    for (const lang of languages) {
        news.i18n[lang.label.toLowerCase()] = generateNewsJson(lang.label.toLowerCase());
    }

    await fs.writeFile(`${sanityConfig.newStudioDataOutputPath}/news.json`, JSON.stringify(news));
}

module.exports = {writeNews};
