const _ = require('lodash');

const {config} = require('../../config');

const sanityConfig = config.services.sanity;

function removeEndSlashFromUrl(url) {
    return _.isString(url) ? url.replace(/\/$/, '') : url;
}

// eslint-disable-next-line max-statements
function getNewsMetaTitleAndDescription(page, pageSlug) {
    const {language, i18n} = require('../../src/node_modules/rf-data/siteConfig.json');
    const newsTitle = i18n[language].newsTitle;
    const newsDescription = i18n[language].newsDescription;
    const year = page?.[0]?.year || page?.content?.[0]?.year;
    const isIndex = !pageSlug.includes('/');

    let title;
    let description;

    if (isIndex) {
        title = newsTitle.latest;
        description = newsDescription.latest;
    } else {
        title = newsTitle.year.replace('*|YEAR|*', year);
        description = newsDescription.year.replace('*|YEAR|*', year);
    }

    return {title, description};
}

// eslint-disable-next-line max-statements
function getChangelogMetaTitleAndDescription(page, pageSlug) {
    const {language, i18n} = require('../../src/node_modules/rf-data/siteConfig.json');
    const {year, quarter} = page?.[0] || page?.content?.[0] || {};
    const changelogTitle = i18n[language].changelogTitle;
    const changelogDescription = i18n[language].changelogDescription;
    const quarterString = `Q${quarter} ${year}`;
    const isIndex = !pageSlug.includes('/');

    let title;
    let description;

    if (isIndex) {
        title = changelogTitle.latest;
        description = changelogDescription.latest;
    } else {
        title = changelogTitle.quarter.replace('*|QUARTER|*', quarterString);
        description = changelogDescription.quarter.replace('*|QUARTER|*', quarterString);
    }

    return {title, description};
}

// eslint-disable-next-line max-statements
function generateBreadCrumbItemName(breadCrumbPath, language) {
    const news = require(`${sanityConfig.newStudioDataOutputPath}/news.json`);
    const changelog = require(`${sanityConfig.newStudioDataOutputPath}/changelog.json`);
    const routes = require(`${sanityConfig.newStudioDataOutputPath}/routes.json`);

    const routePageTitle = _.get(routes, `${breadCrumbPath}.i18n.${language}.page.i18n.${language}.title`);
    if (routePageTitle) {
        return routePageTitle;
    }

    const newsRef = _.get(news, `i18n.${language}.routes.${breadCrumbPath}`);
    if (newsRef) {
        return getNewsMetaTitleAndDescription(newsRef, breadCrumbPath).title;
    }

    const changelogRef = _.get(changelog, `i18n.${language}.routes.${breadCrumbPath}`);
    if (changelogRef) {
        return getChangelogMetaTitleAndDescription(changelogRef, breadCrumbPath).title;
    }

    return null;
}

function generateBreadCrumbsList(slug) {
    const {config} = require('../../config');
    const baseURL = config.host.url;
    const parts = slug.split('/');
    const breadcrumbList = [{
        '@type':  'ListItem',
        position: 1,
        item:     {
            '@id': baseURL,
            name:  generateBreadCrumbItemName('', config.language)
        }
    }];

    let currentURL = baseURL;
    for (let i = 0; i < parts.length; i++) {
        currentURL += `/${parts[i]}`;
        const name = generateBreadCrumbItemName(currentURL.replace(baseURL, '').substring(1), config.language);

        if (name) {
            const listItem = {
                '@type':  'ListItem',
                position: breadcrumbList.length + 1,
                item:     {
                    '@id': currentURL,
                    name
                }
            };
            breadcrumbList.push(listItem);
        }
    }

    return breadcrumbList;
}

module.exports = {
    generateBreadCrumbsList,
    getNewsMetaTitleAndDescription,
    getChangelogMetaTitleAndDescription,
    removeEndSlashFromUrl
};
