// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`writeRoutes writes routes to file 1`] = `
{
  "": {
    "_id": "homePage",
    "_updatedAt": "2020-01-01T00:00:00Z",
    "i18n": {
      "de": {
        "includeInSitemap": true,
        "page": {
          "_ref": "page1",
          "_updatedAt": "2020-01-01T00:00:00Z",
        },
        "slug": {
          "current": "",
        },
      },
      "es": {
        "includeInSitemap": true,
        "page": {
          "_ref": "page1",
        },
        "slug": {
          "current": "",
        },
      },
    },
  },
  "de-route1": {
    "_createdAt": "2020-01-01T00:00:00Z",
    "_id": "route1",
    "_updatedAt": "2020-01-01T00:00:00Z",
    "i18n": {
      "de": {
        "page": {
          "_createdAt": "2020-01-01T00:00:00Z",
          "_id": "page1",
          "_updatedAt": "2020-01-01T00:00:00Z",
          "language": "de",
        },
        "slug": {
          "current": "de-route1",
        },
      },
    },
    "language": "en",
  },
}
`;
