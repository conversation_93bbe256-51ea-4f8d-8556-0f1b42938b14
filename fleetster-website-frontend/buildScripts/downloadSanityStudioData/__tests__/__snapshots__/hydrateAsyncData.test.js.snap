// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`handleAsyncData should call google api on video section and inject correct data 1`] = `"{"route1":{"i18n":{"de":{"page":{"i18n":{"de":{"content":[{"_type":"videoSection","videoUrl":{"href":"https://www.youtube.com/watch?v=exampleID"},"youtubeVideoData":{"id":"exampleID","publishedAt":"2019-05-17T15:00:13Z","title":"<PERSON><PERSON> <PERSON> Plays With Puppies While Answering Fan Questions","description":"<PERSON><PERSON> <PERSON> plays with puppies and answers questions","thumbnails":{"standard":{"url":"https://i.ytimg.com/vi/rOqUiXhECos/sddefault.jpg"}},"duration":"PT5M27S","viewCount":"18030191"}},{"_type":"videoSection","videoUrl":{"href":"https://www.vimeo.com/watch?v=exampleID"}},{"_type":"whateverSection"}]}}}}}}}"`;

exports[`handleAsyncData should call google api on video section and inject correct data 2`] = `"{"i18n":{"de":{"routes":{"changelog1":{"content":[{"_type":"changelog","videoUrl":"https://www.youtube.com/watch?v=anotherExampleID&extraParam=1","youtubeVideoData":{"id":"anotherExampleID","publishedAt":"2019-05-18T15:00:13Z","title":"Keanu Reeves kills people responsible for stealing his Puppies While Answering Fan Questions","description":"Keanu Reeves killing the people responsible for stealing his Puppies While Answering Fan Questions","thumbnails":{"standard":{"url":"https://i.ytimg.com/vi/rOqUiXhECos/sddefault.jpg"}},"duration":"PT5M27S","viewCount":"999999999"}}]},"changelogWithNoVideoUrl":{"content":[{"_type":"changelog"}]},"changelogWithNoContent":{}}}}}"`;
