jest.mock('fs-extra', () => ({
    writeFile: jest.fn()
}));
describe('handleAsyncData', () => {
    beforeEach(() => {
        jest.doMock('../../../sanityStudioData/routes.json', () => ({
            route1: {
                i18n: {
                    de: {
                        page: {
                            i18n: {
                                de: {
                                    content: [{
                                        _type:    'videoSection',
                                        videoUrl: {
                                            href: 'https://www.youtube.com/watch?v=exampleID'
                                        }
                                    },
                                    {
                                        _type:    'videoSection',
                                        videoUrl: {
                                            href: 'https://www.vimeo.com/watch?v=exampleID'
                                        }
                                    },
                                    {
                                        _type: 'whateverSection'
                                    }]
                                }
                            }
                        }
                    }
                }
            }
        }
        ), {virtual: true});
        jest.doMock('../../../sanityStudioData/changelog.json', () => ({
            i18n: {
                de: {
                    routes: {
                        changelog1: {
                            content: [{
                                _type:    'changelog',
                                videoUrl: 'https://www.youtube.com/watch?v=anotherExampleID&extraParam=1'
                            }]
                        },
                        changelogWithNoVideoUrl: {
                            content: [{
                                _type: 'changelog'
                            }]
                        },
                        changelogWithNoContent: {}
                    }
                }
            }
        }
        ), {virtual: true});
    });

    test('should call google api on video section and inject correct data', async() => {
        const fetchSpy = jest.spyOn(global, 'fetch');

        fetchSpy.mockResolvedValueOnce({
            ok:   true,
            json: () => ({
                items: [
                    {
                        snippet: {
                            publishedAt: '2019-05-17T15:00:13Z',
                            title:       'Keanu Reeves Plays With Puppies While Answering Fan Questions',
                            description: 'Keanu Reeves plays with puppies and answers questions',
                            thumbnails:  {
                                standard: {
                                    url: 'https://i.ytimg.com/vi/rOqUiXhECos/sddefault.jpg'
                                }
                            }
                        },
                        contentDetails: {
                            duration: 'PT5M27S'
                        },
                        statistics: {
                            viewCount: '18030191'
                        }
                    }
                ]
            }
            )
        });

        fetchSpy.mockResolvedValueOnce({
            ok:   true,
            json: () => ({
                items: [
                    {
                        snippet: {
                            publishedAt: '2019-05-18T15:00:13Z',
                            title:       'Keanu Reeves kills people responsible for stealing his Puppies While Answering Fan Questions',
                            description: 'Keanu Reeves killing the people responsible for stealing his Puppies While Answering Fan Questions',
                            thumbnails:  {
                                standard: {
                                    url: 'https://i.ytimg.com/vi/rOqUiXhECos/sddefault.jpg'
                                }
                            }
                        },
                        contentDetails: {
                            duration: 'PT5M27S'
                        },
                        statistics: {
                            viewCount: '999999999'
                        }
                    }
                ]
            }
            )
        });

        const {hydrateAsyncData} = require('../hydrateAsyncData');
        await hydrateAsyncData();

        expect(fetchSpy).toHaveBeenCalledTimes(2);

        const {writeFile} = require('fs-extra');

        expect(writeFile.mock.calls[0][1]).toMatchSnapshot();
        expect(writeFile.mock.calls[1][1]).toMatchSnapshot();
    });

    test('should not break if fetch fails', async() => {
        const fetchSpy = jest.spyOn(global, 'fetch');

        fetchSpy.mockRejectedValueOnce(new Error('fetch failed'));

        const {hydrateAsyncData} = require('../hydrateAsyncData');
        await hydrateAsyncData();

        expect(() => hydrateAsyncData()).not.toThrow();
    });
});
