const fs = require('fs-extra');
const {hydrateImages} = require('../hydrateImages');

jest.mock('fs-extra');
jest.mock('../../../config');

const siteConfig = require('../../../config').config.services.sanity;

describe('hydrateImages', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should checkForImages and write updated routes and siteConfig', async() => {
        const routes = {testRoute: {i18n: {en: {page: {i18n: {en: {content: 'some content'}}}}}}};
        const images = {testImage: {width: 100, height: 200}};

        jest.mock(`${siteConfig.studioDataOutputPath}/siteConfig.json`, () => ({language: 'en'}), {virtual: true});
        jest.mock(`${siteConfig.newStudioDataOutputPath}/routes.json`, () => routes, {virtual: true});
        jest.mock(`${siteConfig.newStudioDataOutputPath}/images`, () => images, {virtual: true});

        const fsExtraMock = jest.spyOn(fs, 'writeFile').mockImplementation(() => Promise.resolve());

        await hydrateImages();

        expect(fsExtraMock).toHaveBeenCalledWith(`${siteConfig.newStudioDataOutputPath}/routes.json`, JSON.stringify(routes));
        expect(fsExtraMock).toHaveBeenCalledWith(
            `${siteConfig.studioDataOutputPath}/siteConfig.json`,
            JSON.stringify(require(`${siteConfig.studioDataOutputPath}/siteConfig.json`))
        );
    });
});
