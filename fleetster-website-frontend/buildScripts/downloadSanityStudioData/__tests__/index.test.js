import fs from 'fs-extra';

import {config} from '../../../config';
import {writeImageData} from '../writeImageData';
import {writeRoutes} from '../writeRoutes';
import {writeSiteConfig} from '../writeSiteConfig';
import {writeChangelogs} from '../writeChangelogs';
import {hydrateBreadCrumbs} from '../hydrateBreadCrumbs';
import {writeNews} from '../writeNews';
import {hydrateRoutes} from '../hydrateRoutes';
import {hydrateImages} from '../hydrateImages';
import {hydrateAsyncData} from '../hydrateAsyncData';
import {downloadSanityStudioData} from '../index';

jest.mock('fs-extra', () => ({ensureDir: jest.fn()}));

jest.mock('../../../config', () => ({config: {...jest.requireActual('../../../config').config, services: {sanity: {newStudioDataOutputPath: '/path'}}}}));
jest.mock('../writeImageData', () => ({writeImageData: jest.fn()}));
jest.mock('../writeRoutes', () => ({writeRoutes: jest.fn()}));
jest.mock('../writeSiteConfig', () => ({writeSiteConfig: jest.fn()}));
jest.mock('../writeChangelogs', () => ({writeChangelogs: jest.fn()}));
jest.mock('../hydrateBreadCrumbs', () => ({hydrateBreadCrumbs: jest.fn()}));
jest.mock('../writeNews', () => ({writeNews: jest.fn()}));
jest.mock('../hydrateRoutes', () => ({hydrateRoutes: jest.fn()}));
jest.mock('../hydrateImages', () => ({hydrateImages: jest.fn()}));
jest.mock('../hydrateAsyncData', () => ({hydrateAsyncData: jest.fn()}));

describe('downloadSanityStudioData', () => {
    it('should call all functions once', async() => {
        const exitSpy = jest.spyOn(process, 'exit').mockImplementation(() => {
        });

        await downloadSanityStudioData();

        expect(fs.ensureDir).toHaveBeenCalledWith(`${config.services.sanity.newStudioDataOutputPath}`);
        expect(writeSiteConfig).toHaveBeenCalledTimes(1);
        expect(writeRoutes).toHaveBeenCalledTimes(1);
        expect(writeImageData).toHaveBeenCalledTimes(1);
        expect(writeChangelogs).toHaveBeenCalledTimes(1);
        expect(writeNews).toHaveBeenCalledTimes(1);
        expect(hydrateBreadCrumbs).toHaveBeenCalledTimes(1);
        expect(hydrateRoutes).toHaveBeenCalledTimes(1);
        expect(hydrateImages).toHaveBeenCalledTimes(1);
        expect(hydrateAsyncData).toHaveBeenCalledTimes(1);
        expect(exitSpy).not.toHaveBeenCalled();
        exitSpy.mockRestore();
    });

    it('should terminate the process when an error occurs', async() => {
        const exitSpy = jest.spyOn(process, 'exit').mockImplementation(() => {
        });

        const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {
        });

        writeSiteConfig.mockRejectedValueOnce(new Error('Test error'));

        await downloadSanityStudioData();

        expect(exitSpy).toHaveBeenCalledWith(1);
        expect(consoleSpy).toHaveBeenCalledWith(new Error('Test error'));

        consoleSpy.mockRestore();
        exitSpy.mockRestore();
    });
});
