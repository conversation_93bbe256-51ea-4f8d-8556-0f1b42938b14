const fs = require('fs-extra');
const moment = require('moment');
const _ = require('lodash');

const {changelogUrlMap} = require('./constants');

const {fetchDocuments} = require('../../src/node_modules/sanity-client-adapter');
const {config} = require('../../config');
const {removeEndSlashFromUrl, getChangelogMetaTitleAndDescription} = require('./utils');

const sanityConfig = config.services.sanity;
const languages = config.languages;
const language = config.language;

function getChangelogs() {
    const query = `*[_type == "changelog" && !(_id match "drafts*") && language == $language] {
        _id,
        _type,
        _updatedAt,
        tags,
        language
    }`;

    return fetchDocuments(query, {language});
}

function setPaginationData(releaseDate, content, type) {
    const quarter = moment(releaseDate).quarter();
    const year = moment(releaseDate).year();
    const pageSlug = `${changelogUrlMap[content.language]}/q${quarter}-${year}`;

    _.set(content, 'quarter', quarter);
    _.set(content, 'year', year);
    _.set(content, 'pageSlug', pageSlug);
    _.set(content, '_type', type);
}

function getPaginationData(routes) {
    const changelogs = _(routes).values().map('content').flatten().compact().value();
    const data = _.map(_.uniqBy(changelogs, 'pageSlug'), changelog => {
        const {year, quarter, pageSlug} = changelog;

        return {year, quarter, pageSlug};
    });

    return _.orderBy(data, ['year', 'quarter'], ['desc', 'desc']);
}

const MINIMAL_INITIAL_CONTENT = 5;

// eslint-disable-next-line max-params
function reduceByQuarter(result, item, index, allItems) {
    let belowMinimumContentLength = result.length < MINIMAL_INITIAL_CONTENT;
    let changeLogInQuarter = item.year === allItems[0].year && item.quarter === allItems[0].quarter;
    if (belowMinimumContentLength || changeLogInQuarter) {
        return result.concat(_.assign(item, {isIndex: true}));
    } else {
        return result;
    }
}

function changeLogReleaseDate(content) {
    return moment(content.releaseDate).format('YYYY-MM-DD');
}

function getIndexContent(items) {
    return _(items)
        .values()
        .map('content')
        .flatten()
        .orderBy(changeLogReleaseDate, ['desc'])
        .reduce(reduceByQuarter, []);
}

function orderRouteContents(routes) {
    return _.transform(routes, (result, value, key) => {
        _.set(result, [key, 'content'], _.orderBy(value.content, content => {
            return moment(content.releaseDate, 'YYYY-MM-DD');
        }, ['desc']));

        _.set(result, [key, '_updatedAt'], value._updatedAt);
    }, {});
}

function generateChangelogsJson(sanityChangelogs, lang) {
    const {host} = require(`${sanityConfig.studioDataOutputPath}/siteConfig.json`);

    let routes = _.transform(sanityChangelogs, (routes, changelog) => {
        let content = _.get(changelog, `i18n.${lang}`);

        if (!content) { return routes; }

        content.tags = changelog.tags;
        content.language = lang;

        const releaseDate = _.get(changelog, `i18n.${lang}.releaseDate`);
        setPaginationData(releaseDate, content, changelog._type);

        if (routes[content.pageSlug]) {
            routes[content.pageSlug].content = [...routes[content.pageSlug].content, content];
        } else {
            routes[content.pageSlug] = {content: [content], _updatedAt: changelog._updatedAt};
        }

        return routes;
    }, {});

    const orderedRoutes = orderRouteContents(routes);
    const pagination = getPaginationData(orderedRoutes);
    const indexContent = getIndexContent(orderedRoutes);
    const indexChangelogPageSlug = changelogUrlMap[lang];

    _.forEach(orderedRoutes, (route, slug) => {
        route.pagination = pagination;
        route.title = getChangelogMetaTitleAndDescription(route.content, slug).title;
        route.description = getChangelogMetaTitleAndDescription(route.content, slug).description;
        route.canonical = removeEndSlashFromUrl(`${host.url}/${slug}`);
    });

    const {title, description} = getChangelogMetaTitleAndDescription(indexContent, indexChangelogPageSlug);

    orderedRoutes[indexChangelogPageSlug] = {
        content:    indexContent,
        title,
        description,
        isIndex:    true,
        canonical:  removeEndSlashFromUrl(`${host.url}/${indexChangelogPageSlug}`),
        _updatedAt: moment().toDate(),
        pagination
    };

    return {routes: orderedRoutes, pagination};
}

// ToDo generate changelogs only for the site language
async function writeChangelogs() {
    const sanityChangelogs = await getChangelogs();

    const changelogs = {i18n: {}};

    for (const lang of languages) {
        changelogs.i18n[lang.label.toLowerCase()] = generateChangelogsJson(sanityChangelogs, lang.label.toLowerCase());
    }

    await fs.writeFile(`${sanityConfig.newStudioDataOutputPath}/changelog.json`, JSON.stringify(changelogs));
}

module.exports = {writeChangelogs};
