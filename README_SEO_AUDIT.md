# Technical SEO & Structure Audit (fleetster.de)

This document summarizes what was checked on https://www.fleetster.de/ and the issues found, with concrete next steps.

## What was checked

- Redirects
  - HTTP → HTTPS
  - non‑www → www
- Homepage meta
  - Title, meta description, canonical, hreflang
- robots.txt
  - Availability and presence of Sitemap directive
- sitemap.xml
  - Availability and sampling of URLs
- Page-level checks on a sample of 120 URLs (out of 271 in sitemap)
  - HTTP status
  - Canonical presence and match vs resolved URL (trailing-slash policy)
  - Noindex pages included in sitemap (should not be)
  - Hreflang presence and sampled link reachability
  - Shallow internal link integrity (up to 8 internal links per page)
  - Mixed-content detection (http assets on https pages)
- 404 behavior (quick probe)

Scripts added to run these checks:
- scripts/seo_quick_check.py — quick probes (redirects, homepage meta, robots, sitemap sample, 404)
- scripts/seo_deep_check2.py — deeper sampling and validation

How to run:
- python3 scripts/seo_quick_check.py
- python3 scripts/seo_deep_check2.py

## Findings

- Overall: Technical health is strong across core areas. The main improvement area is canonical URL consistency.

1) Canonical trailing slash mismatch
- Observed: Canonicals are emitted without trailing slash, while pages resolve with a trailing slash.
- Evidence (sample of many similar cases):
  - Page: https://www.fleetster.de/fuhrpark-wissen/faq
    - canonical: https://www.fleetster.de/fuhrpark-wissen/faq
    - expected: https://www.fleetster.de/fuhrpark-wissen/faq/
- Impact: Mixed canonical signals and potential for duplicate URL interpretations.

2) non‑www redirect target includes :443
- Observed: https://fleetster.de/ 301 redirects to https://www.fleetster.de:443/
- Impact: Functional but suboptimal; best practice is to omit the explicit port in Location.

Everything else in the sampled set looked good:
- No HTTP errors for sampled pages
- Canonical present on all sampled pages
- No noindex URLs included in sitemap sample
- Hreflang present and reachable (sampled)
- No mixed-content references
- No broken internal links in shallow checks
- 404: returns 404 and uses meta robots noindex,follow

## Fixes implemented in codebase

- Canonical normalization at render time to include a trailing slash for directory-like pages, aligned with Next.js trailingSlash: true.
  - Helper added: fleetster-website-frontend/src/pageComponents/ensureCanonical.js
  - Applied in:
    - fleetster-website-frontend/src/pageComponents/regularPage.js
    - fleetster-website-frontend/src/pageComponents/newsPage.js
    - fleetster-website-frontend/src/pageComponents/changelogPage.js
  - Rationale: Minimal, low-risk change to align canonicals with resolved URLs. Preview canonicals (with query strings) remain unchanged.

Post-deploy verification:
- Run: python3 scripts/seo_deep_check2.py
- Expected: Trailing-slash-only canonical mismatches should be 0.

## Recommended next steps

1) Edge redirect normalization (non‑www → www)
- Update the rule so Location omits :443 and points to https://www.fleetster.de/ only.
- Provide your edge platform (CloudFront, Nginx, Netlify, etc.), and we will supply an exact snippet.

2) Optional: Emit canonicals with slashes at source
- If preferred, update rf-static to generate trailing-slash canonicals (instead of adjusting at render). This centralizes the policy.

3) Optional: Expand checks to full sitemap
- Increase the --limit in scripts/seo_deep_check2.py to cover all URLs and export a CSV for archival.

## Appendix (commands)

- Quick check: python3 scripts/seo_quick_check.py
- Deep check (defaults): python3 scripts/seo_deep_check2.py
- Deep check (full): python3 scripts/seo_deep_check2.py --limit 300 --links-per-page 8 --sample-hreflang 3

