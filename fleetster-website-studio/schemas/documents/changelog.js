import {MdNewReleases} from 'react-icons/md';

import CopyAndTranslateButton from '../../components/copyAndTranslateButton';
import TagInput from '../../components/tagInput';
import {defaultChangelogOrdering} from '../../deskMenus/constants';
import {validateVideoUrl} from '../shared/rf-validators';

export const changelog = {
    name:   'changelog',
    type:   'document',
    title:  'Changelog',
    icon:   MdNewReleases,
    fields: [
        {
            name:       'copyFrom',
            type:       'string',
            components: {
                input: CopyAndTranslateButton
            },
            options: {
                canvasApp: {
                    exclude: true
                }
            }
        },
        {
            name:  'tags',
            title: 'Tags',
            type:  'array',
            of:    [{
                type:   'object', title:  'Tags', fields: [
                    {title: 'label', name: 'label', type: 'string'},
                    {title: 'value', name: 'value', type: 'string'}
                ]
            }],
            components: {
                input: TagInput
            }
        },
        {
            name:  'title',
            type:  'string',
            title: 'Title'
        },
        {
            name:  'releaseDate',
            type:  'date',
            title: 'Release date'
        },
        {
            name:  'releaseVersion',
            type:  'string',
            title: 'Release version'
        },
        {
            name:  'content',
            type:  'simplePortableText',
            title: 'Content'
        },
        {
            name:  'callToAction',
            type:  'callToAction',
            title: 'Call to action'
        },
        {
            name:    'image',
            type:    'image',
            title:   'Image',
            options: {
                hotspot: true
            }
        },
        {
            name:  'imageAlt',
            type:  'string',
            title: 'Image Alt Text'
        },
        {
            name:       'videoUrl',
            type:       'string',
            title:      'Video URL (YouTube only)',
            validation: Rule => Rule.custom(link => validateVideoUrl({href: link}))
        },
        {
            name:        'description',
            type:        'text',
            title:       'Description',
            description: 'This description populates meta-tags on the changelog page'
        },
        {
            name:   'language',
            type:   'string',
            hidden: true
        }
    ],

    preview: {
        select: {title: 'title', releaseVersion: 'releaseVersion'},
        prepare({title, releaseVersion}) {
            return {title, subtitle: `Release: ${releaseVersion}`};
        }
    },
    orderings: [{title: 'Release version', name: 'releaseVersionDesc', by: defaultChangelogOrdering}]
};
