import {BiLink} from 'react-icons/bi';

import {isUniqueOtherThanLanguage, validateDisallowRobotsAndIncludeInSitemap, validateSlug} from '../shared/rf-validators';

export const route = {
    name:   'route',
    type:   'document',
    title:  'Route',
    icon:   BiLink,
    fields: [
        {
            name:       'slug',
            type:       'slug',
            title:      'Slug',
            validation: Rule => Rule.custom(validateSlug),
            options:    {
                isUnique: isUniqueOtherThanLanguage
            }
        },
        {
            name:        'page',
            type:        'reference',
            description: 'Select the page that this route should point to.',
            to:          [
                {
                    type: 'page'
                }
            ],
            options: {
                filter: ({document}) => {
                    return {
                        filter: '_type == "page" && language == $language',
                        params: {language: document.language}
                    };
                }
            }
        },
        {
            name:        'includeInSitemap',
            type:        'boolean',
            title:       'Include page in sitemap',
            description: 'For search engines. Will be added to /sitemap.xml.',
            validation:  Rule => Rule.custom((value, schema) => validateDisallowRobotsAndIncludeInSitemap(schema))
        },
        {
            name:        'disallowRobots',
            type:        'boolean',
            title:       'Disallow in robots.txt',
            description: 'Hide this route for search engines.'
        },
        {
            name:        'includeInNews',
            type:        'boolean',
            title:       'Include page in news section',
            description: 'Display the article section as an entry in the news section'
        },
        {
            name:   'language',
            type:   'string',
            hidden: true
        }
    ],

    preview: {
        select: {
            slug:      'slug.current',
            page:      'page',
            pageTitle: 'page.title'
        },
        prepare({slug, page, pageTitle}) {
            pageTitle = pageTitle || 'Untitled';

            return {
                title:    slug === '/' ? '/' : `/${slug}`,
                subtitle: `Page: ${page ? pageTitle : 'Not set'}`
            };
        }
    }
};
