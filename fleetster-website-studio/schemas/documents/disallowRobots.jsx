/* eslint-disable react/no-unescaped-entities */
import React from 'react';
import {BiBot} from 'react-icons/bi';

import {validateURI} from '../shared/rf-validators';

export const disallowRobots = {
    name:   'disallow-robots',
    type:   'document',
    title:  'Disallow robots',
    icon:   BiBot,
    options: {
        canvasApp: {
            exclude: true
        }
    },
    fields: [
        {
            name:        'uri',
            type:        'string',
            title:       'URI',
            validation:  Rule => Rule.custom(validateURI),
            description: (
                <>
                    This URI will be included in the DISALLOW section of the robots.txt file. Careful with what you put here.
                    <br/>
                    <br/>
                    For reference, please see&nbsp;
                    <a href={'https://developers.google.com/search/docs/crawling-indexing/robots/robots_txt#url-matching-based-on-path-values'}
                        target={'_blank'} rel={'noreferrer'}>
                        Google's robots.txt documentation
                    </a>
                </>
            )
        },
        {
            name:   'language',
            type:   'string',
            hidden: true
        }
    ]
};
