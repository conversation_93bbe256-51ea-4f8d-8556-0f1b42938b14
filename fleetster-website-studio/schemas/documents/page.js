import CopyAndTranslateButton from '../../components/copyAndTranslateButton';

export const page = {
    name:   'page',
    type:   'document',
    title:  'Page',
    fields: [
        {
            name:       'copyFromLanguage',
            type:       'string',
            components: {
                input: CopyAndTranslateButton
            },
            options: {
                canvasApp: {
                    exclude: true
                }
            }
        },
        {
            name:  'title',
            type:  'string',
            title: 'Title'
        },
        {
            name:  'content',
            type:  'array',
            title: 'Page sections',
            of:    [
                {type: 'hero'},
                {type: 'articleHeadingSection'},
                {type: 'signupForm'},
                {type: 'additionalInformationForm'},
                {type: 'desiredFeaturesForm'},
                {type: 'textSection'},
                {type: 'imageTextSection'},
                {type: 'featuresSection'},
                {type: 'featuresComparisonSection'},
                {type: 'productPricingSection'},
                {type: 'videoSection'},
                {type: 'ctaSection'},
                {type: 'productStructuredData'},
                {type: 'resellerSignupForm'},
                {type: 'customerInformationForm'},
                {type: 'productOverviewSection'},
                {type: 'imagesSection'},
                {type: 'titleSection'},
                {type: 'contactCardsSection'}
            ]
        },
        {
            name:        'description',
            type:        'text',
            title:       'Description',
            description: 'This description populates meta-tags on the webpage'
        },
        {
            name:        'openGraphImage',
            type:        'image',
            title:       'Open Graph Image',
            description: 'Image for sharing previews on Facebook, Twitter etc.'
        },
        {
            name:   'language',
            type:   'string',
            hidden: true
        }
    ],
    preview: {
        select: {
            title: 'title',
            lang:  'language',
            media: 'openGraphImage'
        },
        prepare({title, lang, media}) {
            return {
                title,
                subtitle: `Language: ${lang.toUpperCase()}`,
                media
            };
        }
    }
};
