export const siteConfig = {
    name:    'site-config',
    type:    'document',
    title:   'Site configuration',
    options: {
        canvasApp: {
            exclude: true
        }
    },
    fields: [
        {
            name:  'title',
            type:  'string',
            title: 'Site title'
        },
        {
            title: 'Brand logo',
            description:
        'Best choice is to use an SVG where the color are set with currentColor',
            name:   'logo',
            type:   'image',
            fields: [
                {
                    name:        'alt',
                    type:        'string',
                    title:       'Alternative text',
                    description: 'Important for SEO and accessiblity.'
                }
            ]
        },
        {
            title: 'Footer Brand logo',
            description:
                'Best choice is to use an SVG where the color is set with currentColor',
            name:   'footerLogo',
            type:   'image',
            fields: [
                {
                    name:        'alt',
                    type:        'string',
                    title:       'Alternative text',
                    description: 'Important for SEO and accessiblity.'
                }
            ]
        },
        {
            name:        'homePage',
            type:        'reference',
            description: 'Select the website\'s home page',
            to:          [{type: 'page'}],
            options:     {
                filter: ({document}) => {
                    return {
                        filter: '_type == "page" && language == $language',
                        params: {language: document.language}
                    };
                }
            }
        },
        {
            title: 'Main navigation',
            name:  'mainNavigation',
            type:  'mainNavigation'
        },
        {
            title: 'Footer',
            name:  'footer',
            type:  'footer'
        },
        {
            title: 'Product Updates Meta description',
            name:  'changelogDescription',
            type:  'string'
        },
        {
            name:  'socialMediaLinks',
            title: 'Social Media Links',
            type:  'array',
            of:    [{type: 'socialMediaLink'}]
        },
        {
            name:        'salesEndpoints',
            title:       'Sales Service Endpoints',
            description: 'Endpoints for the sales service',
            type:        'array',
            of:          [{type: 'endpointOption'}],
            validation:  Rule => Rule.required().min(1).custom((fields = {}) => {
                const defaultEndpoints = fields.filter(endpoint => endpoint.default);
                if (defaultEndpoints.length === 0) {
                    return 'At least one default endpoint is required';
                }

                if (defaultEndpoints.length > 1) {
                    return 'Only one default endpoint is allowed';
                }

                return true;
            })
        },
        {
            name:   'language',
            type:   'string',
            hidden: true
        }
    ]
};
