import {BiUnlink} from 'react-icons/bi';

import {validateSlug} from '../shared/rf-validators';

export const redirect = {
    name:    'redirect',
    type:    'document',
    title:   'Redirect',
    icon:    BiUnlink,
    options: {
        canvasApp: {
            exclude: true
        }
    },
    fields: [
        {
            name:       'originalUrl',
            type:       'slug',
            title:      'Original URL',
            validation: Rule => Rule.custom(validateSlug),
            options:    {
                isUnique: () => true
            }
        },
        {
            name:       'redirectUrl',
            type:       'slug',
            title:      'Redirect URL',
            validation: Rule => Rule.custom(validateSlug),
            options:    {
                isUnique: () => true
            }
        },
        {
            name:   'language',
            type:   'string',
            hidden: true
        }
    ],

    preview: {
        select: {
            originalUrl: 'originalUrl.current',
            redirectUrl: 'redirectUrl.current'
        },
        prepare({originalUrl, redirectUrl}) {
            return {
                title:    originalUrl,
                subtitle: `Redirects to: ${redirectUrl}`
            };
        }
    }
};
