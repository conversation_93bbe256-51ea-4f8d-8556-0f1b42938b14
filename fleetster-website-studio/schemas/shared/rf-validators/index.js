import _ from 'lodash';

const validationMessages = {
    slugOnlyAlphanumeric:     'Slugs can only contain alphanumeric characters, dashes and slashes',
    slugCannotBeginWithSlash: 'Slugs must not begin with a slash character',
    uriBeginWithSlash:        'This URI must begin with a slash character',
    invalidVideoUrl:          'Wrong format. Expected: https://www.youtube.com/watch?v=<video-id>[&otherParams=value]' +
                              ' or https://vimeo.com/<video-id>',
    disallowRobotsAndIncludeInSitemapNotAllowed: 'Disallow robots and include in sitemap cannot both be checked'
};

const onlyAlphanumeric = /[^a-z0-9\-/]/;
const beginsWithSlash = /^\//;

export async function isUniqueOtherThanLanguage(slug, context) {
    const {document, getClient} = context;
    if (!document?.language) {
        return true;
    }
    const client = getClient({apiVersion: '2022-01-21'});
    const id = document._id.replace(/^drafts\./, '');
    const params = {
        draft:     `drafts.${id}`,
        published: id,
        language:  document.language,
        slug
    };
    const query = `!defined(*[
        !(_id in [$draft, $published]) &&
        slug.current == $slug &&
        language == $language
    ][0]._id)`;
    const result = await client.fetch(query, params);

    return result;
}

export function validateSlug(slug) {
    if (onlyAlphanumeric.test(slug.current)) {
        return validationMessages.slugOnlyAlphanumeric;
    } else if (beginsWithSlash.test(slug.current)) {
        return validationMessages.slugCannotBeginWithSlash;
    }

    return true;
}

export function validateURI(uri) {
    if (uri && !beginsWithSlash.test(uri)) {
        return validationMessages.uriBeginWithSlash;
    }

    return true;
}

const youtubeUrlRegex = /https:\/\/www.youtube.com\/watch\?v=(.+?)(&|$)/;
const vimeoUrlRegex = /https:\/\/vimeo.com\/\d+$/;

export function validateVideoUrl(link) {
    const url = _.get(link, 'href');

    if (!url) { return true; }

    if (!youtubeUrlRegex.test(url) && !vimeoUrlRegex.test(url)) {
        return validationMessages.invalidVideoUrl;
    }

    return true;
}

export function validateDisallowRobotsAndIncludeInSitemap(schema) {
    const disallowRobots = _.get(schema, 'disallowRobots', false);
    const includeInSitemap = _.get(schema, 'includeInSitemap', false);

    if (disallowRobots && includeInSitemap) {
        return validationMessages.disallowRobotsAndIncludeInSitemapNotAllowed;
    }

    return true;
}
