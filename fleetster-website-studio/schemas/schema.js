/* eslint-disable max-lines */
// Document types
import {page} from './documents/page';
import {route} from './documents/route';
import {redirect} from './documents/redirect';
import {changelog} from './documents/changelog';
import {siteConfig} from './documents/siteConfig';
import {disallowRobots} from './documents/disallowRobots';

// Object types
import callToAction from './objects/callToAction';
import embedHTML from './objects/embedHTML';
import figure from './objects/figure';
import internalLink from './objects/internalLink';
import link from './objects/link';
import portableText from './objects/portableText';
import pricingCard from './objects/pricingCard';
import simplePortableText from './objects/simplePortableText';
import brandStructuredData from './objects/structuredData/brandStructuredData';
import manufacturerStructuredData from './objects/structuredData/manufacturerStructuredData';
import offerStructuredData from './objects/structuredData/offerStructuredData';
import locationStructuredData from './objects/structuredData/locationStructuredData';
import productStructuredData from './objects/structuredData/productStructuredData';
import dropdownMenuItem from './objects/dropdownMenuItem';
import dropdownMenu from './objects/dropdownMenu';
import navigationLink from './objects/navigationLink';
import headerLink from './objects/headerLink';
import mainNavigation from './objects/mainNavigation';
import footer from './objects/footer';
import footerSegment from './objects/footerSegment';
import socialMediaLink from './objects/socialMediaLink';
import tag from './objects/tag';
import iconTextItem from './objects/iconTextItem';
import productOverview from './objects/productOverview';
import productOverviewSection from './objects/sections/productOverviewSection';
import heroImage from './objects/heroImage';
import contactCard from './objects/contactCard';
import iconTextLink from './objects/iconTextLink';
import iconText from './objects/iconText';
import pricingPeriod from './objects/pricingPeriod';
import actionButton from './objects/actionButton';

// Page sections
import hero from './objects/sections/hero';
import {resellerSignupForm} from './objects/sections/resellerSignupForm';
import {customerInformationForm} from './objects/sections/customerInformationForm';
import {signupForm} from './objects/sections/signupForm';
import {additionalInformationForm} from './objects/sections/additionalInformationForm';
import {desiredFeaturesForm} from './objects/sections/desiredFeaturesForm';
import productPricingSection from './objects/sections/productPricingSection';
import textSection from './objects/sections/textSection';
import featuresSection from './objects/sections/featuresSection';
import featuresComparisonSection from './objects/sections/featuresComparisonSection';
import feature from './objects/feature';
import imageTextSection from './objects/sections/imageTextSection';
import videoSection from './objects/sections/videoSection';
import ctaSection from './objects/sections/ctaSection';
import articleHeadingSection from './objects/sections/articleHeadingSection';
import imagesSection from './objects/sections/imagesSection';
import titleSection from './objects/sections/titleSection';
import contactCardsSection from './objects/sections/contactCardsSection';
import offerChildStructuredData from './objects/structuredData/offerChildStructuredData';
import endpointOption from './objects/endpointOption';

export const translatedSchemas = ['page', 'route', 'redirect', 'site-config', 'changelog', 'disallow-robots'];

export default [
    // Document types
    page,
    route,
    redirect,
    changelog,
    siteConfig,
    disallowRobots,

    // Object types
    dropdownMenuItem,
    dropdownMenu,
    navigationLink,
    headerLink,
    mainNavigation,
    footer,
    footerSegment,
    socialMediaLink,
    tag,
    callToAction,
    embedHTML,
    figure,
    internalLink,
    link,
    pricingCard,
    pricingPeriod,
    portableText,
    simplePortableText,
    locationStructuredData,
    brandStructuredData,
    manufacturerStructuredData,
    offerStructuredData,
    offerChildStructuredData,
    productStructuredData,
    iconTextItem,
    productOverview,
    productOverviewSection,
    heroImage,
    contactCard,
    iconTextLink,
    iconText,
    actionButton,
    endpointOption,

    // Page sections
    hero,
    textSection,
    productPricingSection,
    featuresComparisonSection,
    featuresSection,
    feature,
    imageTextSection,
    videoSection,
    ctaSection,
    articleHeadingSection,
    resellerSignupForm,
    customerInformationForm,
    signupForm,
    additionalInformationForm,
    desiredFeaturesForm,
    imagesSection,
    titleSection,
    contactCardsSection
];
