export default {
    name:   'iconText',
    title:  'Icon Text',
    type:   'object',
    fields: [
        {
            title: 'Icon',
            name:  'icon',
            type:  'image'
        },
        {
            name:        'iconAlt',
            type:        'string',
            title:       'Alternative text',
            description: 'Important for SEO and accessiblity.'
        },
        {
            title: 'Text',
            name:  'description',
            type:  'string'
        }
    ],
    preview: {
        select: {
            description: 'description'
        },
        prepare({description}) {
            return {
                title: description
            };
        }
    }
};
