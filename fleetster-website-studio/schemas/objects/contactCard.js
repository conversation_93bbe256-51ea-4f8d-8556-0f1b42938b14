export default {
    title: 'Contact Card',
    name:  'contactCard',
    type:  'object',

    fieldsets: [
        {
            title: '<PERSON>',
            name:  'link'
        }
    ],

    fields: [
        {
            title: 'Sanity Display Text',
            name:  'sanityDisplay',
            type:  'string'
        },
        {
            title: 'Name',
            name:  'name',
            type:  'string'
        },
        {
            title: 'Function',
            name:  'role',
            type:  'string'
        },
        {
            name:    'image',
            type:    'image',
            title:   'Avatar Image',
            options: {
                hotspot: true
            }
        },
        {
            name:    'backgroundImage',
            type:    'image',
            title:   'Background Image',
            options: {
                hotspot: true
            }
        },
        {
            name:       'itemList',
            type:       'array',
            validation: Rule => Rule.max(4).error('Contact card should have at most 4 items'),
            of:         [{type: 'iconText'}]
        },
        {
            name:       'buttons',
            type:       'array',
            validation: Rule => Rule.max(2).error('Contact card should have at most 2 buttons'),
            of:         [{type: 'iconTextLink'}]
        }
    ],

    preview: {
        select: {
            sanityDisplay: 'sanityDisplay'
        },
        prepare({sanityDisplay}) {
            return {
                title: sanityDisplay
            };
        }
    }
};
