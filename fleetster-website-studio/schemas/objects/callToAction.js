export default {
    title:      'Call to action',
    name:       'callToAction',
    type:       'object',
    validation: Rule => Rule.custom(
        (fields = {}) => !fields.route || !fields.link || 'Only one link type is allowed'
    ),

    fieldsets: [
        {
            title: 'Link',
            name:  'link'
        }
    ],

    fields: [
        {
            title: 'Title',
            name:  'title',
            type:  'string'
        },
        {
            title: 'Prefixed Text',
            name:  'prefixedText',
            type:  'string'
        },
        {
            title:       'Internal link',
            description: 'Use this to link between pages on the website',
            name:        'route',
            type:        'reference',
            to:          [{type: 'route'}],
            fieldset:    'link',
            options:     {
                filter: ({document}) => {
                    return {
                        filter: '_type == "route" && language == $language',
                        params: {language: document.language}
                    };
                }
            }
        },
        {
            title:      'External link',
            name:       'link',
            type:       'url',
            fieldset:   'link',
            validation: Rule => Rule.uri({
                allowRelative: true,
                scheme:        ['https', 'http', 'mailto', 'tel']
            })
        }
    ],

    preview: {
        select: {
            title:      'title',
            routeTitle: 'route.title',
            slug:       'route.slug.current',
            link:       'link'
        },
        prepare({title, routeTitle = '', slug, link}) {
            let subtitleExtra;
            if (slug) {
                subtitleExtra = `Slug:/${slug}/`;
            } else {
                subtitleExtra = link ? `External link: ${link}` : 'Not set';
            }

            return {
                title:    `${title}`,
                subtitle: `${routeTitle} ${subtitleExtra}`
            };
        }
    }
};
