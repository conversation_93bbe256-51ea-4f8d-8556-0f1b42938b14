import {copyPaste} from '@superside-oss/sanity-plugin-copy-paste';

import {validateVideoUrl} from '../../shared/rf-validators';

export default {
    type:   'object',
    name:   'videoSection',
    title:  'Video Section',
    fields: [
        copyPaste,
        {
            name:  'heading',
            title: 'Heading',
            type:  'string',
            rows:  1
        },
        {
            name:  'content',
            title: 'Content',
            type:  'simplePortableText'
        },
        {
            name:       'videoUrl',
            type:       'link',
            title:      'Video URL (YouTube or Vimeo)',
            validation: Rule => Rule.custom(link => validateVideoUrl(link))
        },
        {
            name:  'sendToRight',
            title: 'Video is on the right side',
            type:  'boolean'
        },
        {
            name:  'callToAction',
            type:  'callToAction',
            title: 'Call to action'
        }
    ],
    preview: {
        select: {
            title: 'heading'
        },
        prepare({title}) {
            return {
                title,
                subtitle: 'Video section'
            };
        }
    }
};
