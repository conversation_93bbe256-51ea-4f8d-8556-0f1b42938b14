import {copyPaste} from '@superside-oss/sanity-plugin-copy-paste';

export default {
    type:       'object',
    name:       'productPricingSection',
    title:      'Product pricing',
    validation: Rule => Rule.custom(fields => {
        if (fields.periods?.filter(period => period.default).length > 1) {
            return 'Pricing periods must have only one default period';
        }

        return true;
    }),
    fields: [
        copyPaste,
        {
            name:  'productName',
            title: 'Plan/Product name',
            type:  'string'
        },
        {
            name: 'pricings',
            type: 'array',
            of:   [
                {
                    type:  'pricingCard',
                    title: 'Pricing card'
                }
            ],
            validation: Rule => Rule.required().max(3)
        },
        {
            name: 'periods',
            type: 'array',
            of:   [
                {
                    type:  'pricingPeriod',
                    title: 'Pricing period'
                }
            ]
        },
        {
            name:  'observationText',
            type:  'string',
            title: 'Observation text'
        },
        {
            name:  'backgroundImage',
            type:  'image',
            title: 'Section background image (optional)'
        }
    ],
    preview: {
        select: {
            title: 'productName'
        },
        prepare({title}) {
            return {
                title,
                subtitle: 'Product Pricing Section'
            };
        }
    }
};
