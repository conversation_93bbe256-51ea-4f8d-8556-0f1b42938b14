import {copyPaste} from '@superside-oss/sanity-plugin-copy-paste';

export const customerInformationForm = {
    type:   'object',
    name:   'customerInformationForm',
    title:  'Reseller Signup Form - Contact Data - Adds customer information',
    fields: [
        copyPaste,
        {
            name:  'title',
            type:  'string',
            title: 'Form Title'
        },
        {
            name:  'subtitle',
            type:  'string',
            title: 'Form Subtitle'
        },
        {
            name:        'sideImageTitle',
            type:        'string',
            title:       'Side image title',
            description: 'The title that will appear over the side image'
        },
        {
            title:       'Side image',
            description: 'The image that will accompany the form',
            name:        'sideImage',
            type:        'image',
            options:     {
                hotspot: true
            }
        },
        {
            name:        'sideImageAlt',
            type:        'string',
            title:       'Side image alt caption',
            description: 'The alternative text that explains the image'
        },
        {
            title:       'Next step',
            description: 'This will be the next page shown if the form submission succeeds',
            name:        'successURL',
            type:        'url'
        },
        {
            title:       'Privacy policy link',
            description: 'Link to the policy privacy documentation',
            name:        'privacyPolicyLink',
            type:        'url'
        },
        {
            title:       'Sales Service',
            description: 'The Sales Service that will be used to submit the form, if not set, the default German Sales Service will be used',
            name:        'salesEndpoint',
            type:        'salesEndpoint'
        }
    ],
    preview: {
        select: {
            title: 'title',
            media: 'backgroundImage'
        },
        prepare({title, media}) {
            return {
                title,
                media,
                subtitle: 'Signup Form'
            };
        }
    }
};
