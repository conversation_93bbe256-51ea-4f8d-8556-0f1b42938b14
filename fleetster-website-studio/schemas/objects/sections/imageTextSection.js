import {copyPaste} from '@superside-oss/sanity-plugin-copy-paste';

export default {
    type:   'object',
    name:   'imageTextSection',
    title:  'Image & Text Section',
    fields: [
        copyPaste,
        {
            name:  'heading',
            title: 'Heading',
            type:  'string',
            rows:  1
        },
        {
            name:  'content',
            title: 'Content',
            type:  'simplePortableText'
        },
        {
            name:    'image',
            type:    'image',
            title:   'Image',
            options: {
                hotspot: true
            }
        },
        {
            name:  'imageAlt',
            type:  'string',
            title: 'Image - Alternative Text'
        },
        {
            name:  'sendImageToRight',
            title: 'Image is on the right side',
            type:  'boolean'
        },
        {
            name:    'callToAction',
            type:    'callToAction',
            title:   'Call to action',
            options: {
                hotspot: true
            }
        }
    ],
    preview: {
        select: {
            title: 'heading',
            media: 'image'
        },
        prepare({title, media}) {
            return {
                title,
                subtitle: 'Image & Text Section',
                media
            };
        }
    }
};
