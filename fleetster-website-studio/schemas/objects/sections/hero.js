import {copyPaste} from '@superside-oss/sanity-plugin-copy-paste';

export default {
    type:  'object',
    name:  'hero',
    title: 'Hero',

    fields: [
        copyPaste,
        {
            name:  'heading',
            type:  'string',
            title: 'Heading'
        },
        {
            name:  'lightTheme',
            title: 'Light theme (title becomes white)',
            type:  'boolean'
        },
        {
            name:  'text',
            type:  'text',
            title: 'Text'
        },
        {
            name:    'backgroundImageLandscape',
            type:    'image',
            title:   'Background image',
            options: {
                hotspot: true
            }
        },
        {
            name: 'heroImages',
            type: 'array',
            of:   [{type: 'heroImage'}]
        },
        {
            name:    'callToAction',
            type:    'callToAction',
            title:   'Call to action',
            options: {
                hotspot: true
            }
        }
    ],

    preview: {
        select: {
            title: 'heading',
            media: 'backgroundImage'
        },
        prepare({title, media}) {
            return {
                title,
                subtitle: 'Hero section',
                media
            };
        }
    }
};
