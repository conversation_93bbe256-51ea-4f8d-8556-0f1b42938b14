import {copyPaste} from '@superside-oss/sanity-plugin-copy-paste';
import _ from 'lodash';

export default {
    type:  'object',
    name:  'featuresComparisonSection',
    title: 'Product features comparison table',

    fields: [
        copyPaste,
        {
            name:  'heading',
            type:  'string',
            title: 'Heading'
        },
        {
            title:       'Header Icons',
            description: 'The icon text must have an exact match within the table headers.',
            name:        'icons',
            type:        'array',
            of:          [{type: 'iconText'}],
            validation:  Rule => Rule.custom((icons, context) => {
                const {parent} = context;

                const headers = _.get(parent, 'featuresTable.rows[0].cells', []);

                let errorMessages = [];

                _.forEach(icons, ({description}, index) => {
                    if (!_.includes(headers, description)) {
                        errorMessages.push(`Icon ${index + 1} with text "${description}" is not present in the table headers.`);
                    }
                });

                if (errorMessages.length) {
                    return errorMessages.join(' \n');
                }

                return true;
            })
        },
        {
            name:  'featuresTable',
            type:  'table',
            title: 'Features Table'
        }
    ],

    preview: {
        select: {
            title: 'heading'
        },
        prepare({title}) {
            return {
                title,
                subtitle: 'Feature Comparison Table Section'
            };
        }
    }
};
