import {copyPaste} from '@superside-oss/sanity-plugin-copy-paste';

export default {
    name:   'imagesSection',
    title:  'Images section',
    type:   'object',
    fields: [
        copyPaste,
        {
            name:        'minimized',
            title:       'Mini image style',
            type:        'boolean',
            description: 'Mini-images need to be SVGs, regular ones cannot be SVG(messes up resizing)'
        },
        {
            title:       'Section content',
            name:        'content',
            description: 'Regular must have only one content section. Mini images may have two content sections',
            type:        'array',
            validation:  Rule => Rule.required().max(2),
            of:          [{
                type:        'object',
                title:       'Section content group',
                description: 'title with a group of images',
                fields:      [{
                    title:       'Title',
                    name:        'title',
                    type:        'string',
                    description: 'Write *text* between asterisks to make it bold.'
                },
                {
                    title: 'Images',
                    name:  'images',
                    type:  'array',
                    of:    [
                        {
                            type:       'object',
                            title:      'images list',
                            validation: Rule => Rule.custom(
                                (fields = {}) => !fields.route || !fields.link || 'Only one link type is allowed'
                            ),
                            fieldsets: [
                                {
                                    title: 'Link',
                                    name:  'link'
                                }
                            ],
                            fields: [
                                {
                                    title: 'Image',
                                    name:  'image',
                                    type:  'image'
                                },
                                {
                                    title: 'Alternative text',
                                    name:  'alt',
                                    type:  'string'
                                },
                                {
                                    title:       'Internal link',
                                    description: 'Use this to link between pages on the website',
                                    name:        'route',
                                    type:        'internalLink',
                                    fieldset:    'link'
                                },
                                {
                                    title:      'External link',
                                    name:       'link',
                                    type:       'url',
                                    validation: Rule => Rule.uri({
                                        allowRelative: true,
                                        scheme:        ['https', 'http', 'mailto', 'tel']
                                    }),
                                    fieldset: 'link'
                                }
                            ]
                        }
                    ]
                }]
            }]
        }],
    preview: {
        select: {
            title: 'content[0].title'
        },
        prepare({title}) {
            return {
                title,
                subtitle: 'Images section'
            };
        }
    }
};

