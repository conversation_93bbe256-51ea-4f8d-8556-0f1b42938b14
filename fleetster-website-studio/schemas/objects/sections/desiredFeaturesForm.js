import {copyPaste} from '@superside-oss/sanity-plugin-copy-paste';

export const desiredFeaturesForm = {
    type:   'object',
    name:   'desiredFeaturesForm',
    title:  'Follow up form - Features',
    fields: [
        copyPaste,
        {
            name:  'title',
            type:  'string',
            title: 'Form Title'
        },
        {
            name:  'subtitle',
            type:  'string',
            title: 'Form Subtitle'
        },
        {
            name:        'sideImageTitle',
            type:        'string',
            title:       'Side image title',
            description: 'The title that will appear over the side image'
        },
        {
            title:       'Side image',
            description: 'The image that will accompany the form',
            name:        'sideImage',
            type:        'image',
            options:     {
                hotspot: true
            }
        },
        {
            name:        'sideImageAlt',
            type:        'string',
            title:       'Side image alt caption',
            description: 'The alternative text that explains the image'
        },
        {
            title:       'Next step',
            description: 'This will be the next page shown if the form submission succeeds',
            name:        'successURL',
            type:        'url'
        },
        {
            title:       'Redirect url',
            description: 'The url to which the user will be redirected to if the hashedId parameter is not present in the url',
            name:        'redirectURL',
            type:        'url'
        },
        {
            title:       'Sales Service',
            description: 'The Sales Service that will be used to submit the form, if not set, the default German Sales Service will be used',
            name:        'salesEndpoint',
            type:        'salesEndpoint'
        }
    ],
    preview: {
        select: {
            title: 'title',
            media: 'backgroundImage'
        },
        prepare({title, media}) {
            return {
                title,
                subtitle: 'Desired features form',
                media
            };
        }
    }
};
