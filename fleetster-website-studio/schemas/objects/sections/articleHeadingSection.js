import {copyPaste} from '@superside-oss/sanity-plugin-copy-paste';

export default {
    type:   'object',
    name:   'articleHeadingSection',
    title:  'Article Heading Section',
    fields: [
        copyPaste,
        {
            name:    'image',
            type:    'image',
            title:   'Image',
            options: {
                hotspot: true
            }
        },
        {
            name:        'imageAlt',
            type:        'string',
            title:       'Alternative text',
            description: 'Important for SEO and accessiblity.'
        },
        {
            name:  'heading',
            title: 'Heading',
            type:  'string',
            rows:  1
        },
        {
            name:  'publishedOn',
            title: 'Publishing Date',
            type:  'date'
        },
        {
            name:  'author',
            title: 'Author',
            type:  'string',
            rows:  1
        },
        {
            name:  'description',
            title: 'Meta Description',
            type:  'text',
            rows:  8
        }
    ],

    preview: {
        select: {
            title: 'heading',
            media: 'image'
        },
        prepare({title, media}) {
            return {
                title,
                subtitle: 'Article Heading Section',
                media
            };
        }
    }
};
