import {copyPaste} from '@superside-oss/sanity-plugin-copy-paste';

export default {
    type:   'object',
    name:   'textSection',
    title:  'Text Section',
    fields: [
        copyPaste,
        {
            name:  'label',
            title: 'Section Label',
            type:  'string'
        },
        {
            type:   'object',
            name:   'leftColumn',
            title:  'Left Column',
            fields: [
                {
                    name:  'subheading',
                    title: 'Subheading',
                    type:  'string'
                },
                {
                    name:  'heading',
                    title: 'Heading',
                    type:  'string',
                    rows:  1
                },
                {
                    name:  'content',
                    title: 'Content',
                    type:  'simplePortableText'
                },
                {
                    name:  'footnote',
                    title: 'Footnote',
                    type:  'string'
                }
            ]
        },
        {
            type:   'object',
            name:   'rightColumn',
            title:  'Right Column',
            fields: [
                {
                    name:  'content',
                    title: 'Content',
                    type:  'text',
                    rows:  12
                },
                {
                    name:  'footnote',
                    title: 'Footnote',
                    type:  'string'
                }
            ]
        }
    ],
    preview: {
        select: {
            label: 'label'
        },
        prepare({label}) {
            return {
                title:    `${label}`,
                subtitle: 'Text Section'
            };
        }
    }
};
