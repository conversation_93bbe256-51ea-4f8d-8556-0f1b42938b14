export default {
    name:       'productOverview',
    title:      'Module',
    type:       'object',
    validation: Rule => Rule.custom(
        (fields = {}) => !fields.route || !fields.url || 'Only one link type is allowed'
    ),
    fieldsets: [
        {
            title: 'Navigate To',
            name:  'link'
        }
    ],
    fields: [
        {
            title: 'Category',
            name:  'category',
            type:  'string'
        },
        {
            title: 'Title',
            name:  'title',
            type:  'string'
        },
        {
            name:  'isBlueTitle',
            title: 'Blue title',
            type:  'boolean'
        },
        {
            title: 'Description',
            name:  'description',
            type:  'text'
        },
        {
            title:       'Internal link',
            description: 'Link to which card leads to',
            name:        'route',
            type:        'reference',
            to:          [{type: 'route'}],
            fieldset:    'link',
            options:     {
                filter: ({document}) => {
                    return {
                        filter: '_type == "route" && language == $language',
                        params: {language: document.language}
                    };
                }
            }
        },
        {
            title:      'External link',
            name:       'url',
            type:       'url',
            fieldset:   'link',
            validation: Rule => Rule.uri({
                allowRelative: true
            })
        },
        {
            title: 'Big Image (200 x 1000)',
            name:  'bigImage',
            type:  'image'
        },
        {
            title: 'Small Image (130 x 1000)',
            name:  'smallImage',
            type:  'image'
        },
        {
            name:        'imageAlt',
            type:        'string',
            title:       'Alternative text',
            description: 'Important for SEO and accessiblity.'
        }
    ]
};
