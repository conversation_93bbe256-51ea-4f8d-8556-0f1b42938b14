export default {
    name:   'actionButton',
    title:  'Action Button',
    type:   'object',
    fields: [
        {
            title: 'Text',
            name:  'text',
            type:  'string'
        },
        {
            title:   'Action',
            name:    'action',
            type:    'string',
            options: {
                list: [{value: 'openGdprPopup', title: 'Open GDPR Popup'}]
            },
            initialValue: 'openGdprPopup',
            validation:   Rule => Rule.required()
        }
    ]
};
