export default {
    name:      'iconTextLink',
    title:     'Icon Text',
    type:      'object',
    fieldsets: [
        {
            title: 'Link',
            name:  'link'
        }
    ],
    fields: [
        {
            title: 'Icon',
            name:  'icon',
            type:  'image'
        },
        {
            name:        'iconAlt',
            type:        'string',
            title:       'Alternative text',
            description: 'Important for SEO and accessiblity.'
        },
        {
            title: 'Text',
            name:  'description',
            type:  'string'
        },
        {
            title:       'Internal link',
            description: 'Use this to link between pages on the website',
            name:        'route',
            type:        'internalLink',
            fieldset:    'link'
        },
        {
            title:      'External link',
            name:       'link',
            type:       'url',
            validation: Rule => Rule.uri({
                allowRelative: true,
                scheme:        ['https', 'http', 'mailto', 'tel']
            }),
            fieldset: 'link'
        },
        {
            name:  'lightTheme',
            title: 'Light theme (background becomes white)',
            type:  'boolean'
        }
    ],
    preview: {
        select: {
            description: 'description'
        },
        prepare({description}) {
            return {
                title: description
            };
        }
    }
};
