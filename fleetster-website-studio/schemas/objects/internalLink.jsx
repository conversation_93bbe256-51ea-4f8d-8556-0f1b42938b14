import React from 'react';

// eslint-disable-next-line react/prop-types
const InternalLinkRender = ({children}) => <span>{children} 🔗</span>;

export default {
    title:       'Internal link to another document',
    name:        'internalLink',
    type:        'reference',
    description: 'Locate a document you want to link to',
    to:          [{type: 'route'}],
    blockEditor: {
        icon:   () => '🔗',
        render: InternalLinkRender
    },
    options: {
        filter: ({document}) => {
            return {
                filter: '_type == "route" && language == $language',
                params: {language: document.language}
            };
        }
    }
};
