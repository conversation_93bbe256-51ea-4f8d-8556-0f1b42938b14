export default {
    name:   'heroImage',
    title:  'Image with link',
    type:   'object',
    fields: [
        {
            title: 'Title',
            name:  'title',
            type:  'string'
        },
        {
            title: 'Image',
            name:  'image',
            type:  'image'
        },
        {
            title: 'Alternative text',
            name:  'alt',
            type:  'string'
        },
        {
            title: 'Icon',
            name:  'icon',
            type:  'image'
        },
        {
            title: 'Link',
            name:  'link',
            type:  'internalLink'
        }
    ],
    preview: {
        select: {
            imageUrl: 'image.asset.url',
            title:    'title',
            alt:      'alt'
        },
        prepare({imageUrl, title, alt}) {
            return {
                imageUrl,
                title: title || alt
            };
        }
    }
};
