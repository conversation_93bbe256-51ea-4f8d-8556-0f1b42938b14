export default {
    name:   'figure',
    title:  'Image',
    type:   'image',
    fields: [
        {
            title: 'Caption',
            name:  'caption',
            type:  'string'
        },
        {
            title:       'Alternative text',
            name:        'alt',
            type:        'string',
            description: 'Important for SEO and accessibility.',
            validation:  Rule => Rule.required()
        }
    ],
    preview: {
        select: {
            imageUrl: 'asset.url',
            caption:  'caption',
            alt:      'alt'
        },
        prepare({imageUrl, alt, caption}) {
            return {
                imageUrl,
                title: caption || alt
            };
        }
    }
};
