export default {
    name:   'headerLink',
    title:  'Header Link',
    type:   'object',
    fields: [
        {
            title: 'Link',
            name:  'link',
            type:  'navigationLink'
        },
        {
            title: 'Icon',
            name:  'icon',
            type:  'image'
        },
        {
            name:        'iconAlt',
            type:        'string',
            title:       'Alternative text',
            description: 'Important for SEO and accessiblity.'
        }
    ],
    preview: {
        select: {
            link: 'link',
            icon: 'icon'
        },
        prepare({link, icon}) {
            return {
                title: link.text,
                media: icon
            };
        }
    }
};
