export default {
    type:  'object',
    name:  'pricingPeriod',
    title: 'Product pricing period',

    fields: [
        {
            name:  'label',
            title: 'Label',
            type:  'string'
        },
        {
            name:        'percentage',
            description: 'The percentage that will be applied on top of the base price that is defined in the pricing cards.',
            title:       'Percentage',
            type:        'number'
        },
        {
            name:         'default',
            title:        'Default selected',
            type:         'boolean',
            initialValue: false
        }
    ]
};
