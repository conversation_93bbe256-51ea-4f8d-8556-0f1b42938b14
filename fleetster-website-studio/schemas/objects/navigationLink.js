export default {
    name:       'navigationLink',
    title:      'Navigation Link',
    type:       'object',
    validation: Rule => Rule.custom(
        (fields = {}) => !fields.route || !fields.url || 'Only one link type is allowed'
    ),
    fieldsets: [
        {
            title: 'Navigate To',
            name:  'link'
        }
    ],
    fields: [
        {
            title: 'Text',
            name:  'text',
            type:  'string'
        },
        {
            title:       'Internal link',
            description: 'Use this to link between pages on the website',
            name:        'route',
            type:        'reference',
            to:          [{type: 'route'}],
            fieldset:    'link',
            options:     {
                filter: ({document}) => {
                    return {
                        filter: '_type == "route" && language == $language',
                        params: {language: document.language}
                    };
                }
            }
        },
        {
            title:      'External link',
            name:       'url',
            type:       'url',
            fieldset:   'link',
            validation: Rule => Rule.uri({
                allowRelative: true
            })
        }
    ]
};
