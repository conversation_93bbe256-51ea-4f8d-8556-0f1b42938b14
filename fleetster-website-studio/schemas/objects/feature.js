export default {
    name:    'feature',
    title:   'Feature',
    type:    'object',
    options: {
        hotspot: true
    },

    fields: [
        {
            title: 'Caption',
            name:  'caption',
            type:  'string'
        },
        {
            title: 'Description',
            name:  'description',
            type:  'string'
        },
        {
            title: 'Icon',
            name:  'icon',
            type:  'image'
        },
        {
            title:       'Internal link',
            description: 'Link to which card leads to',
            name:        'route',
            type:        'reference',
            to:          [{type: 'route'}],
            options:     {
                filter: ({document}) => {
                    return {
                        filter: '_type == "route" && language == $language',
                        params: {language: document.language}
                    };
                }
            }
        }
    ]
};
