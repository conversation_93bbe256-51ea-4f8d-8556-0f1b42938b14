export default {
    name:       'dropdownMenuItem',
    title:      'Menu Item',
    type:       'object',
    validation: Rule => Rule.custom(
        (fields = {}) => !fields.route || !fields.url || 'Only one link type is allowed'
    ),
    fieldsets: [
        {
            title: 'Navigate To',
            name:  'link'
        }
    ],
    fields: [
        {
            title: 'Text',
            name:  'text',
            type:  'string'
        },
        {
            title: 'Description',
            name:  'description',
            type:  'string'
        },
        {
            title:       'Internal link',
            description: 'Use this to link between pages on the website',
            name:        'route',
            type:        'reference',
            to:          [{type: 'route'}],
            fieldset:    'link',
            options:     {
                filter: ({document}) => {
                    return {
                        filter: '_type == "route" && language == $language',
                        params: {language: document.language}
                    };
                }
            }
        },
        {
            title:      'External link',
            name:       'url',
            type:       'url',
            fieldset:   'link',
            validation: Rule => Rule.uri({
                allowRelative: true
            })
        },
        {
            title: 'Icon',
            name:  'icon',
            type:  'image'
        },
        {
            name:        'iconAlt',
            type:        'string',
            title:       'Alternative text',
            description: 'Important for SEO and accessiblity.'
        }
    ]
};
