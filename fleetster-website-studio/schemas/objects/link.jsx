import React from 'react';

// eslint-disable-next-line react/prop-types
const LinkRender = ({children}) => <span>{children} 🌍</span>;

export default {
    title:  'URL',
    name:   'link',
    type:   'object',
    fields: [
        {
            title:      'URL',
            name:       'href',
            type:       'url',
            validation: Rule => Rule.uri({
                allowRelative: true,
                scheme:        ['https', 'http', 'mailto', 'tel']
            })
        },
        {
            title: 'Open in new tab',
            name:  'openInNewTab',
            type:  'boolean'
        }
    ],
    blockEditor: {
        icon:   () => '🌍',
        render: LinkRender
    }
};
