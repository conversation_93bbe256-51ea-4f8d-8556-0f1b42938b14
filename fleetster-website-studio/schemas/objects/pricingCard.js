export default {
    type:  'object',
    name:  'pricingCard',
    title: 'Product pricing card',

    fields: [
        {
            name:  'isMostPopular',
            title: 'Most popular price',
            type:  'boolean'
        },
        {
            name:  'heading',
            title: 'Heading',
            type:  'string'
        },
        {
            name:  'description',
            title: 'Description',
            type:  'string'
        },
        {
            name:  'prices',
            title: 'Prices',
            type:  'array',
            of:    [
                {
                    title:  'Price',
                    type:   'object',
                    fields: [
                        {
                            name:    'currency',
                            title:   'Currency',
                            type:    'string',
                            options: {
                                list: [{value: 'euro', title: 'Euro'}, {value: 'dollar', title: 'Dollar'}]
                            },
                            initialValue: 'euro',
                            validation:   Rule => Rule.required()
                        },
                        {
                            name:  'value',
                            title: 'Value',
                            type:  'number'
                        }
                    ]
                }
            ]
        },
        {
            name:  'condition',
            title: 'Condition',
            type:  'string'
        },
        {
            name:    'callToAction',
            type:    'callToAction',
            title:   'Call to action',
            options: {
                hotspot: true
            }
        },
        {
            name:  'plus',
            title: 'Plus text',
            type:  'string'
        },
        {
            name: 'features',
            type: 'array',
            of:   [
                {
                    type:  'string',
                    title: 'Feature'
                }
            ]
        }
    ]
};
