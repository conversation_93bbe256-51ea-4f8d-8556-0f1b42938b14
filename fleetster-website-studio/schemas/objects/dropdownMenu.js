export default {
    name:   'dropdownMenu',
    title:  'Dropdown Menu',
    type:   'object',
    fields: [
        {
            title: 'Text',
            name:  'text',
            type:  'string'
        },
        {
            title: 'Icon',
            name:  'icon',
            type:  'image'
        },
        {
            title:       '"See All" Link Ref',
            description: 'To what page should the "See All" link redirect',
            name:        'seeAllRef',
            type:        'reference',
            to:          [{type: 'route'}],
            options:     {
                filter: ({document}) => {
                    return {
                        filter: '_type == "route" && language == $language',
                        params: {language: document.language}
                    };
                }
            }
        },
        {
            name: 'items',
            type: 'array',
            of:   [{type: 'dropdownMenuItem'}]
        }
    ]
};
