export default {
    name:    'offerStructuredData',
    title:   'Offer',
    type:    'object',
    options: {
        collapsible: true
    },
    fields: [
        {
            name:  'offers',
            title: 'Prices *',
            type:  'array',
            of:    [
                {
                    type:  'offerChildStructuredData',
                    title: 'Offer Price'
                }
            ],
            validation: Rule => Rule.required().min(1)
        },
        {
            name:    'availability',
            title:   'Availability *',
            type:    'string',
            options: {
                list: [
                    {title: 'Pre-order', value: 'https://schema.org/PreOrder'},
                    {title: 'In stock', value: 'https://schema.org/InStock'},
                    {title: 'Limited availability', value: 'https://schema.org/LimitedAvailability'},
                    {title: 'Sold out', value: 'https://schema.org/SoldOut'},
                    {title: 'Discontinued', value: 'https://schema.org/Discontinued'}
                ]
            }
        },
        {
            name:  'url',
            title: 'URL',
            type:  'url'
        },
        {
            name:  'priceValidUntil',
            title: 'Price valid until',
            type:  'date'
        },
        {
            name:    'businessFunction',
            title:   'Type of service',
            type:    'string',
            options: {
                list: [
                    {title: 'Provide service', value: 'http://purl.org/goodrelations/v1#ProvideService'},
                    {title: 'Sell', value: 'http://purl.org/goodrelations/v1#Sell'},
                    {title: 'Maintain', value: 'http://purl.org/goodrelations/v1#Maintain'},
                    {title: 'Lease out', value: 'http://purl.org/goodrelations/v1#LeaseOut'}
                ]
            }
        }
    ]
};
