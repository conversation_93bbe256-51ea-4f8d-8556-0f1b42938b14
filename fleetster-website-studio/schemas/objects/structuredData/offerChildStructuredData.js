export default {
    name:   'offerChildStructuredData',
    title:  'Offer Price',
    type:   'object',
    fields: [
        {
            name:       'price',
            title:      'Price *',
            type:       'string',
            validation: Rule => Rule.required()
        },
        {
            name:    'priceCurrency',
            title:   'Currency *',
            type:    'string',
            options: {
                list: [
                    {title: 'EUR', value: 'EUR'},
                    {title: 'USD', value: 'USD'}
                ]
            },
            validation: Rule => Rule.required()
        }
    ]
};
