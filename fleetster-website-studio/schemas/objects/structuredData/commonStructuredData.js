import _ from 'lodash';

export const thingProperties = [
    {
        name:       'name',
        title:      'Name *',
        type:       'string',
        validation: Rule => Rule.required()
    },
    {
        name:  'description',
        title: 'Description',
        type:  'string'
    },
    {
        name:  'url',
        title: 'URL',
        type:  'url'
    },
    {
        name:  'image',
        title: 'Image',
        type:  'image'
    }
];

export const thingPropertiesWithoutValidation = _.map(thingProperties, prop => {
    if (!prop.validation) { return prop; }

    let propWithoutValidation = _.omit(prop, 'validation');
    propWithoutValidation.title = _.trimEnd(prop.title, ' *');

    return propWithoutValidation;
});

export const Logo = {
    name:  'logo',
    title: 'Logo',
    type:  'image'
};

export const Slogan = {
    name:  'slogan',
    title: 'Slogan',
    type:  'string'
};

