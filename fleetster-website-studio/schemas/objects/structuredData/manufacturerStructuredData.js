import {Logo, Slogan, thingPropertiesWithoutValidation} from './commonStructuredData';

export default {
    name:        'manufacturerStructuredData',
    title:       'Manufacturer',
    type:        'object',
    description: 'Defaults to fleetster when empty',
    options:     {
        collapsible: true
    },
    fields: [
        ...thingPropertiesWithoutValidation,
        {...Logo},
        {
            name:  'email',
            title: 'Email',
            type:  'string'
        },
        {
            name:  'telephone',
            title: 'Telephone',
            type:  'string'
        },
        {
            name:  'location',
            title: 'Location',
            type:  'locationStructuredData'
        },
        {...Slogan}
    ]
};
