import {Logo, Slogan, thingPropertiesWithoutValidation} from './commonStructuredData';

export default {
    name:        'brandStructuredData',
    title:       'Brand',
    type:        'object',
    description: 'Defaults to fleetster when empty',
    options:     {
        collapsible: true
    },
    fields: [
        ...thingPropertiesWithoutValidation,
        {...Logo},
        {...Slogan}
    ]
};
