export default {
    name:    'locationStructuredData',
    title:   'Location',
    type:    'object',
    options: {
        collapsible: true
    },
    fields: [
        {
            name:  'addressCountry',
            title: 'Country',
            type:  'string'
        },
        {
            name:  'addressRegion',
            title: 'Region',
            type:  'string'
        },
        {
            name:  'addressLocality',
            title: 'City',
            type:  'string'
        },
        {
            name:  'postalCode',
            title: 'Postal Code',
            type:  'string'
        },
        {
            name:  'streetAddress',
            title: 'Street Address',
            type:  'string'
        }
    ]
};
