import {thingProperties} from './commonStructuredData';

export default {
    name:        'productStructuredData',
    title:       'Product Structured Data (Rich Text)',
    type:        'object',
    description: 'Structured data is a standardized format for providing information about a page which greatly improves SEO',
    fields:      [
        ...thingProperties,
        {
            name:  'category',
            title: 'Category',
            type:  'string'
        },
        {
            name:  'model',
            title: 'Model',
            type:  'string'
        },
        {
            name:       'offers',
            title:      'Offer *',
            type:       'offerStructuredData',
            validation: Rule => Rule.required()
        },
        {
            name:  'brand',
            title: 'Brand',
            type:  'brandStructuredData'
        },
        {
            name:  'manufacturer',
            title: 'Manufacturer',
            type:  'manufacturerStructuredData'
        }
    ],
    preview: {
        select: {
            title: 'name',
            media: 'image'
        },
        prepare({title, media}) {
            return {
                title,
                media,
                subtitle: 'Product Structured Data (Rich Text)'
            };
        }
    }
};
