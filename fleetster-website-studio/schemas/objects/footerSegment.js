export default {
    name:   'footerSegment',
    title:  'Footer segment',
    type:   'object',
    fields: [
        {
            title: 'Title',
            name:  'title',
            type:  'string'
        },
        {
            title:       '"View all" Link Ref',
            description: 'To what page should the "View all products" link redirect',
            name:        'viewAllRef',
            type:        'reference',
            to:          [{type: 'route'}],
            options:     {
                filter: ({document}) => {
                    return {
                        filter: '_type == "route" && language == $language',
                        params: {language: document.language}
                    };
                }
            }
        },
        {
            name: 'links',
            type: 'array',
            of:   [{type: 'navigationLink'}, {type: 'actionButton'}]
        }
    ]
};
