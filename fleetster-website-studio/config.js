import * as _ from 'lodash';
import rc from 'rc';

const configs = {};

configs.development = _.merge({}, {
    services: {
        websitePreview: {
            proto:       'http',
            host:        'localhost',
            port:        '3000',
            previewPath: 'preview'
        }
    }
});

configs.testing = {
    services: {
        websitePreview: {
            proto:       'https',
            host:        'nextjs-website-preview.fleetster.de',
            port:        '443',
            previewPath: 'preview'
        }
    }
};

configs.production = {
    services: {
        websitePreview: {
            proto:       'https',
            host:        'nextjs-website-preview.fleetster.de',
            port:        '443',
            previewPath: 'preview'
        }
    }
};

export const config = rc('fleetsterWebsiteStudio', configs[process.env.NODE_ENV || 'development']);

