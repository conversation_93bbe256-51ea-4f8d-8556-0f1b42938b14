export const controlTowerTranslateEndpoint = process.env.SANITY_STUDIO_ENV === 'production'
    ? 'https://control-tower-next.fleetster.de/api/label/translate-text'
    : 'https://control-tower-next.testing.fleetster.de/api/label/translate-text';

export const DEEPL_API_KEY64 = process.env.SANITY_STUDIO_ENV === 'production'
    ? process.env.SANITY_STUDIO_DEEPL_API_KEY64
    : process.env.SANITY_STUDIO_DEEPL_API_KEY64_TESTING;

export const translatableFields = {
    title:          true,
    subtitle:       true,
    description:    true,
    content:        true,
    name:           true,
    imageAlt:       true,
    sideImageTitle: true,
    sideImageAlt:   true,
    text:           true,
    html:           true,
    heading:        true,
    caption:        true,
    copyright:      true,
    label:          true,
    condition:      true,
    plus:           true,
    productName:    true,
    footnote:       true,
    cells:          true,
    iconAlt:        true,
    alt:            true
};

export const titleFields = ['title', 'heading', 'caption', 'text', 'name', 'label', 'productName', 'callToAction.title'];

export const nonTranslatableFields = {
    tags: true
};
