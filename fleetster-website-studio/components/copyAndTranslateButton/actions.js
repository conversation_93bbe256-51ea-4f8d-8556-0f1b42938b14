import _ from 'lodash';
import {uuid} from '@sanity/uuid';

import {deepTranslate} from './translate';
import {getRefPaths} from './utils';

async function getDraftElseOriginal(client, document) {
    const draftDocument = await client.getDocument(`drafts.${document._id || document._ref}`);

    return draftDocument || client.getDocument(document._id || document._ref);
}

export async function getTranslationsMetadata(client, document) {
    const documentId = document._id.replace('drafts.', '');
    let translationMetadata = await client.fetch(`*[_type == "translation.metadata" && references($documentId)] { 
        _id, "translations": translations[] {_key, value} 
    }[0]`, {documentId}) || {translations: [{_key: document.language, value: document}]};

    for (const translation of translationMetadata.translations) {
        translation.value = await getDraftElseOriginal(client, translation.value);
    }

    return translationMetadata;
}

async function createTranslationMetadata(client, document) {
    const undraftedId = document._id.replace('drafts.', '');

    await client.createOrReplace({...document, _id: undraftedId});

    const translationMetadata = {
        _id:          uuid(),
        _type:        'translation.metadata',
        schemaTypes:  [document._type],
        translations: [{_key: document.language, value: {_type: 'reference', _ref: undraftedId}}]
    };

    await client.createOrReplace(translationMetadata);

    _.set(translationMetadata, 'translations[0].value', document);

    return translationMetadata;
}

async function createNewTranslatedDocument(client, translationMetadata, targetLang) {
    const isMetadataCreated = !!translationMetadata._id;

    if (!isMetadataCreated) {
        translationMetadata = await createTranslationMetadata(client, _.first(translationMetadata.translations).value);
    }

    const newDocument = {
        _id:      uuid(),
        _type:    translationMetadata.translations[0].value._type,
        language: targetLang,
        content:  []
    };

    const rawMetadata = await client.getDocument(translationMetadata._id);

    rawMetadata.translations.push({
        _key:  targetLang,
        value: {_type: 'reference', _ref: newDocument._id}
    });

    await client.createOrReplace(newDocument);
    await client.createOrReplace(rawMetadata);

    return newDocument;
}

async function fixTranslationReferences({client, toast, content, targetLang}) {
    const refPaths = getRefPaths(content);

    for (let path of refPaths) {
        const refId = _.get(content, path);

        const {translations} = await getTranslationsMetadata(client, {_id: refId});

        const correctTranslation = translations.find(translation => translation._key === targetLang);

        path = path.join('.');

        if (correctTranslation) {
            _.set(content, path, correctTranslation.value._id);
        } else {
            toast.push({
                status: 'warning',
                title:  `No translation found for ${refId} in ${targetLang}, unsetting [${path.split('.').slice(0, -1).join('.')}]`
            });

            _.unset(content, path.split('.').slice(0, -1).join('.'));
        }
    }
}

async function getLanguageDocuments({srcLang, targetLang, document, client}) {
    const translationMetadata = await getTranslationsMetadata(client, document);

    const srcLangDocument = _.find(translationMetadata.translations, {_key: srcLang})?.value;
    let targetLangDocument = _.find(translationMetadata.translations, {_key: targetLang})?.value;

    if (!targetLangDocument) {
        targetLangDocument = await createNewTranslatedDocument(client, translationMetadata, targetLang);
    }

    return {srcLangDocument, targetLangDocument};
}

export async function writeIntoLanguage({srcLang, targetLang, section, document, client, toast}) {
    let {srcLangDocument, targetLangDocument} = await getLanguageDocuments({srcLang, targetLang, document, client});

    const targetLangDocumentId = targetLangDocument._id.includes('drafts.') ? targetLangDocument._id : `drafts.${targetLangDocument._id}`;

    let translatedContent;
    if (section === 'all') {
        translatedContent = await deepTranslate(srcLang, targetLang, srcLangDocument);
        targetLangDocument = {
            ...translatedContent,
            _id:      targetLangDocumentId,
            language: targetLangDocument.language
        };
    } else {
        translatedContent = await deepTranslate(srcLang, targetLang, _.find(srcLangDocument.content, {_key: section}));
        targetLangDocument = {
            ...targetLangDocument,
            _id:     targetLangDocumentId,
            content: [...targetLangDocument.content, translatedContent]
        };
    }

    await fixTranslationReferences({client, toast, content: translatedContent, targetLang});

    return client.createOrReplace(targetLangDocument);
}
