import _ from 'lodash';
import {custom<PERSON><PERSON><PERSON>bet} from 'nanoid';

import {DEEPL_API_KEY64, controlTowerTranslateEndpoint, nonTranslatableFields, translatableFields} from './constants';
import {decodeBase64} from './utils';

const nanoid = customAlphabet('1234567890abcdefghijklmnopqrstuvwxyz', 20);

function preserveOriginalSpacing(originalText, translatedText) {
    const leadingSpaces = originalText.match(/^\s*/)[0];
    const trailingSpaces = originalText.match(/\s*$/)[0];

    return `${leadingSpaces}${translatedText.trim()}${trailingSpaces}`;
}

export async function translateText(srcLang, targetLang, text) {
    const body = {
        text,
        sourceLanguage: srcLang,
        targetLanguage: targetLang
    };

    const response = await fetch(controlTowerTranslateEndpoint, {
        method:  'POST',
        headers: {
            'api-key':      decodeBase64(DEEPL_API_KEY64),
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(body)
    });

    const result = await response.json();

    const {translatedText} = result;

    return preserveOriginalSpacing(text, translatedText);
}

function setNewKey(obj) {
    if (obj._key) {
        obj._key = nanoid();
    }
}

export function translateArray({srcLang, targetLang, object, field, promises}) {
    setNewKey(object);

    if (translatableFields[field] && typeof object[field][0] === 'string') {
        for (let i = 0; i < object[field].length; i++) {
            if (object[field][i].length > 1) {
                try {
                    promises.push(translateText(srcLang, targetLang, object[field][i]).then(translation => {
                        object[field][i] = translation;
                    }));
                } catch (e) {
                    console.error(e);
                }
            }
        }
    } else {
        _.forEach(object[field], item => deepTranslate(srcLang, targetLang, item, promises));
    }
}

export async function deepTranslate(srcLang, targetLang, object, promises = []) {
    setNewKey(object);

    for (const field in object) {
        if (_.isEmpty(object[field])) { continue; }
        if (typeof object[field] === 'object' && !nonTranslatableFields[field]) {
            if (_.isArray(object[field])) {
                translateArray({srcLang, targetLang, object, field, promises});
            } else {
                deepTranslate(srcLang, targetLang, object[field], promises);
            }
        } else if (translatableFields[field]) {
            promises.push(translateText(srcLang, targetLang, object[field]).then(translation => {
                object[field] = translation;
            }));
        }
    }

    await Promise.all(promises);

    return object;
}
