import _ from 'lodash';

const typesWithTranslatedRefs = {
    route:            'page',
    'site-config':    'homePage',
    callToAction:     'route',
    dropdownMenu:     'seeAllRef',
    dropdownMenuItem: 'route',
    feature:          'route',
    footerSegment:    'viewAllRef',
    navigationLink:   'route',
    productOverview:  'route'
};

export function getRefPaths(document) {
    const refs = [];

    function findRefs(obj, path) {
        for (const [key, value] of Object.entries(obj)) {
            if (key === '_type' && typesWithTranslatedRefs[value]) {
                const refKey = typesWithTranslatedRefs[value];
                refs.push([...path, refKey, '_ref']);
            } else if (key === '_type' && value === 'internalLink') {
                refs.push([...path, '_ref']);
            } else if (typeof value === 'object') {
                findRefs(value, [...path, key]);
            }
        }
    }

    findRefs(document, []);

    return refs.filter(path => _.get(document, path));
}

export function decodeBase64(base64String) {
    const binaryString = atob(base64String);

    return new TextDecoder('utf-8').decode(
        new Uint8Array([...binaryString].map(char => char.charCodeAt(0)))
    );
}
