import React, {forwardRef, useEffect, useImperativeHandle, useState} from 'react';
import _ from 'lodash';
import {useClient, useFormValue} from 'sanity';
import {useToast} from '@sanity/ui';

import {languages} from '../../schemas/documents/languages';
import {titleFields} from './constants';
import {getTranslationsMetadata, writeIntoLanguage} from './actions';

function CopyAndTranslateButton(props, ref) {
    const client = useClient({apiVersion: '2024-01-01'});
    const toast = useToast();
    const [selectedSourceLanguage, setSelectedSourceLanguage] = useState('en');
    const [selectedTargetLanguage, setSelectedTargetLanguage] = useState('en');
    const [sectionList, setSectionList] = useState([{title: 'Whole document', value: 'all'}]);
    const [selectedSection, setSelectedSection] = useState('all');

    const document = useFormValue([]);

    const setSourceLanguage = async lang => {
        const translationMetadata = await getTranslationsMetadata(client, document);

        let content = _.find(translationMetadata.translations, {_key: lang})?.value?.content || [];
        content = _.filter(content, section => section._type !== 'block');

        setSectionList([
            {title: 'Whole document', value: 'all'},
            ...content.map(section => {
                const titleField = titleFields.find(field => _.get(section, field));
                const sectionTypeTitle = _.startCase(_.get(section, '_type', 'Untitled'));
                const title = _.get(section, titleField) || sectionTypeTitle;

                return {title, value: section._key};
            })
        ]);
        setSelectedSourceLanguage(lang);
    };

    useEffect(() => {
        setSourceLanguage('en');
    }, []);

    useImperativeHandle(ref, () => ({
        focus() { /* yum yum */ }
    }));

    return (
        <div style={{display: 'flex', alignItems: 'center'}}>
            <span style={{marginRight: 8}}>Translate:</span>
            <select style={{marginRight: 8, maxWidth: 200}} onChange={e => setSelectedSection(e.target.value)}>
                {_.map(sectionList, section => <option value={section.value} key={section.value}>{section.title}</option>)}
            </select>
            <span style={{marginRight: 8}}>From:</span>
            <select style={{marginRight: 8}} onChange={e => setSourceLanguage(e.target.value)}>
                {_.map(languages, lang => <option value={lang.name} key={`source_${lang.name}`}>{lang.title}</option>)}
            </select>
            <span style={{marginRight: 8}}>To:</span>
            <select style={{marginRight: 8}} onChange={e => setSelectedTargetLanguage(e.target.value)}>
                {_.map(languages, lang => <option value={lang.name} key={`target_${lang.name}`}>{lang.title}</option>)}
            </select>
            <button onClick={async() => {
                await writeIntoLanguage({
                    srcLang:    selectedSourceLanguage,
                    targetLang: selectedTargetLanguage,
                    section:    selectedSection,
                    document:   _.cloneDeep(document),
                    client,
                    toast
                });

                toast.push({
                    status: 'success',
                    title:  'Translation finished.'
                });
            }}>
                Confirm
            </button>
        </div>
    );
}

export default forwardRef(CopyAndTranslateButton);
