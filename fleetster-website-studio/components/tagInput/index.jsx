import React, {
    useState,
    forwardRef,
    useImperativeHandle
} from 'react';
import Select from 'react-select';
import PropTypes from 'prop-types';
import {FormFieldSet, PatchEvent, set, unset} from 'sanity';
import {customAlphabet} from 'nanoid';
import _ from 'lodash';

const nanoid = customAlphabet('1234567890abcdefghijklmnopqrstuvwxyz', 10);

const createPatchFrom = value => PatchEvent.from(value === '' ? unset() : set(value));

const tags = [
    {label: 'Improvement', value: 'improvement'},
    {label: 'Feature', value: 'feature'},
    {label: 'Car Sharing', value: 'carSharing'},
    {label: 'Checks', value: 'driverLicenseCheck'},
    {label: 'Driver Log', value: 'driverLog'},
    {label: 'Fleet Management', value: 'fleetManagement'},
    {label: 'Mobile App', value: 'mobileApp'},
    {label: 'Logistics', value: 'logistics'},
    {label: 'Billing', value: 'billing'},
    {label: 'Setting', value: 'setting'},
    {label: 'Bookings', value: 'bookings'},
    {label: 'Platform', value: 'platform'},
    {label: 'Traka', value: 'traka'},
    {label: 'Corporate Car Sharing', value: 'corporateCarSharing'},
    {label: 'Public Car Sharing', value: 'publicCarSharing'},
    {label: 'Car Sharing Kit', value: 'carSharingKit'},
    {label: 'Reporting', value: 'reporting'}
];

// eslint-disable-next-line react/display-name
const tagInput = forwardRef((props, ref) => {
    const {schemaType, level, value} = props;

    const [selected, setSelected] = useState(value || []);
    const options = schemaType.options || {};
    const closeMenuOnSelect = options.closeMenuOnSelect !== false;

    useImperativeHandle(ref, () => ({
        focus() {
            this._inputElement.focus();
        }
    }));

    const handleChange = value => {
        if (_.last(value)) {
            _.last(value)._key = nanoid();
        }

        setSelected(!value ? [] : value);
        props.onChange(createPatchFrom(!value ? [] : value));
    };

    const selectMenuProps = {
        value:     selected ? selected : [],
        isMulti:   true,
        options:   tags,
        onChange:  handleChange,
        closeMenuOnSelect,
        className: 'tag-select'
    };

    return (
        <FormFieldSet level={level} legend={schemaType.title} description={schemaType.description}>
            <Select {...selectMenuProps}/>
        </FormFieldSet>
    );
});

tagInput.propTypes = {
    onChange:   PropTypes.func,
    schemaType: PropTypes.object,
    level:      PropTypes.number,
    value:      PropTypes.array
};

export default tagInput;
