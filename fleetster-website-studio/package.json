{"name": "fleetster-website-studio", "private": true, "version": "1.0.153", "description": "fleetster Website Studio", "main": "package.json", "repository": "https://gitlab.com/fleetster/fleetster-website-studio.git", "author": "<PERSON> <<EMAIL>>", "license": "UNLICENSED", "scripts": {"build": "./node_modules/.bin/sanity build", "deploy": "./node_modules/.bin/sanity deploy", "dev": "./node_modules/.bin/sanity dev", "preview": "./node_modules/.bin/http-server ./dist -p 80 -P http://localhost:80?", "lint": "./node_modules/.bin/eslint ."}, "keywords": ["sanity"], "dependencies": {"@babel/core": "^7.27.1", "@sanity/assist": "^4.2.0", "@sanity/cli": "^3.88.3", "@sanity/color-input": "^4.0.3", "@sanity/dashboard": "^4.1.3", "@sanity/document-internationalization": "^3.3.1", "@sanity/image-url": "^1.1.0", "@sanity/sanity-plugin-async-list": "^1.3.1", "@sanity/table": "^1.1.3", "@sanity/ui": "^2.15.17", "@sanity/uuid": "^3.0.2", "@superside-oss/sanity-plugin-copy-paste": "^1.0.3", "http-server": "^14.1.1", "lodash": "^4.17.21", "nanoid": "^5.1.5", "prop-types": "^15.8", "qs": "^6.14.0", "rc": "^1.2.8", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-select": "^5.10.1", "sanity": "^3.88.3", "sanity-plugin-dashboard-widget-document-list": "^2.1.0", "sanity-plugin-media": "^3.0.2", "styled-components": "^6.1.18", "sanity-plugin-document-reference-by": "^1.0.1"}, "devDependencies": {"eslint": "^9.27.0", "eslint-plugin-react": "^7.37.5"}}