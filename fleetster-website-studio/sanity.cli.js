import {defineCliConfig} from 'sanity/cli';
// import path from 'path';

export default defineCliConfig({
    api: {
        projectId: process.env.SANITY_STUDIO_PROJECT_ID,
        dataset:   process.env.SANITY_STUDIO_API_DATASET
    }
    // Disabled until this is resolved: https://github.com/sanity-io/sanity/issues/9455
    // vite: {
    //     resolve: {
    //         alias: {
    //             'rf-validators': path.resolve(__dirname, './schemas/shared/rf-validators')
    //         }
    //     }
    // }
});
