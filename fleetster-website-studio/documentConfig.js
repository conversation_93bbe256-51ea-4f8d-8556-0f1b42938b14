import {DeleteTranslationAction} from '@sanity/document-internationalization';
import resolveServiceUrl from './resolveServiceUrl';
import {translatedSchemas} from './schemas/schema';

export const getDocumentConfig = () => ({
    productionUrl: (prev, {document}) => {
        return resolveServiceUrl(document) || prev;
    },
    newDocumentOptions: (prev, {creationContext}) => {
        if (creationContext.type === 'global') {
            return prev.filter(templateItem => templateItem.templateId !== 'settings');
        }

        return prev;
    },
    actions: (prev, {schemaType}) => {
        if (schemaType === 'settings') {
            return prev.filter(({action}) => !['unpublish', 'delete', 'duplicate'].includes(action));
        }
        if (translatedSchemas.includes(schemaType)) {
            return [...prev, DeleteTranslationAction];
        }

        return prev;
    }
});
