import {structureTool} from 'sanity/structure';
import {dashboardTool, projectInfoWidget, projectUsersWidget} from '@sanity/dashboard';
import {documentInternationalization} from '@sanity/document-internationalization';
import {colorInput} from '@sanity/color-input';
import {table} from '@sanity/table';
import {documentListWidget} from 'sanity-plugin-dashboard-widget-document-list';
import {media} from 'sanity-plugin-media';
import {copyPastePlugin} from '@superside-oss/sanity-plugin-copy-paste';
import {asyncList} from '@sanity/sanity-plugin-async-list';
import {assist} from '@sanity/assist';
import {ReferencedBy} from 'sanity-plugin-document-reference-by';

import deskStructure from './deskStructure';

const defaultDocumentNodeResolver = S => S.document().views([
    S.view.form(),
    S.view.component(ReferencedBy).title('Referenced by')
]);

export const getPlugins = () => [
    dashboardTool({
        widgets: [
            projectInfoWidget(),
            projectUsersWidget(),
            documentListWidget({
                title:  'Recently edited',
                types:  ['page'],
                order:  '_updatedAt desc',
                layout: {width: 'medium'}
            })
        ]
    }),
    structureTool({
        structure:           deskStructure,
        defaultDocumentNode: defaultDocumentNodeResolver
    }),
    documentInternationalization(
        {
            supportedLanguages: [
                {id: 'en', title: 'English'},
                {id: 'de', title: 'German'},
                {id: 'es', title: 'Spanish'},
                {id: 'nl', title: 'Dutch'}
            ],
            schemaTypes: ['page', 'route', 'redirect', 'disallow-robots', 'changelog', 'site-config'],
            bulkPublish: true
        }
    ),
    asyncList({
        schemaType: 'salesEndpoint',
        loader:     async({client}) => {
            const {salesEndpoints} = await client.fetch('*[_id == "global-config"]{salesEndpoints}[0]');

            return salesEndpoints.map(endpoint => ({value: endpoint.key}));
        }
    }),
    media(),
    colorInput(),
    table(),
    copyPastePlugin(),
    assist()
];
