/* eslint-disable */

// Updates the variable 'isBlueTitle' according to the category on Product Overview — safe to run, it doesn't delete anything

import sanityClient from '@sanity/base/lib/client/index.js';
import _ from 'lodash';

const client = sanityClient.withConfig({apiVersion: '2021-06-07'});

const documentType = 'page';
const fieldPaths = [
    "i18n.de.content",
    "i18n.en.content",
    "i18n.es.content",
    "i18n.nl.content",
];

const fetchDocuments = fieldPath =>
    client.fetch(`*[_type == '${documentType}' && defined(${fieldPath})]`)

const buildPatches = (docs, fieldPath) =>
    docs.map(doc => {

        let setPatch = {};
        const content = _.get(doc, fieldPath);

        _.forEach(content, (section, i) => {
            const isProductsOverviewSection = section._type === 'productOverviewSection';
            const productOverview = _.get(doc, `${fieldPath}.${i}.productOverview`);
            if (isProductsOverviewSection && productOverview) {
                _.forEach(productOverview, (product, j) => {
                    setPatch[`${fieldPath}[${i}].productOverview[${j}]`] = [{
                        isBlueTitle: _.lowerCase(_.get(product, `category`)) === 'software',
                    }];
                });

            }
        });

        return {
            id: doc._id,
            patch: {
                // if you wish, set a new value for your field, you can reference the own document here
                // so you can actually rename a field, like this:
                // set: {newNameOrPath: _.get(doc, fieldPath)}
                set: setPatch,
                // Use this to remove a field, don't use it if you don't want
                //unset: [fieldPath],
                // this will cause the migration to fail if any of the documents has been
                // modified since it was fetched.
                ifRevisionID: doc._rev
            }
        }
    })

const createTransaction = patches =>
    patches.reduce((tx, patch) => tx.patch(patch.id, patch.patch), client.transaction())

const commitTransaction = tx => tx.commit()

const migrateNextBatch = async (fieldPath) => {
    const documents = await fetchDocuments(fieldPath)
    const patches = _.filter(buildPatches(documents, fieldPath))
    if (patches.length === 0) {
        console.log('No more documents to migrate!')
        return null
    }
    console.log(
        `Migrating batch:\n %s`,
        patches.map(patch => `${patch.id} => ${JSON.stringify(patch.patch)}`).join('\n')
    )
    const transaction = createTransaction(patches)
    await commitTransaction(transaction)
    return migrateNextBatch()
}

const migrate = async () => {
    for (const fieldPath of fieldPaths) {
        await migrateNextBatch(fieldPath).catch(err => {
            console.error(err)
            //process.exit(1)
        })
    }
}

migrate();

//sanity exec scripts/patchProductOverviewColor.mjs
