import _ from 'lodash';

import client from '@sanity/base/lib/client/index.js';

function getChangelogs() {
    const query = `*[_type == "changelog" && !(_id match "drafts.*")] {
        _id,
        _updatedAt,
        i18n,
        tags
    }`;

    return client.fetch(query);
}

const tagsMap = [
    {label: 'Improvement', value: 'improvement'},
    {label: 'Feature', value: 'feature'},
    {label: 'Car Sharing', value: 'carSharing'},
    {label: 'Driver License Check', value: 'driverLicenseCheck'},
    {label: 'Driver Log', value: 'driverLog'},
    {label: 'Fleet Management', value: 'fleetManagement'},
    {label: 'Mobile App', value: 'mobileApp'},
    {label: 'Logistics', value: 'logistics'},
    {label: 'Billing', value: 'billing'},
    {label: 'Setting', value: 'setting'},
    {label: 'Bookings', value: 'bookings'},
    {label: 'Platform', value: 'platform'},
    {label: 'Traka', value: 'traka'},
    {label: 'Corporate Car Sharing', value: 'corporateCarSharing'},
    {label: 'Public Car Sharing', value: 'publicCarSharing'},
    {label: 'Car Sharing Kit', value: 'carSharingKit'}
];

async function addNewTags() {
    const changelogs = await getChangelogs();

    _.forEach(changelogs, changelog => {
        const content = changelog.i18n.en;
        const newTags = [];
        if (content && content.tags) {
            _.forEach(content.tags, tag => {
                let newTag = _.find(tagsMap, {label: tag.label});

                if (newTag) {
                    newTags.push(newTag);
                }
            });

            console.log(changelog);
            client.patch(changelog._id).set({tags: newTags}).commit().then(poop => console.log('Changelog Updated!', JSON.stringify(poop, null, 4)));
        }
    });
}

addNewTags();
