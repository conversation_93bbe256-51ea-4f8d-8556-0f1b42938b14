/* eslint-disable */

// Copies hero images from the old structure to the new structure — safe to run, it doesn't delete anything

import sanityClient from '@sanity/base/lib/client/index.js';
import { nanoid } from 'nanoid';
import _ from 'lodash';

const client = sanityClient.withConfig({apiVersion: '2021-06-07'});

const documentType = 'page';
const fieldPaths = [
    "i18n.de.content",
    "i18n.en.content",
    "i18n.es.content",
    "i18n.nl.content",
];

const fetchDocuments = fieldPath =>
    client.fetch(`*[_type == '${documentType}' && defined(${fieldPath})]`)

const buildPatches = (docs, fieldPath) =>
    docs.map(doc => {
        let setPatch = {};

        const content = _.get(doc, fieldPath);
        _.forEach(content, (section, i) => {
            if (section._type === 'hero' && !_.get(doc, `${fieldPath}.${i}.heroImages`)) {
                setPatch[`${fieldPath}[${i}].heroImages`] = [{
                    _key: nanoid(12),
                    _type: 'heroImage',
                    image: _.get(doc, `${fieldPath}.${i}.heroImage`),
                    alt: _.get(doc, `${fieldPath}.${i}.heroImageAlt`)
                }];
            }
        });

        if (_.isEmpty(setPatch)) {
            return null;
        }

        return {
            id: doc._id,
            patch: {
                // if you wish, set a new value for your field, you can reference the own document here
                // so you can actually rename a field, like this:
                // set: {newNameOrPath: _.get(doc, fieldPath)}
                set: setPatch,
                // Use this to remove a field, don't use it if you don't want
                //unset: [fieldPath],
                // this will cause the migration to fail if any of the documents has been
                // modified since it was fetched.
                ifRevisionID: doc._rev
            }
        }
    })

const createTransaction = patches =>
    patches.reduce((tx, patch) => tx.patch(patch.id, patch.patch), client.transaction())

const commitTransaction = tx => tx.commit()

const migrateNextBatch = async (fieldPath) => {
    const documents = await fetchDocuments(fieldPath)
    const patches = _.filter(buildPatches(documents, fieldPath))
    if (patches.length === 0) {
        console.log('No more documents to migrate!')
        return null
    }
    console.log(
        `Migrating batch:\n %s`,
        patches.map(patch => `${patch.id} => ${JSON.stringify(patch.patch)}`).join('\n')
    )
    const transaction = createTransaction(patches)
    await commitTransaction(transaction)
    return migrateNextBatch()
}

const migrate = async () => {
    for (const fieldPath of fieldPaths) {
        await migrateNextBatch(fieldPath).catch(err => {
            console.error(err)
            //process.exit(1)
        })
    }
}

migrate();
