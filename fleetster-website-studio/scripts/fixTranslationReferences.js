/* eslint-disable */

/*
    After running migrateTranslations.mjs, all the translation references will be wrong.
    This script will fix that by pointing the referenced documents to the correct translations.

    Run this script with: `sanity exec scripts/fixTranslationReferences.mjs --with-user-token`
*/

import {getCliClient} from 'sanity/cli'
import fs from 'fs';
import _ from 'lodash';

const client = getCliClient();

const typesWithTranslatedRefs = {
    'route': 'page',
    'site-config': 'homePage',
    'callToAction': 'route',
    'dropdownMenu': 'seeAllRef',
    'dropdownMenuItem': 'route',
    'feature': 'route',
    'footerSegment': 'viewAllRef',
    'navigationLink': 'route',
    'productOverview': 'route'
};

function getRefPaths(document) {
    const refs = [];
    function findRefs(obj, path) {
        for (const [key, value] of Object.entries(obj)) {
            if (key === '_type' && typesWithTranslatedRefs[value]) {
                const refKey = typesWithTranslatedRefs[value];
                refs.push([...path, refKey, '_ref']);
            } else if (key === '_type' && value === 'internalLink') {
                    refs.push([...path, '_ref']);
            } else if (typeof value === 'object') {
                findRefs(value, [...path, key]);
            }
        }
    }
    findRefs(document, []);
    return refs;
}

function getTranslations(id) {
    return client.fetch(`*[_type == "translation.metadata" && references($id)].translations[].value->{_id, language}`, {id});
}

const unreferencedDocuments = [];

async function migrateDocument(document) {
    if (!document.language) {
        console.log(`Document ${document._id} of type ${document._type} has no language, skipping`);
        return;
    }

    const refPaths = getRefPaths(document);

    const patches = [];

    for (let path of refPaths) {
        const refId = _.get(document, path);
        if (!refId) {
            continue;
        }

        const translations = await getTranslations(refId);
        if (translations.length === 0) {
            continue;
        }

        const correctTranslation = translations.find(translation => translation.language === document.language);

        path = path.join('.').replace(/\.(\d+)/g, '[$1]');

        if (correctTranslation) {
            patches.push({
                id: document._id,
                patch: {
                    set: {
                        [path]: correctTranslation._id
                    }
                }
            });
        } else {
            console.log(`No translation found for ${refId} in ${document.language}, unsetting [${path.split('.').slice(0, -1).join('.')}]`);
            patches.push({
                id: document._id,
                patch: {
                    unset: [path.split('.').slice(0, -1).join('.')]
                }
            });

            unreferencedDocuments.push({
                documentId: document._id,
                type: document._type,
                language: document.language,
                refId,
                path,
            });
        }
    }

    const transaction = patches.reduce((tx, patch) => tx.patch(patch.id, patch.patch), client.transaction());

    await transaction.commit().then(() => {
        console.log(`Successfully migrated ${document._id} of type ${document._type}`);
    }).catch(err => {
        console.error(`Failed to migrate ${document._id} of type ${document._type}`, err);
    });
}

const fetchDocuments = (schemaType, page) =>
    client.fetch(`*[_type == "${schemaType}"] | order(_createdAt) [${page * 100}...${(page + 1) * 100}]`)

const migrateNextBatch = async (schemaType, page = 0) => {
    const documents = await fetchDocuments(schemaType, page)

    if (documents.length === 0) {
        return;
    }

    for (const document of documents) {
        await migrateDocument(document)
    }

    return migrateNextBatch(schemaType, page + 1)
}

const schemaTypes = ['page', 'site-config', 'route', 'redirect', 'disallow-robots', 'changelog'];

const migrate = async () => {
    for (const schemaType of schemaTypes) {
        await migrateNextBatch(schemaType).catch(err => {
            console.error(err)
            process.exit(1)
        })
    }

    if (unreferencedDocuments.length > 0) {
        fs.writeFileSync('unreferencedDocuments.json', JSON.stringify(unreferencedDocuments, null, 2));
    }
}

migrate();
