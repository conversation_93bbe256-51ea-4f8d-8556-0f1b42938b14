/* eslint-disable */
import client from '@sanity/base/lib/client/index.js';
import _ from 'lodash';

/*
    Run this script with: `sanity exec --with-user-token scripts/patchFields.mjs`
    This example shows how you may write a migration script that patches any given field
    on a specific document type.
    This will migrate documents in batches of 100 and continue patching until no more documents are
    returned from the query.
    This script can safely be run, even if documents are being concurrently modified by others.
    If a document gets modified in the time between fetch => submit patch, this script will fail,
    but can safely be re-run multiple times until it eventually runs out of documents to migrate.

    A few things to note:
    - This script will exit if any of the mutations fail due to a revision mismatch (which means the
      document was edited between fetch => update)
    - The query must eventually return an empty set, or else this script will continue indefinitely
*/

const documentType = 'site-config';
const fieldPaths = [
    'i18n.en.mainNavigation.contactLink',
    'i18n.de.mainNavigation.contactLink',
    'i18n.es.mainNavigation.contactLink',
    'i18n.nl.mainNavigation.contactLink',
    'i18n.en.mainNavigation.loginLink',
    'i18n.de.mainNavigation.loginLink',
    'i18n.es.mainNavigation.loginLink',
    'i18n.nl.mainNavigation.loginLink',
    'i18n.en.mainNavigation.demoLink',
    'i18n.de.mainNavigation.demoLink',
    'i18n.es.mainNavigation.demoLink',
    'i18n.nl.mainNavigation.demoLink',
    'i18n.en.content.privacyPolicyLink',
    'i18n.de.content.privacyPolicyLink',
    'i18n.es.content.privacyPolicyLink',
    'i18n.nl.content.privacyPolicyLink',
    'i18n.en.footer.copyright',
    'i18n.de.footer.copyright',
    'i18n.es.footer.copyright',
    'i18n.nl.footer.copyright',
    'i18n.en.footer.text',
    'i18n.de.footer.text',
    'i18n.es.footer.text',
    'i18n.nl.footer.text',
    'i18n.en.footer.privacyStatementLink',
    'i18n.de.footer.privacyStatementLink',
    'i18n.es.footer.privacyStatementLink',
    'i18n.nl.footer.privacyStatementLink',
    'i18n.en.footer.knowledgeLink',
    'i18n.de.footer.knowledgeLink',
    'i18n.es.footer.knowledgeLink',
    'i18n.nl.footer.knowledgeLink',
    'i18n.en.footer.termsAndConditionsLink',
    'i18n.de.footer.termsAndConditionsLink',
    'i18n.es.footer.termsAndConditionsLink',
    'i18n.nl.footer.termsAndConditionsLink',
    'i18n.en.footer.imprintLink',
    'i18n.de.footer.imprintLink',
    'i18n.es.footer.imprintLink',
    'i18n.nl.footer.imprintLink',
];

const fetchDocuments = fieldPath =>
    client.fetch(`*[_type == '${documentType}' && defined(${fieldPath})][0...100]`)

const buildPatches = (docs, fieldPath) =>
    docs.map(doc => ({
        id: doc._id,
        patch: {
            // if you wish, set a new value for your field, you can reference the own document here
            // so you can actually rename a field, like this:
            // set: {newNameOrPath: _.get(doc, fieldPath)}
            set: {},
            // Use this to remove a field, don't use it if you don't want
            unset: [fieldPath],
            // this will cause the migration to fail if any of the documents has been
            // modified since it was fetched.
            ifRevisionID: doc._rev
        }
    }))

const createTransaction = patches =>
    patches.reduce((tx, patch) => tx.patch(patch.id, patch.patch), client.transaction())

const commitTransaction = tx => tx.commit()

const migrateNextBatch = async (fieldPath) => {
    const documents = await fetchDocuments(fieldPath)
    const patches = buildPatches(documents, fieldPath)
    if (patches.length === 0) {
        console.log('No more documents to migrate!')
        return null
    }
    console.log(
        `Migrating batch:\n %s`,
        patches.map(patch => `${patch.id} => ${JSON.stringify(patch.patch)}`).join('\n')
    )
    const transaction = createTransaction(patches)
    await commitTransaction(transaction)
    return migrateNextBatch()
}

const migrate = async () => {
    for (const fieldPath of fieldPaths) {
        await migrateNextBatch(fieldPath).catch(err => {
            console.error(err)
            process.exit(1)
        })
    }
}

migrate();
