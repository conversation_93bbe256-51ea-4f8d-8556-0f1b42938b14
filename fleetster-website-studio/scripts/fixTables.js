/* eslint-disable */
import {getCliClient} from 'sanity/cli'
import _ from 'lodash';

const client = getCliClient();

/*
    All feature comparison tables broke after the plugin got updated in Sanity V3.

    To run this script: `sanity exec scripts/fixTables.mjs --with-user-token`
*/

const fetchDocuments = () =>
    client.fetch(`*[_type == 'page' && "featuresComparisonSection" in content[]._type]{...}`)

const migrate = async () => {
    const documents = await fetchDocuments()

    console.log(`Found ${documents.length} documents with feature comparison tables`);

    for (let doc of documents) {
        const {content} = doc;

        for (let section of content) {
            if (section.featuresTable?.rows) {
                let rows = section.featuresTable.rows;
                const maxColumnSize = _.max(rows.map(row => row.cells.length));

                rows = rows.map(row => {
                    const cells = row.cells;
                    const emptyCells = Array(maxColumnSize - cells.length).fill('');
                    return {
                        ...row,
                        cells: [...cells, ...emptyCells]
                    }
                });

                section.featuresTable.rows = rows;
            }
        }

        console.log(`Patching document ${doc._id}`);
        await client.createOrReplace(doc);
    }
}

migrate();
