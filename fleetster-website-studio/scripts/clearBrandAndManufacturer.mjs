import _ from 'lodash';
import client from '@sanity/base/lib/client/index.js';

/*

To execute on DEV:
sanity exec --with-user-token scripts/clearBrandAndManufacturer.mjs

To execute on PROD:
NODE_ENV=production sanity exec --with-user-token scripts/clearBrandAndManufacturer.mjs

 */

function fetchPages() {
    return client.fetch('*[_type == "page"]');
}

async function buildPatches() {
    let patches = [];
    const pages = await fetchPages();

    _.forEach(pages, page => {
        _.forEach(page.i18n, (data, lang) => {
            _.forEach(data.content, (section, sectionIndex) => {
                if (section._type === 'productStructuredData') {
                    let pagePatch = {
                        id:    page._id,
                        title: page.i18n[lang].title,
                        patch: {unset: []}
                    };

                    if (_.get(section, 'manufacturer.name') === 'fleetster') {
                        pagePatch.patch.unset.push(`i18n.${lang}.content[${sectionIndex}].manufacturer`);
                    }
                    if (_.get(section, 'brand.name') === 'fleetster') {
                        pagePatch.patch.unset.push(`i18n.${lang}.content[${sectionIndex}].brand`);
                    }

                    if (!_.isEmpty(pagePatch.patch.unset)) {
                        patches.push(pagePatch);
                    }
                }
            });
        });
    });

    return patches;
}

function createTransaction(patches) {
    return patches.reduce((tx, patch) => tx.patch(patch.id, patch.patch), client.transaction());
}

async function main() {
    const patches = await buildPatches();
    if (_.isEmpty(patches)) {
        console.log('Found no sections to patch');

        return;
    }

    console.log('patch', JSON.stringify(patches, null, 2));

    const transaction = createTransaction(patches);
    await transaction.commit();

    console.log(`Done. Patched ${patches.length} sections`);
}

main();
