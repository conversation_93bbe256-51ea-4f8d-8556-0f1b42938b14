/* eslint-disable */

/*
* Usage: DATASET=<development/production> node ./scripts/countAttributes.js
*
* This script will count the number of unique paths present in the dataset
* and will output a file called paths.json at the root of the project
*
* */

const _ = require('lodash');
const fetch = require('node-fetch');
const fs = require('fs-extra');

const dataset = process.env.DATASET || 'development';

function paths(obj, parentKey) {
    let result;
    if (_.isArray(obj)) {
        let idx = 0;
        result = _.flatMap(obj, function(obj) {
            return paths(obj, `${parentKey || '' }[${ idx++ }]`);
        });
    } else if (_.isPlainObject(obj)) {
        result = _.flatMap(_.keys(obj), function(key) {
            return _.map(paths(obj[key], key), function(subkey) {
                return (parentKey ? `${parentKey }.` : '') + subkey;
            });
        });
    } else {
        result = [];
    }

    return _.concat(result, parentKey || []);
}

let attributeMap = {};

fetch(`https://dp11egz7.api.sanity.io/v1/data/export/${dataset}`).then(resp => resp.text()).then(data => {
    const jsonText = `[${data.replace(/\}[\s\r\t]+\{/g, '},{')}]`;
    const json = JSON.parse(jsonText);

    _.forEach(json, document => {
        const attrs = paths(document);
        _.forEach(attrs, attr => {
            const attrNoArrayPath = attr.replace(/\[\d+\]/g, '');
            const type = _.get(document, attr)?._type || typeof _.get(document, attr);
            const attrPlusType = `${attrNoArrayPath}.${type}`;

            attributeMap[attrPlusType] = true;
        });
    });

    console.log(`Number of unique paths: ${_.keys(attributeMap).length}`);
    fs.writeFile('paths.json', JSON.stringify(_.keys(attributeMap).sort(),null, 4)).then(() => {
        console.log("Paths list written to file: ./paths.json");
    });
});
