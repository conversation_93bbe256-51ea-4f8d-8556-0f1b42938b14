import {getCliClient} from 'sanity/cli';
import _ from 'lodash';

const client = getCliClient();

/*

To execute on DEV:
sanity exec scripts/clearNonAmpSections.js --with-user-token

To execute on PROD:
NODE_ENV=production sanity exec scripts/clearNonAmpSections.js --with-user-token

 */

function fetchPages() {
    return client.fetch('*[_type == "page"]');
}

async function buildPatches() {
    let patches = [];
    const pages = await fetchPages();

    _.forEach(pages, page => {
        _.forEach(page.content, (section, sectionIndex) => {
            if (section._type === 'nonAMPSection') {
                patches.push({
                    id:    page._id,
                    patch: {unset: [`content[${sectionIndex}]`]}
                });
            }
        });
    });

    return patches;
}

function createTransaction(patches) {
    return patches.reduce((tx, patch) => tx.patch(patch.id, patch.patch), client.transaction());
}

async function main() {
    const patches = await buildPatches();
    if (_.isEmpty(patches)) {
        console.log('Found no sections to patch');

        return;
    }

    console.log('patch', JSON.stringify(patches, null, 2));

    const transaction = createTransaction(patches);
    await transaction.commit();

    console.log(`Done. Patched ${patches.length} sections`);
}

main();
