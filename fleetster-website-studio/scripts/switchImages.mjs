/* eslint-disable */
import sanityClient from '@sanity/base/lib/client/index.js';
import _ from 'lodash';

const client = sanityClient.withConfig({apiVersion: '2023-05-03'});

/*
    To run this script: `sanity exec --with-user-token scripts/switchImages.mjs -- <originalImageRef> <newImageRef>`
*/

const originalImageRef = process.argv[2];
const newImageRef = process.argv[3];

async function validateArgs() {
    if (!originalImageRef) {
        throw new Error('Missing originalImageRef');
    }

    if (!newImageRef) {
        throw new Error('Missing newImageRef');
    }

    if (!originalImageRef.startsWith('image-')) {
        throw new Error('Invalid original imageRef: ' + originalImageRef);
    }

    if (!newImageRef.startsWith('image-')) {
        throw new Error('Invalid new image imageRef: ' + newImageRef);
    }

    if (originalImageRef === newImageRef) {
        throw new Error('originalImageRef and newImageRef are the same');
    }

    const originalImageObject = await client.fetch(`*[_id == "${originalImageRef}"]`);

    if (_.isEmpty(originalImageObject)) {
        throw new Error('Could not find original image object in this dataset. Image ID: ' + originalImageRef);
    }

    const newImageObject = await client.fetch(`*[_id == "${newImageRef}"]`);

    if (_.isEmpty(newImageObject)) {
        throw new Error('Could not find new image object in this dataset. Image ID: ' + newImageRef);
    }
}

const fetchDocuments = () => client.fetch('*');

function paths(obj, parentKey) {
    let result;
    if (_.isArray(obj)) {
        let idx = 0;
        result = _.flatMap(obj, function(obj) {
            return paths(obj, `${parentKey || '' }[${ idx++ }]`);
        });
    } else if (_.isPlainObject(obj)) {
        result = _.flatMap(_.keys(obj), function(key) {
            return _.map(paths(obj[key], key), function(subkey) {
                return (parentKey ? `${parentKey }.` : '') + subkey;
            });
        });
    } else {
        result = [];
    }

    return _.concat(result, parentKey || []);
}

const buildPatches = documents =>
    documents.map(document => {
        let setPatch = {};

        const objPaths = paths(document);
        _.forEach(objPaths, path => {
            const type = _.get(document, `${path}._type`);
            if (type === 'image' && _.get(document, `${path}.asset._ref`) === originalImageRef) {
                setPatch[`${path}.asset._ref`] = newImageRef;
            }
        });

        if (_.isEmpty(setPatch)) {
            return null;
        }

        return {
            id: document._id,
            patch: {
                set: setPatch,
                ifRevisionID: document._rev
            }
        }
    })

const createTransaction = patches =>
    patches.reduce((tx, patch) => tx.patch(patch.id, patch.patch), client.transaction())

const commitTransaction = tx => tx.commit()

const migrate = async () => {
    await validateArgs();

    const documents = await fetchDocuments();
    const patches = _.filter(buildPatches(documents))
    console.log(
        `Migrating batch:\n %s`,
        patches.map(patch => `${patch.id} => ${JSON.stringify(patch.patch)}`).join('\n')
    )

    const transaction = createTransaction(patches)

    let deleteOriginalImage = false;

    const readline = (await import ('readline')).createInterface({
        input: process.stdin,
        output: process.stdout
    });

    await new Promise(resolve => readline.question('Do you want to delete the original image? (y/N): ', async answer => {
        if (answer.trim().toLowerCase() === 'y') {
            deleteOriginalImage = true;
        }

        resolve();
    }));

    readline.close();

    if (patches.length === 0 && !deleteOriginalImage) {
        console.log('\nNo migrations to execute.');
        return;
    }

    await commitTransaction(transaction);

    if (deleteOriginalImage) {
        console.log(`Deleting original image: ${originalImageRef}`);
        const deleteTransaction = client.transaction();
        deleteTransaction.delete(originalImageRef);
        await deleteTransaction.commit();
    }
}

migrate();
