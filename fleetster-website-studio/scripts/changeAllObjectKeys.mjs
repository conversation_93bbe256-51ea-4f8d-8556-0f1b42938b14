/* eslint-disable */
import sanityClient from '@sanity/base/lib/client/index.js';
import { customAlphabet } from 'nanoid'
import _ from 'lodash';

const nanoid = customAlphabet('1234567890abcdefghijklmnopqrstuvwxyz', 20)

const client = sanityClient.withConfig({apiVersion: '2021-06-07'});

const fetchDocuments = () => client.fetch('*');

function paths(obj, parentKey) {
    let result;
    if (_.isArray(obj)) {
        let idx = 0;
        result = _.flatMap(obj, function(obj) {
            return paths(obj, `${parentKey || '' }[${ idx++ }]`);
        });
    } else if (_.isPlainObject(obj)) {
        result = _.flatMap(_.keys(obj), function(key) {
            return _.map(paths(obj[key], key), function(subkey) {
                return (parentKey ? `${parentKey }.` : '') + subkey;
            });
        });
    } else {
        result = [];
    }

    return _.concat(result, parentKey || []);
}

const buildPatches = docs =>
    docs.map(doc => {
        let setPatch = {};

        const objPaths = paths(doc);
        _.forEach(objPaths, path => {
            if (path.includes('_key') && typeof _.get(doc, path) === 'string') {
                setPatch[path] = nanoid();
            }
        });

        if (_.isEmpty(setPatch)) {
            return null;
        }

        return {
            id: doc._id,
            patch: {
                set: setPatch,
                ifRevisionID: doc._rev
            }
        }
    })

const createTransaction = patches =>
    patches.reduce((tx, patch) => tx.patch(patch.id, patch.patch), client.transaction())

const commitTransaction = tx => tx.commit()

const migrate = async () => {
    const documents = await fetchDocuments()
    const patches = _.filter(buildPatches(documents))
    console.log(
        `Migrating batch:\n %s`,
        patches.map(patch => `${patch.id} => ${JSON.stringify(patch.patch)}`).join('\n')
    )
    const transaction = createTransaction(patches)
    await commitTransaction(transaction)
}

migrate();
