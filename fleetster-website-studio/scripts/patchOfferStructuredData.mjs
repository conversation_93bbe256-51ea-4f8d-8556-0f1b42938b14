/* eslint-disable max-statements */
import _ from 'lodash';
import client from '@sanity/base/lib/client/index.js';
import {nanoid} from 'nanoid';

/*
To execute on DEV:
sanity exec --with-user-token scripts/patchOfferStructuredData.mjs

To execute on PROD:
NODE_ENV=production sanity exec --with-user-token scripts/patchOfferStructuredData.mjs
 */

function fetchPages() {
    return client.fetch('*[_type == "page" && !(_id in path("drafts.**"))]');
}

async function buildPatches() {
    let patches = [];
    const pages = await fetchPages();

    _.forEach(pages, page => {
        _.forEach(page.i18n, (data, lang) => {
            _.forEach(data.content, (section, sectionIndex) => {
                if (section._type === 'productStructuredData') {
                    let pagePatch = {
                        id:    page._id,
                        title: page.i18n[lang].title,
                        patch: {unset: []}
                    };

                    const priceField = _.get(section, 'offers.price');
                    const priceCurrencyField = _.get(section, 'offers.priceCurrency');

                    const offerPrices = _.get(section, 'offers.offers');

                    if (_.isEmpty(offerPrices)) {
                        if (!_.isEmpty(priceField)) {
                            pagePatch.patch.unset.push(`i18n.${lang}.content[${sectionIndex}].offers.price`);
                        }

                        if (!_.isEmpty(priceCurrencyField)) {
                            pagePatch.patch.unset.push(`i18n.${lang}.content[${sectionIndex}].offers.priceCurrency`);
                        }

                        _.set(pagePatch, 'patch.set', {
                            [`i18n.${lang}.content[${sectionIndex}].offers.offers`]: [
                                {
                                    price:         priceField,
                                    priceCurrency: priceCurrencyField,
                                    _key:          nanoid(12),
                                    _type:         'offerChildStructuredData'
                                }
                            ]});
                    }

                    if (!_.isEmpty(pagePatch.patch.unset || !_.isEmpty(pagePatch.patch.set))) {
                        patches.push(pagePatch);
                    }
                }
            });
        });
    });

    return patches;
}

function createTransaction(patches) {
    return patches.reduce((tx, patch) => tx.patch(patch.id, patch.patch), client.transaction());
}

async function main() {
    const patches = await buildPatches();
    if (_.isEmpty(patches)) {
        console.log('Found no sections to patch');

        return;
    }

    console.log('patch', JSON.stringify(patches, null, 2));

    const transaction = createTransaction(patches);
    await transaction.commit();

    console.log(`Done. Patched ${patches.length} sections`);
}

main();
