/* eslint-disable */

/*
    Migrates translations from the `i18n` field to document-level translations.

    Run this script with: `sanity exec scripts/migrateTranslations.mjs --with-user-token`
*/

import {getCliClient} from 'sanity/cli'
import {uuid} from '@sanity/uuid'
import _ from 'lodash';

const client = getCliClient();

const languagePriority = ['en', 'de', 'es', 'nl']

async function migrateDocument(document) {
    const languageContents = _(languagePriority).map(lang => ({language: lang, content: _.get(document, `i18n.${lang}`)})).filter(({content}) => content).value();
    const mainLanguage = languageContents.shift();

    const commonDocumentProps = _.omit(document, ['_id', '_rev', 'i18n']);

    const mainDocument = {
        _id: document._id,
        ...commonDocumentProps,
        ..._.omit(mainLanguage.content, ['_type']),
        language: mainLanguage.language,
    }

    const translations = languageContents.map(({language, content}) => ({
        _id: uuid(),
        ...commonDocumentProps,
        ..._.omit(content, ['_type']),
        language,
    }));

    const documents = [mainDocument, ...translations];

    // Create a separate metadata document to join the translations together
    const metadata = {
        _id: uuid(),
        _type: 'translation.metadata',
        translations: documents.map((doc) => ({
            _key: doc.language,
            value: {
                _type: 'reference',
                _ref: doc._id
            }
        })),
        // Optional, used to filter references in the `translations` field
        schemaTypes: [document._type],
    }

    // Perform all document creation in a single transaction
    const transaction = client.transaction()

    documents.forEach((doc) => {
        transaction.createOrReplace(doc)
    })
    transaction.createOrReplace(metadata)

    await transaction.commit().then((res) => {
        console.log(`Translations created successfully for document ${document._id} of type ${document._type}`)
    }).catch((err) => {
        console.error(`Failed to create translations for document ${document._id} of type ${document._type}`, err)
    });
}

const fetchDocuments = (schemaType) =>
    client.fetch(`*[_type == "${schemaType}" && !defined(language)][0...100]`)

const migrateNextBatch = async (schemaType) => {
    const documents = await fetchDocuments(schemaType)

    if (documents.length === 0) {
        return;
    }

    for (const document of documents) {
        await migrateDocument(document)
    }

    return migrateNextBatch(schemaType)
}

const schemaTypes = ['page', 'route', 'redirect', 'disallow-robots', 'changelog', 'site-config'];

const migrate = async () => {
    for (const schemaType of schemaTypes) {
        await migrateNextBatch(schemaType).catch(err => {
            console.error(err)
            process.exit(1)
        })
    }
}

migrate();
