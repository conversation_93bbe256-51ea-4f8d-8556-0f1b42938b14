import {MdSettings} from 'react-icons/md';

import {
    changelogsMenuItem, routesMenuItem, getPagesMenuItem,
    redirectsMenuItem, disallowRobotsMenuItem, translationsMenuItem
} from './deskMenus';

const siteConfigMenuItem = S => S.listItem()
    .title('Site config')
    .icon(MdSettings)
    .child(
        S.editor()
            .id('config')
            .schemaType('site-config')
            .documentId('global-config')
    );

export default async(S, context) => {
    const client = context.getClient({apiVersion: '2024-01-01', dataset: context.dataset, projectId: context.projectId});

    return S.list()
        .title('Site')
        .items([
            siteConfigMenuItem(S),
            await getPagesMenuItem(S, client),
            routesMenuItem(S),
            redirectsMenuItem(S),
            changelogsMenuItem(S),
            disallowRobotsMenuItem(S),
            translationsMenuItem(S, client)
        ]);
};
