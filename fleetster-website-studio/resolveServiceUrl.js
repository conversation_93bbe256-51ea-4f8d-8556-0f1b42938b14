import {config} from './config';

function generateUrlForService(serviceConfig) {
    const {proto, host, port} = serviceConfig;

    if (!host) {
        return '';
    }

    return `${proto}://${host}${port ? `:${port}` : ''}`;
}

export default function resolveServiceUrl(resource) {
    if (resource._type !== 'page') {
        return null;
    }

    const {services: {websitePreview}} = config;

    return `${generateUrlForService(websitePreview)}/${websitePreview.previewPath}/${resource._id}`;
}
