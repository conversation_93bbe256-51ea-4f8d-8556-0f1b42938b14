import {defineConfig} from 'sanity';
import {getPlugins} from './plugins';
import {getDocumentConfig} from './documentConfig';
import schemas from './schemas/schema';
import './components/tagInput/styles.css';

const datasets = process.env.NODE_ENV === 'production'
    ? ['production', 'development']
    : ['development', 'production'];

const disallowedTemplateTypes = ['site-config', 'media.tag'];

const config = {
    title:     'fleetster Website Studio',
    projectId: process.env.SANITY_STUDIO_PROJECT_ID,
    plugins:   getPlugins(),
    schema:    {
        types:     schemas,
        templates: prev => prev
            .filter(template => {
                const isEnglishTemplate = template.id.includes('-en');
                const isDisallowedTemplate = disallowedTemplateTypes.includes(template.schemaType);

                return isEnglishTemplate && !isDisallowedTemplate;
            })
            .map(template => ({
                ...template,
                title: template.title.replace('English ', '')
            }))
    },
    document: getDocumentConfig()
};

export default defineConfig(datasets.map(dataset => ({
    ...config,
    dataset,
    title:    `${config.title} (${dataset})`,
    name:     dataset,
    basePath: `/${dataset}`
})));
