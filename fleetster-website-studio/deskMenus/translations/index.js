import {MdNewReleases, MdTranslate} from 'react-icons/md';
import _ from 'lodash';

import {languages} from '../../schemas/documents/languages';
import {changelogItemMapper, pageItemMapper, translationsFilteredItems} from './utils';

export const translationsMenuItem = (S, client) => S.listItem()
    .title('Translations Central')
    .icon(MdTranslate)
    .child(
        S.list()
            .title('Filters')
            .items([
                S.listItem()
                    .icon(MdTranslate)
                    .title('Page Translations')
                    .child(S.list()
                        .title('Page Translations Filters')
                        .items(
                            [
                                S.listItem()
                                    .title('All Pages')
                                    .icon(MdTranslate)
                                    .schemaType('page')
                                    .child(
                                        translationsFilteredItems(
                                            {S, client, schemaType: 'page'},
                                            pageItemMapper(S)
                                        )
                                    ),
                                ..._.map(languages, lang => S.listItem()
                                    .title(`Missing ${lang.title} Translations`)
                                    .icon(MdTranslate)
                                    .schemaType('page')
                                    .child(
                                        translationsFilteredItems(
                                            {S, client, schemaType: 'page', filter: `!("${lang.name}" in translations[]._key)`},
                                            pageItemMapper(S)
                                        )
                                    ))
                            ]
                        )),
                S.listItem()
                    .icon(MdTranslate)
                    .title('Changelog Translations')
                    .child(S.list()
                        .title('Changelog Translations Filters')
                        .items(
                            [
                                S.listItem()
                                    .title('All Changelogs')
                                    .icon(MdNewReleases)
                                    .schemaType('changelog')
                                    .child(
                                        translationsFilteredItems(
                                            {S, client, schemaType: 'changelog'},
                                            changelogItemMapper(S)
                                        )
                                    ),
                                ..._.map(languages, lang => S.listItem()
                                    .title(`Missing ${lang.title} Translations`)
                                    .icon(MdNewReleases)
                                    .schemaType('changelog')
                                    .child(
                                        translationsFilteredItems(
                                            {S, client, schemaType: 'changelog', filter: `!("${lang.name}" in translations[]._key)`},
                                            changelogItemMapper(S)
                                        )
                                    ))
                            ]
                        ))
            ])
    );
