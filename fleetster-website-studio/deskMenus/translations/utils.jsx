import _ from 'lodash';
import {uuid} from '@sanity/uuid';
import imageUrlBuilder from '@sanity/image-url';
import React from 'react';
import {MdNewReleases} from 'react-icons/md';

export function translationsFilteredItems({S, client, schemaType, filter}, itemMapper) {
    const filterString = filter ? ` && ${filter}` : '';

    return async() => {
        const translationMetadata = await client.fetch(`*[_type == "translation.metadata" && $schemaType in schemaTypes ${filterString}]{
                                            _id,
                                            "translations": translations[] {_key, value->}
                                        }`, {schemaType});

        const items = _.map(translationMetadata, itemMapper);

        return S.list().id(uuid()).items(items);
    };
}

export function pageItemMapper(S) {
    return translationMetadata => {
        const title = _(translationMetadata?.translations).map(translation => translation?.value?.title).compact().first();
        const openGraphImage = _(translationMetadata?.translations).map(translation => translation?.value?.openGraphImage).compact().first();

        let imageUrl = '';
        if (openGraphImage) {
            imageUrl = imageUrlBuilder(S.context)
                .image(openGraphImage)
                .width(33)
                .height(33)
                .fit('crop')
                .url();
        }

        return S.documentListItem()
            .id(translationMetadata._id)
            .title(title)
            .icon(() => <img src={imageUrl} alt={''}/>)
            .schemaType('translation.metadata')
            .child(S.document(translationMetadata._id).schemaType('translation.metadata'));
    };
}

export function changelogItemMapper(S) {
    return translationMetadata => {
        const title = _(translationMetadata?.translations).map(translation => translation?.value?.title).compact().first();

        return S.documentListItem()
            .id(translationMetadata._id)
            .title(title)
            .icon(MdNewReleases)
            .schemaType('translation.metadata')
            .child(S.document(translationMetadata._id).schemaType('translation.metadata'));
    };
}
