import {BiLink} from 'react-icons/bi';
import _ from 'lodash';
import {MdTranslate} from 'react-icons/md';

import {languages} from '../schemas/documents/languages';

export const routesMenuItem = S => S.listItem()
    .title('Routes')
    .icon(BiLink)
    .child(
        S.list()
            .title('Filters')
            .items([
                S.listItem()
                    .title('All Routes')
                    .schemaType('route')
                    .child(S.documentTypeList('route')
                        .title('Routes')),
                ..._.map(languages, lang => S.listItem()
                    .title(`${lang.title} routes`)
                    .icon(MdTranslate)
                    .child(S.documentTypeList('route')
                        .title('Routes')
                        .filter(`_type == "route" && language == "${lang.name}"`))),
                S.listItem()
                    .title('Not in Sitemap')
                    .schemaType('route')
                    .child(S.documentTypeList('route')
                        .title('Routes').filter('includeInSitemap == false')),
                S.listItem()
                    .title('No Robots')
                    .schemaType('route')
                    .child(S.documentTypeList('route')
                        .title('Routes').filter('disallowRobots == true')),
                S.listItem()
                    .title('Included in News')
                    .schemaType('route')
                    .child(S.documentTypeList('route')
                        .title('Routes').filter('includeInNews == true'))
            ])
    );
