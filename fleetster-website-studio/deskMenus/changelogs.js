import {MdNewReleases, MdTranslate} from 'react-icons/md';
import _ from 'lodash';

import {languages} from '../schemas/documents/languages';
import {defaultChangelogOrdering} from './constants';

export const changelogsMenuItem = S => S.listItem()
    .title('Changelogs')
    .icon(MdNewReleases)
    .child(
        S.list()
            .title('Filters')
            .items([
                S.listItem()
                    .title('All Changelogs')
                    .schemaType('changelog')
                    .child(S.documentTypeList('changelog')
                        .title('Changelogs')
                        .defaultOrdering(defaultChangelogOrdering)),
                ..._.map(languages, lang => S.listItem()
                    .title(`${lang.title} changelogs`)
                    .icon(MdTranslate)
                    .child(S.documentTypeList('changelog')
                        .title('Changelogs')
                        .filter(`_type == "changelog" && language == "${lang.name}"`)
                        .defaultOrdering(defaultChangelogOrdering)))
            ])
    );
