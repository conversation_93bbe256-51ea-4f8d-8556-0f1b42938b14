import {BiBot} from 'react-icons/bi';
import {MdTranslate} from 'react-icons/md';
import _ from 'lodash';

import {languages} from '../schemas/documents/languages';

export const disallowRobotsMenuItem = S => S.listItem()
    .title('Disallow Robots')
    .icon(BiBot)
    .child(
        S.list()
            .title('Filters')
            .items([
                S.listItem()
                    .title('All Disallow Robots')
                    .schemaType('disallow-robots')
                    .child(S.documentTypeList('disallow-robots')
                        .title('DisallowRobots')),
                ..._.map(languages, lang => S.listItem()
                    .title(`${lang.title} Disallow Robots`)
                    .icon(MdTranslate)
                    .child(S.documentTypeList('disallow-robots')
                        .title('DisallowRobots')
                        .filter(`_type == "disallow-robots" && language == "${lang.name}"`)))
            ])
    );
