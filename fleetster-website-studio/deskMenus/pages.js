import {MdDashboard, MdTranslate} from 'react-icons/md';
import {BiLink} from 'react-icons/bi';
import _ from 'lodash';

import {languages} from '../schemas/documents/languages';

function buildPagesMenuItem(S, pagesWithoutRouteIds) {
    return S.listItem()
        .title('Pages')
        .icon(MdDashboard)
        .child(
            S.list()
                .title('Filters')
                .items([
                    S.listItem()
                        .title('All Pages')
                        .icon(MdDashboard)
                        .schemaType('page')
                        .child(S.documentTypeList('page')
                            .title('Pages')),

                    ..._.map(languages, lang => S.listItem()
                        .title(`${lang.title} pages`)
                        .icon(MdTranslate)
                        .child(S.documentTypeList('page')
                            .title('Pages')
                            .filter(`_type == "page" && language == "${lang.name}"`))),
                    S.listItem()
                        .title('Not linked to any Route')
                        .schemaType('page')
                        .icon(BiLink)
                        .child(S.documentList()
                            .title('Pages')
                            .filter(`_id in [${pagesWithoutRouteIds}]`))
                ])
        );
}

async function fetchRoutePageIds(client) {
    const routes = await client.fetch('*[_type == "route"]{page}');

    return _.map(routes, route => route.page?._ref);
}

async function fetchPageIdsWithoutRoute(client) {
    const routePageIds = await fetchRoutePageIds(client);

    const pages = await client.fetch('*[_type == "page"]{_id}');
    const pagesWithoutRoute = _.reject(pages, page => _.includes(routePageIds, page._id));

    return _.map(pagesWithoutRoute, page => `"${page._id}"`);
}

export async function getPagesMenuItem(S, client) {
    const pageIdsWithoutRoute = await fetchPageIdsWithoutRoute(client);

    return buildPagesMenuItem(S, pageIdsWithoutRoute);
}
