import {BiUnlink} from 'react-icons/bi';
import {MdTranslate} from 'react-icons/md';
import _ from 'lodash';

import {languages} from '../schemas/documents/languages';

export const redirectsMenuItem = S => S.listItem()
    .title('Redirects')
    .icon(BiUnlink)
    .child(
        S.list()
            .title('Filters')
            .items([
                S.listItem()
                    .title('All Redirects')
                    .schemaType('redirect')
                    .child(S.documentTypeList('redirect')
                        .title('Redirects')),
                ..._.map(languages, lang => S.listItem()
                    .title(`${lang.title} redirects`)
                    .icon(MdTranslate)
                    .child(S.documentTypeList('redirect')
                        .title('Redirects')
                        .filter(`_type == "redirect" && language == "${lang.name}"`)))
            ])
    );
