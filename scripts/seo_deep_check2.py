#!/usr/bin/env python3
import argparse
import re
import sys
import urllib.error
import urllib.parse
import urllib.request
import xml.etree.ElementTree as ET
from collections import defaultdict

BASE_HOST = "www.fleetster.de"
BASE_URL = f"https://{BASE_HOST}/"
USER_AGENT = "AugmentSEOAuditBot/1.0 (+https://augmentcode.ai)"
TIMEOUT = 12

class RedirectTrackingHandler(urllib.request.HTTPRedirectHandler):
    def __init__(self):
        super().__init__()
        self.redirect_chain = []
    def redirect_request(self, req, fp, code, msg, headers, newurl):
        self.redirect_chain.append((req.full_url, code, newurl))
        return super().redirect_request(req, fp, code, msg, headers, newurl)

def fetch(url: str):
    handler = RedirectTrackingHandler()
    opener = urllib.request.build_opener(handler)
    req = urllib.request.Request(url, headers={
        "User-Agent": USER_AGENT,
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    })
    try:
        resp = opener.open(req, timeout=TIMEOUT)
        body = resp.read()
        return {
            "final_url": resp.geturl(),
            "status": resp.getcode(),
            "headers": dict(resp.info()),
            "body": body,
            "redirects": list(handler.redirect_chain),
            "error": None,
        }
    except urllib.error.HTTPError as e:
        return {
            "final_url": url,
            "status": e.code,
            "headers": dict(e.headers or {}),
            "body": e.read() if hasattr(e, 'read') else b"",
            "redirects": list(handler.redirect_chain),
            "error": str(e),
        }
    except Exception as e:
        return {"final_url": url, "status": None, "headers": {}, "body": b"", "redirects": list(handler.redirect_chain), "error": str(e)}


def parse_html_meta(html: bytes, max_links_per_page: int):
    text = html.decode("utf-8", errors="ignore")
    title = None
    m = re.search(r"<title[^>]*>(.*?)</title>", text, re.IGNORECASE | re.DOTALL)
    if m:
        title = re.sub(r"\s+", " ", m.group(1)).strip()

    canonical = None
    m = re.search(r"<link[^>]+rel=\"?canonical\"?[^>]*href=\"([^\"]+)\"", text, re.IGNORECASE)
    if m:
        canonical = m.group(1).strip()

    description = None
    m = re.search(r"<meta[^>]+name=\"description\"[^>]*content=\"([^\"]*)\"", text, re.IGNORECASE)
    if m:
        description = m.group(1)

    robots = None
    m = re.search(r"<meta[^>]+name=\"robots\"[^>]*content=\"([^\"]*)\"", text, re.IGNORECASE)
    if m:
        robots = m.group(1)

    hreflangs = re.findall(r"<link[^>]+rel=\"alternate\"[^>]*hreflang=\"([^\"]+)\"[^>]*href=\"([^\"]+)\"", text, re.IGNORECASE)

    # Extract a few internal links
    links = re.findall(r"<a\s+[^>]*href=\"([^\"]+)\"", text, re.IGNORECASE)
    internal = []
    for href in links:
        href = href.strip()
        if href.startswith("#"):
            continue
        if href.startswith("mailto:") or href.startswith("tel:"):
            continue
        if href.startswith("/"):
            internal.append(urllib.parse.urljoin(BASE_URL, href))
        else:
            try:
                p = urllib.parse.urlparse(href)
                if p.scheme in ("http", "https") and p.netloc == BASE_HOST:
                    internal.append(href)
            except Exception:
                pass
        if len(internal) >= max_links_per_page:
            break

    # Extract asset URLs to detect mixed-content
    scripts = re.findall(r"<script[^>]+src=\"([^\"]+)\"", text, re.IGNORECASE)
    imgs = re.findall(r"<img[^>]+src=\"([^\"]+)\"", text, re.IGNORECASE)
    links_href = re.findall(r"<link[^>]+href=\"([^\"]+)\"", text, re.IGNORECASE)
    assets = scripts + imgs + links_href
    http_assets = [a for a in assets if a.strip().lower().startswith("http://")]

    return {
        "title": title,
        "canonical": canonical,
        "description": description,
        "robots": robots,
        "hreflangs": hreflangs,
        "internal_links": internal,
        "http_assets": http_assets,
    }


def load_sitemap(url):
    info = fetch(url)
    urls = []
    if info.get("status") == 200 and info.get("body"):
        try:
            root = ET.fromstring(info["body"])  # urlset only (no index handling)
            ns = {"sm": "http://www.sitemaps.org/schemas/sitemap/0.9"}
            for loc in root.findall(".//sm:url/sm:loc", ns):
                u = (loc.text or "").strip()
                if u:
                    urls.append(u)
        except Exception:
            pass
    return urls, info


def normalize_trailing(url):
    try:
        p = urllib.parse.urlparse(url)
        if p.netloc and p.path:
            if not p.path.split("/")[-1]:
                return url  # already ends with '/'
            if "." in p.path.split("/")[-1]:
                return url  # file-like
            new = p._replace(path=p.path + "/")
            return urllib.parse.urlunparse(new)
    except Exception:
        pass
    return url


def analyze_urls(urls, limit, links_per_page, sample_hreflang=2):
    results = {
        "errors": [],
        "mismatched_canonicals": [],
        "trailing_only_mismatch": [],
        "missing_canonical": [],
        "noindex_in_sitemap": [],
        "hreflang_stats": defaultdict(int),
        "broken_internal_links": [],
        "mixed_content": [],
        "broken_hreflang": [],
    }
    checked = 0
    for u in urls[:limit]:
        r = fetch(u)
        meta = parse_html_meta(r.get("body", b""), links_per_page)
        status = r.get("status")
        final = r.get("final_url")
        canonical = meta.get("canonical")
        robots = (meta.get("robots") or "").lower()
        if not status or status >= 400:
            results["errors"].append((u, status, r.get("error")))
        # Canonical comparison and presence
        if canonical:
            expected = normalize_trailing(final)
            if canonical.rstrip("/") != expected.rstrip("/"):
                results["mismatched_canonicals"].append((u, canonical, final))
            elif canonical != expected:
                # only trailing slash difference
                results["trailing_only_mismatch"].append((u, canonical, expected))
        else:
            results["missing_canonical"].append(u)
        # Noindex pages shouldn't be in sitemap
        if "noindex" in robots:
            results["noindex_in_sitemap"].append(u)
        # Hreflang counts and sample validation
        for (hl, href) in meta.get("hreflangs", [])[:sample_hreflang]:
            results["hreflang_stats"][hl] += 1
            hr = fetch(href)
            if not hr.get("status") or hr.get("status") >= 400:
                results["broken_hreflang"].append((u, hl, href, hr.get("status")))
        # Shallow internal link check (limit)
        for link in meta.get("internal_links", [])[:links_per_page]:
            lr = fetch(link)
            if not lr.get("status") or lr.get("status") >= 400:
                results["broken_internal_links"].append((u, link, lr.get("status")))
        # Mixed content detection
        for a in meta.get("http_assets", [])[:10]:
            results["mixed_content"].append((u, a))
        checked += 1
    results["checked_count"] = checked
    return results


def main():
    ap = argparse.ArgumentParser()
    ap.add_argument("--limit", type=int, default=120)
    ap.add_argument("--links-per-page", type=int, default=8)
    ap.add_argument("--sample-hreflang", type=int, default=2)
    args = ap.parse_args()

    sm_urls, sm_info = load_sitemap(urllib.parse.urljoin(BASE_URL, "sitemap.xml"))
    if sm_info.get("status") != 200:
        print("Failed to load sitemap.xml", sm_info.get("status"))
        return 2
    res = analyze_urls(
        sm_urls,
        limit=args.limit,
        links_per_page=args.links_per_page,
        sample_hreflang=args.sample_hreflang,
    )

    print(f"Checked {res['checked_count']} URLs from sitemap (of {len(sm_urls)} total)")
    if res['errors']:
        print(f"Pages with HTTP errors: {len(res['errors'])}")
        for (u, s, e) in res['errors'][:20]:
            print(f" - {u} -> {s} {e}")
    else:
        print("No HTTP errors detected in sampled pages")

    if res['missing_canonical']:
        print(f"Pages missing canonical: {len(res['missing_canonical'])}")
        for u in res['missing_canonical'][:20]:
            print(f" - {u}")
    else:
        print("No pages missing canonical in sample")

    if res['mismatched_canonicals'] or res['trailing_only_mismatch']:
        print(f"Canonical mismatches (excluding pure trailing-slash): {len(res['mismatched_canonicals'])}")
        for (u, c, f) in res['mismatched_canonicals'][:20]:
            print(f" - page={u}\n   canonical={c}\n   final={f}")
        print(f"Trailing-slash-only canonical mismatches: {len(res['trailing_only_mismatch'])}")
        for (u, c, exp) in res['trailing_only_mismatch'][:20]:
            print(f" - page={u}\n   canonical={c}\n   expected={exp}")
    else:
        print("No canonical mismatches detected in sampled pages")

    if res['noindex_in_sitemap']:
        print(f"Sitemap contains noindex pages: {len(res['noindex_in_sitemap'])}")
        for u in res['noindex_in_sitemap'][:20]:
            print(f" - {u}")
    else:
        print("No noindex pages found in sitemap sample")

    if res['broken_internal_links']:
        print(f"Broken internal links found: {len(res['broken_internal_links'])}")
        for (src, link, s) in res['broken_internal_links'][:30]:
            print(f" - on {src} -> {link} status={s}")
    else:
        print("No broken internal links in the shallow sample")

    if res['mixed_content']:
        print(f"Mixed-content asset references (http on https): {len(res['mixed_content'])}")
        for (page, asset) in res['mixed_content'][:30]:
            print(f" - on {page} -> {asset}")
    else:
        print("No mixed-content asset references found in sample")

    if res['broken_hreflang']:
        print(f"Broken hreflang references: {len(res['broken_hreflang'])}")
        for (page, lang, href, st) in res['broken_hreflang'][:20]:
            print(f" - on {page} -> [{lang}] {href} status={st}")
    else:
        print("No broken hreflang references in sample")

if __name__ == "__main__":
    sys.exit(main() or 0)

