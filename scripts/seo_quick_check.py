#!/usr/bin/env python3
import re
import sys
import time
import urllib.request
import urllib.parse
import urllib.error
import xml.etree.ElementTree as ET
from http.client import HTTPResponse

BASE_URL = "https://www.fleetster.de/"
NON_WWW = "https://fleetster.de/"
HTTP_URL = "http://www.fleetster.de/"
USER_AGENT = "AugmentSEOAuditBot/1.0 (+https://augmentcode.ai)"
TIMEOUT = 20

class RedirectTrackingHandler(urllib.request.HTTPRedirectHandler):
    def __init__(self):
        super().__init__()
        self.redirect_chain = []
    def redirect_request(self, req, fp, code, msg, headers, newurl):
        self.redirect_chain.append((req.full_url, code, newurl))
        return super().redirect_request(req, fp, code, msg, headers, newurl)


def fetch(url: str, allow_redirects=True):
    handler = RedirectTrackingHandler()
    opener = urllib.request.build_opener(handler)
    req = urllib.request.Request(url, headers={"User-Agent": USER_AGENT, "Accept": "*/*"})
    try:
        resp = opener.open(req, timeout=TIMEOUT)
        body = resp.read()
        info = {
            "final_url": resp.geturl(),
            "status": resp.getcode(),
            "headers": dict(resp.info()),
            "body": body,
            "redirects": list(handler.redirect_chain),
        }
        return info
    except urllib.error.HTTPError as e:
        return {
            "final_url": url,
            "status": e.code,
            "headers": dict(e.headers or {}),
            "body": e.read() if hasattr(e, 'read') else b"",
            "redirects": list(handler.redirect_chain),
            "error": str(e),
        }
    except Exception as e:
        return {"final_url": url, "status": None, "headers": {}, "body": b"", "redirects": list(handler.redirect_chain), "error": str(e)}


def parse_html_meta(html: bytes):
    text = html.decode("utf-8", errors="ignore")
    title = None
    m = re.search(r"<title[^>]*>(.*?)</title>", text, re.IGNORECASE | re.DOTALL)
    if m:
        title = re.sub(r"\s+", " ", m.group(1)).strip()

    canonical = None
    m = re.search(r"<link[^>]+rel=\"?canonical\"?[^>]*href=\"([^\"]+)\"", text, re.IGNORECASE)
    if m:
        canonical = m.group(1)

    description = None
    m = re.search(r"<meta[^>]+name=\"description\"[^>]*content=\"([^\"]*)\"", text, re.IGNORECASE)
    if m:
        description = m.group(1)

    robots = None
    m = re.search(r"<meta[^>]+name=\"robots\"[^>]*content=\"([^\"]*)\"", text, re.IGNORECASE)
    if m:
        robots = m.group(1)

    hreflangs = re.findall(r"<link[^>]+rel=\"alternate\"[^>]*hreflang=\"([^\"]+)\"[^>]*href=\"([^\"]+)\"", text, re.IGNORECASE)

    return {
        "title": title,
        "canonical": canonical,
        "description": description,
        "robots": robots,
        "hreflangs": hreflangs,
    }


def check_redirects():
    results = {}
    for label, url in [("http->https", HTTP_URL), ("non-www", NON_WWW)]:
        info = fetch(url)
        results[label] = {
            "start_url": url,
            "final_url": info.get("final_url"),
            "status": info.get("status"),
            "redirects": info.get("redirects"),
            "error": info.get("error"),
        }
    return results


def check_homepage():
    info = fetch(BASE_URL)
    meta = parse_html_meta(info.get("body", b""))
    return {"http": info, "meta": meta}


def check_robots():
    info = fetch(urllib.parse.urljoin(BASE_URL, "robots.txt"))
    text = info.get("body", b"").decode("utf-8", errors="ignore")
    has_sitemap = any(line.lower().startswith("sitemap:") for line in text.splitlines())
    return {"http": info, "text": text, "has_sitemap": has_sitemap}


def check_sitemap_and_sample_urls(max_urls=10):
    sm_info = fetch(urllib.parse.urljoin(BASE_URL, "sitemap.xml"))
    urls = []
    if sm_info.get("status") == 200 and sm_info.get("body"):
        try:
            root = ET.fromstring(sm_info["body"])  # lazy parse
            ns = {"sm": "http://www.sitemaps.org/schemas/sitemap/0.9"}
            for url in root.findall(".//sm:url/sm:loc", ns):
                loc = (url.text or "").strip()
                if loc:
                    urls.append(loc)
                if len(urls) >= max_urls:
                    break
        except Exception as e:
            pass
    samples = []
    for u in urls:
        resp = fetch(u)
        samples.append({"url": u, "status": resp.get("status"), "final": resp.get("final_url")})
    return {"sitemap": sm_info, "sample_urls": samples, "count": len(urls)}


def check_404():
    missing = urllib.parse.urljoin(BASE_URL, f"/__missing__-{int(time.time())}-xyz.html")
    info = fetch(missing)
    meta = parse_html_meta(info.get("body", b""))
    return {"url": missing, "http": info, "meta": meta}


def main():
    report = {}
    report["redirects"] = check_redirects()
    report["homepage"] = check_homepage()
    report["robots"] = check_robots()
    report["sitemap"] = check_sitemap_and_sample_urls()
    report["not_found"] = check_404()

    # Pretty print summary
    def pr(*args):
        print(*args)

    pr("=== Redirects ===")
    for k, v in report["redirects"].items():
        pr(f"{k}: status={v['status']} final={v['final_url']} chain={v['redirects']} error={v.get('error')}")

    pr("\n=== Homepage ===")
    hp = report["homepage"]
    pr(f"status={hp['http']['status']} final={hp['http']['final_url']}")
    pr(f"title={hp['meta']['title']}")
    pr(f"canonical={hp['meta']['canonical']}")
    pr(f"description_len={len(hp['meta']['description'] or '')}")
    pr(f"hreflangs={[hl[0] for hl in hp['meta']['hreflangs']]}")

    pr("\n=== robots.txt ===")
    rb = report["robots"]
    pr(f"status={rb['http']['status']} has_sitemap={rb['has_sitemap']}")

    pr("\n=== Sitemap sample ===")
    sm = report["sitemap"]
    pr(f"status={sm['sitemap']['status']} sample_count={len(sm['sample_urls'])}")
    for s in sm['sample_urls']:
        pr(f" - {s['url']} -> {s['status']}")

    pr("\n=== 404 page ===")
    nf = report["not_found"]
    pr(f"status={nf['http']['status']} url={nf['url']}")
    pr(f"robots_meta={nf['meta']['robots']}")

if __name__ == "__main__":
    sys.exit(main() or 0)

